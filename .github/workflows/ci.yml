name: CI-WhiskerGuard AI Service

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: whiskerguard_ai_service
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install Node.js dependencies
        run: npm ci

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with <PERSON><PERSON>
        run: ./mvnw clean verify

      - name: Upload JAR file
        uses: actions/upload-artifact@v4
        with:
          name: target
          path: target/*.jar
          retention-days: 1

      #   - name: Run SonarQube Analysis
      #     env:
      #       SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #       SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      #     run: |
      #       ./mvnw -B -e -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} -Dsonar.token=${{ secrets.SONAR_TOKEN }} -Dsonar.projectKey=whiskerguard-ai-service -Dsonar.projectName="WhiskerGuard AI Service" -Dsonar.java.source=17 -Dsonar.java.target=17 -Dsonar.sources=src/main/java -Dsonar.tests=src/test/java -Dsonar.java.binaries=target/classes -Dsonar.java.test.binaries=target/test-classes -Dsonar.coverage.jacoco.xmlReportPaths=target/jacoco/test/jacocoTestReport.xml -Dsonar.junit.reportPaths=target/surefire-reports -Dsonar.jacoco.reportPaths=target/jacoco/test/jacocoTestReport.xml -Dsonar.sourceEncoding=UTF-8 org.sonarsource.scanner.maven:sonar-maven-plugin:3.10.0.2594:sonar

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            target/test-results/
            target/jacoco/

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: target/jacoco/

      - name: Build Docker image
        if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
        run: |
          # 构建本地镜像
          docker build -t whiskerguard-ai-service:latest .
          # 添加 Harbor 标签（确保 Harbor 地址格式正确）
          HARBOR_HOST=$(echo "${{ secrets.HARBOR_HOST }}" | sed 's/^https*:\/\///')
          docker tag whiskerguard-ai-service:latest ${HARBOR_HOST}/whiskerguard/whiskerguard-ai-service:latest

      - name: Login to Harbor
        if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
        run: |
          # 登录到 Harbor（确保 Harbor 地址格式正确）
          HARBOR_HOST=$(echo "${{ secrets.HARBOR_HOST }}" | sed 's/^https*:\/\///')
          echo ${{ secrets.HARBOR_PASSWORD }} | docker login ${HARBOR_HOST} -u ${{ secrets.HARBOR_USERNAME }} --password-stdin

      - name: Push Docker image
        if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
        run: |
          # 推送镜像到 Harbor（确保 Harbor 地址格式正确）
          HARBOR_HOST=$(echo "${{ secrets.HARBOR_HOST }}" | sed 's/^https*:\/\///')
          docker push ${HARBOR_HOST}/whiskerguard/whiskerguard-ai-service:latest
