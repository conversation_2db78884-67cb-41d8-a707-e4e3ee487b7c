name: CD-WhiskerGuard AI Service

on:
  workflow_run:
    workflows: ['CI-WhiskerGuard AI Service']
    types:
      - completed

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'

    steps:
      - uses: actions/checkout@v4

      - name: Download CI artifacts
        uses: actions/download-artifact@v4
        with:
          name: target
          path: target

      - name: Configure SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add host key
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Prepare deployment package
        run: |
          # 创建临时目录
          mkdir -p deploy
          # 复制必要文件
          cp target/whiskerguard-ai-service-*.jar deploy/
          cp Dockerfile deploy/
          cp src/main/docker/*.yml deploy/
          # 打包
          tar -czf deploy.tar.gz -C deploy .

      - name: Deploy to server
        run: |
          # 创建远程目录
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "mkdir -p /data/whiskerguard-ai-service"

          # 复制部署包
          scp deploy.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }}:/data/whiskerguard-ai-service/

          # 在服务器上执行部署命令
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_HOST }} "cd /data/whiskerguard-ai-service && \
            echo 'Extracting deployment package...' && \
            tar -xzf deploy.tar.gz && \
            rm deploy.tar.gz && \
            echo 'Stopping existing containers...' && \
            docker compose -f app.yml down || true && \
            echo 'Building new image...' && \
            docker build -t harbor.mbbhg.com/whiskerguard/whiskerguard-ai-service:latest . && \
            echo 'Pushing image to registry...' && \
            docker push harbor.mbbhg.com/whiskerguard/whiskerguard-ai-service:latest && \
            echo 'Starting new containers...' && \
            docker compose -f app.yml up -d && \
            echo 'Deployment completed successfully'"
