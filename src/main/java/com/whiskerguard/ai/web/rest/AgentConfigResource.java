package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AgentConfigRepository;
import com.whiskerguard.ai.service.AgentConfigService;
import com.whiskerguard.ai.service.dto.AgentConfigDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.AgentConfig}.
 */
@RestController
@RequestMapping("/api/agent-configs")
public class AgentConfigResource {

    private static final Logger LOG = LoggerFactory.getLogger(AgentConfigResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceAgentConfig";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final AgentConfigService agentConfigService;

    private final AgentConfigRepository agentConfigRepository;

    public AgentConfigResource(AgentConfigService agentConfigService, AgentConfigRepository agentConfigRepository) {
        this.agentConfigService = agentConfigService;
        this.agentConfigRepository = agentConfigRepository;
    }

    /**
     * {@code POST  /agent-configs} : Create a new agentConfig.
     *
     * @param agentConfigDTO the agentConfigDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new agentConfigDTO, or with status {@code 400 (Bad Request)} if the agentConfig has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AgentConfigDTO> createAgentConfig(@Valid @RequestBody AgentConfigDTO agentConfigDTO) throws URISyntaxException {
        LOG.debug("REST request to save AgentConfig : {}", agentConfigDTO);
        if (agentConfigDTO.getId() != null) {
            throw new BadRequestAlertException("A new agentConfig cannot already have an ID", ENTITY_NAME, "idexists");
        }
        agentConfigDTO = agentConfigService.save(agentConfigDTO);
        return ResponseEntity.created(new URI("/api/agent-configs/" + agentConfigDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, agentConfigDTO.getId().toString()))
            .body(agentConfigDTO);
    }

    /**
     * {@code PUT  /agent-configs/:id} : Updates an existing agentConfig.
     *
     * @param id the id of the agentConfigDTO to save.
     * @param agentConfigDTO the agentConfigDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentConfigDTO,
     * or with status {@code 400 (Bad Request)} if the agentConfigDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the agentConfigDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AgentConfigDTO> updateAgentConfig(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AgentConfigDTO agentConfigDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AgentConfig : {}, {}", id, agentConfigDTO);
        if (agentConfigDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentConfigDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentConfigRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        agentConfigDTO = agentConfigService.update(agentConfigDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentConfigDTO.getId().toString()))
            .body(agentConfigDTO);
    }

    /**
     * {@code PATCH  /agent-configs/:id} : Partial updates given fields of an existing agentConfig, field will ignore if it is null
     *
     * @param id the id of the agentConfigDTO to save.
     * @param agentConfigDTO the agentConfigDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentConfigDTO,
     * or with status {@code 400 (Bad Request)} if the agentConfigDTO is not valid,
     * or with status {@code 404 (Not Found)} if the agentConfigDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the agentConfigDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AgentConfigDTO> partialUpdateAgentConfig(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AgentConfigDTO agentConfigDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AgentConfig partially : {}, {}", id, agentConfigDTO);
        if (agentConfigDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentConfigDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentConfigRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AgentConfigDTO> result = agentConfigService.partialUpdate(agentConfigDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentConfigDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /agent-configs} : get all the agentConfigs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of agentConfigs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AgentConfigDTO>> getAllAgentConfigs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AgentConfigs");
        Page<AgentConfigDTO> page = agentConfigService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /agent-configs/:id} : get the "id" agentConfig.
     *
     * @param id the id of the agentConfigDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the agentConfigDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AgentConfigDTO> getAgentConfig(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AgentConfig : {}", id);
        Optional<AgentConfigDTO> agentConfigDTO = agentConfigService.findOne(id);
        return ResponseUtil.wrapOrNotFound(agentConfigDTO);
    }

    /**
     * {@code DELETE  /agent-configs/:id} : delete the "id" agentConfig.
     *
     * @param id the id of the agentConfigDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAgentConfig(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AgentConfig : {}", id);
        agentConfigService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
