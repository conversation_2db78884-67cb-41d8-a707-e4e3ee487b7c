package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.AgentTaskService;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.AgentTask}.
 */
@RestController
@RequestMapping("/api/agent-tasks")
public class AgentTaskResource {

    private static final Logger LOG = LoggerFactory.getLogger(AgentTaskResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceAgentTask";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final AgentTaskService agentTaskService;

    private final AgentTaskRepository agentTaskRepository;

    public AgentTaskResource(AgentTaskService agentTaskService, AgentTaskRepository agentTaskRepository) {
        this.agentTaskService = agentTaskService;
        this.agentTaskRepository = agentTaskRepository;
    }

    /**
     * {@code POST  /agent-tasks} : Create a new agentTask.
     *
     * @param agentTaskDTO the agentTaskDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new agentTaskDTO, or with status {@code 400 (Bad Request)} if the agentTask has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AgentTaskDTO> createAgentTask(@Valid @RequestBody AgentTaskDTO agentTaskDTO) throws URISyntaxException {
        LOG.debug("REST request to save AgentTask : {}", agentTaskDTO);
        if (agentTaskDTO.getId() != null) {
            throw new BadRequestAlertException("A new agentTask cannot already have an ID", ENTITY_NAME, "idexists");
        }
        agentTaskDTO = agentTaskService.save(agentTaskDTO);
        return ResponseEntity.created(new URI("/api/agent-tasks/" + agentTaskDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, agentTaskDTO.getId().toString()))
            .body(agentTaskDTO);
    }

    /**
     * {@code PUT  /agent-tasks/:id} : Updates an existing agentTask.
     *
     * @param id the id of the agentTaskDTO to save.
     * @param agentTaskDTO the agentTaskDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentTaskDTO,
     * or with status {@code 400 (Bad Request)} if the agentTaskDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the agentTaskDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AgentTaskDTO> updateAgentTask(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AgentTaskDTO agentTaskDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AgentTask : {}, {}", id, agentTaskDTO);
        if (agentTaskDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentTaskDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentTaskRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        agentTaskDTO = agentTaskService.update(agentTaskDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentTaskDTO.getId().toString()))
            .body(agentTaskDTO);
    }

    /**
     * {@code PATCH  /agent-tasks/:id} : Partial updates given fields of an existing agentTask, field will ignore if it is null
     *
     * @param id the id of the agentTaskDTO to save.
     * @param agentTaskDTO the agentTaskDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentTaskDTO,
     * or with status {@code 400 (Bad Request)} if the agentTaskDTO is not valid,
     * or with status {@code 404 (Not Found)} if the agentTaskDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the agentTaskDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AgentTaskDTO> partialUpdateAgentTask(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AgentTaskDTO agentTaskDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AgentTask partially : {}, {}", id, agentTaskDTO);
        if (agentTaskDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentTaskDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentTaskRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AgentTaskDTO> result = agentTaskService.partialUpdate(agentTaskDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentTaskDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /agent-tasks} : get all the agentTasks.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of agentTasks in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AgentTaskDTO>> getAllAgentTasks(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AgentTasks");
        Page<AgentTaskDTO> page = agentTaskService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /agent-tasks/:id} : get the "id" agentTask.
     *
     * @param id the id of the agentTaskDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the agentTaskDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AgentTaskDTO> getAgentTask(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AgentTask : {}", id);
        Optional<AgentTaskDTO> agentTaskDTO = agentTaskService.findOne(id);
        return ResponseUtil.wrapOrNotFound(agentTaskDTO);
    }

    /**
     * {@code DELETE  /agent-tasks/:id} : delete the "id" agentTask.
     *
     * @param id the id of the agentTaskDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAgentTask(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AgentTask : {}", id);
        agentTaskService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
