/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiRequestResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI请求管理控制器，提供AI请求的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.AiRequestService;
import com.whiskerguard.ai.service.dto.AiRequestChatDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI请求管理控制器
 * <p>
 * REST controller for managing {@link com.whiskerguard.ai.domain.AiRequest}.
 * <p>
 * 负责处理AI请求的创建、查询、更新和删除等操作。
 * 提供了分页查询、按条件筛选等功能。
 */
@Tag(name = "AI请求管理", description = "AI请求相关的API接口")
@RestController
@RequestMapping("/api/ai-requests")
public class AiRequestResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiRequestResource.class);

    /**
     * 实体名称，用于异常消息
     */
    private static final String ENTITY_NAME = "whiskerguardAiServiceAiRequest";

    /**
     * 应用名称，从配置文件中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * AI请求服务，负责处理AI请求的业务逻辑
     */
    private final AiRequestService aiRequestService;

    /**
     * AI请求仓库，用于直接访问数据
     */
    private final AiRequestRepository aiRequestRepository;

    public AiRequestResource(AiRequestService aiRequestService, AiRequestRepository aiRequestRepository) {
        this.aiRequestService = aiRequestService;
        this.aiRequestRepository = aiRequestRepository;
    }

    /**
     * 创建新的AI请求。
     * {@code POST  /ai-requests} : Create a new aiRequest.
     *
     * @param aiRequestDTO 要创建的AI请求DTO。 the aiRequestDTO to create.
     * @return 返回新建的AI请求DTO，状态为201（已创建）；如果AI请求已存在ID，则返回400（错误请求）。 the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new aiRequestDTO, or with status {@code 400 (Bad Request)} if the aiRequest has already an ID.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AiRequestDTO> createAiRequest(@Valid @RequestBody AiRequestDTO aiRequestDTO) throws URISyntaxException {
        LOG.debug("REST request to save AiRequest : {}", aiRequestDTO);
        if (aiRequestDTO.getId() != null) {
            throw new BadRequestAlertException("A new aiRequest cannot already have an ID", ENTITY_NAME, "idexists");
        }
        aiRequestDTO = aiRequestService.save(aiRequestDTO);
        return ResponseEntity.created(new URI("/api/ai-requests/" + aiRequestDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, aiRequestDTO.getId().toString()))
            .body(aiRequestDTO);
    }

    /**
     * 更新已有的AI请求。
     * {@code PUT  /ai-requests/:id} : Updates an existing aiRequest.
     *
     * @param id 要保存的AI请求DTO的ID。 the id of the aiRequestDTO to save.
     * @param aiRequestDTO 要更新的AI请求DTO。 the aiRequestDTO to update.
     * @return 返回更新后的AI请求DTO，状态为200（成功）；如果无效则返回400（错误请求），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiRequestDTO,
     * or with status {@code 400 (Bad Request)} if the aiRequestDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the aiRequestDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AiRequestDTO> updateAiRequest(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AiRequestDTO aiRequestDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AiRequest : {}, {}", id, aiRequestDTO);
        if (aiRequestDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiRequestDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiRequestRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        aiRequestDTO = aiRequestService.update(aiRequestDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiRequestDTO.getId().toString()))
            .body(aiRequestDTO);
    }

    /**
     * 部分更新已有AI请求，仅更新非空字段。
     * {@code PATCH  /ai-requests/:id} : Partial updates given fields of an existing aiRequest, field will ignore if it is null
     *
     * @param id 要保存的AI请求DTO的ID。 the id of the aiRequestDTO to save.
     * @param aiRequestDTO 要更新的AI请求DTO。 the aiRequestDTO to update.
     * @return 返回更新后的AI请求DTO，状态为200（成功）；如果无效则返回400（错误请求），如果未找到则返回404（未找到），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiRequestDTO,
     * or with status {@code 400 (Bad Request)} if the aiRequestDTO is not valid,
     * or with status {@code 404 (Not Found)} if the aiRequestDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the aiRequestDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AiRequestDTO> partialUpdateAiRequest(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AiRequestDTO aiRequestDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AiRequest partially : {}, {}", id, aiRequestDTO);
        if (aiRequestDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiRequestDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiRequestRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AiRequestDTO> result = aiRequestService.partialUpdate(aiRequestDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiRequestDTO.getId().toString())
        );
    }

    /**
     * 获取所有AI请求。
     * {@code GET  /ai-requests} : get all the aiRequests.
     *
     * @param pageable 分页信息。 the pagination information.
     * @return 返回AI请求列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiRequests in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AiRequestDTO>> getAllAiRequests(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AiRequests");
        Page<AiRequestDTO> page = aiRequestService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取指定AI请求。
     * {@code GET  /ai-requests/:id} : get the "id" aiRequest.
     *
     * @param id 要获取的AI请求DTO的ID。 the id of the aiRequestDTO to retrieve.
     * @return 返回指定AI请求DTO，状态为200（成功）；如果未找到则返回404（未找到）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the aiRequestDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AiRequestDTO> getAiRequest(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AiRequest : {}", id);
        Optional<AiRequestDTO> aiRequestDTO = aiRequestService.findOne(id);
        return ResponseUtil.wrapOrNotFound(aiRequestDTO);
    }

    /**
     * 根据ID删除指定AI请求。
     * {@code DELETE  /ai-requests/:id} : delete the "id" aiRequest.
     *
     * @param id 要删除的AI请求DTO的ID。 the id of the aiRequestDTO to delete.
     * @return 返回204（无内容）状态。 the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAiRequest(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AiRequest : {}", id);
        aiRequestService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * 根据员工ID分页查询AI请求列表。
     * {@code GET  /ai-requests/employee/:employeeId} : get AI requests by employee ID.
     *
     * @param employeeId 员工ID。 the employee ID to filter by.
     * @param pageable 分页信息。 the pagination information.
     * @return 返回该员工的AI请求列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiRequests in body.
     */
    @Operation(summary = "根据员工ID分页查询AI请求", description = "获取指定员工的所有AI请求记录，支持分页")
    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<List<AiRequestDTO>> getAiRequestsByEmployeeId(
        @Parameter(description = "员工ID", required = true, schema = @Schema(type = "integer", format = "int64")) @PathVariable(
            "employeeId"
        ) Long employeeId,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get AiRequests by employeeId : {}", employeeId);
        Page<AiRequestDTO> page = aiRequestService.findByEmployeeId(employeeId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据员工ID和请求状态分页查询AI请求列表。
     * {@code POST  /ai-requests/employee/query-by-status} : get AI requests by employee ID and status.
     *
     * @param employeeId 员工ID。 the employee ID to filter by.
     * @param status 请求状态。 the request status to filter by.
     * @param pageable 分页信息。 the pagination information.
     * @return 返回该员工指定状态的AI请求聊天列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiRequests in body.
     */
    @Operation(summary = "根据员工ID和状态分页查询AI请求", description = "获取指定员工特定状态的AI请求记录，以聊天格式展示，支持分页")
    @PostMapping("/employee/query-by-status")
    public ResponseEntity<List<AiRequestChatDTO>> getAiRequestsByEmployeeIdAndStatus(
        @Parameter(description = "员工ID", required = true) @RequestParam Long employeeId,
        @Parameter(description = "请求状态", required = true) @RequestParam RequestStatus status,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get AiRequests by employeeId : {} and status : {}", employeeId, status);
        Page<AiRequestChatDTO> page = aiRequestService.findChatByEmployeeIdAndStatus(employeeId, status, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据员工ID和工具类型分页查询AI请求列表。
     * {@code POST  /ai-requests/employee/query-by-tool-type} : get AI requests by employee ID and tool type.
     *
     * @param employeeId 员工ID。 the employee ID to filter by.
     * @param toolType 工具类型。 the tool type to filter by.
     * @param pageable 分页信息。 the pagination information.
     * @return 返回该员工指定工具类型的AI请求列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiRequests in body.
     */
    @Operation(summary = "根据员工ID和工具类型分页查询AI请求", description = "获取指定员工特定工具类型的AI请求记录，支持分页")
    @PostMapping("/employee/query-by-tool-type")
    public ResponseEntity<List<AiRequestDTO>> getAiRequestsByEmployeeIdAndToolType(
        @Parameter(description = "员工ID", required = true) @RequestParam Long employeeId,
        @Parameter(description = "工具类型", required = true) @RequestParam String toolType,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get AiRequests by employeeId : {} and toolType : {}", employeeId, toolType);
        Page<AiRequestDTO> page = aiRequestService.findByEmployeeIdAndToolType(employeeId, toolType, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据员工ID查询所有AI请求记录。
     * {@code GET  /ai-requests/employee/{employeeId}/all} : get all AI requests by employee ID without pagination.
     *
     * @param employeeId 员工ID。 the employee ID to filter by.
     * @return 返回该员工的所有AI请求列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiRequests in body.
     */
    @Operation(summary = "根据员工ID查询所有AI请求记录", description = "获取指定员工的所有AI请求记录，不分页")
    @GetMapping("/employee/{employeeId}/all")
    public ResponseEntity<List<AiRequestDTO>> getAllAiRequestsByEmployeeId(
        @Parameter(description = "员工ID", required = true, schema = @Schema(type = "integer", format = "int64")) @PathVariable(
            "employeeId"
        ) Long employeeId
    ) {
        LOG.debug("REST request to get all AiRequests by employeeId : {}", employeeId);
        List<AiRequestDTO> requests = aiRequestService.findAllByEmployeeId(employeeId);
        return ResponseEntity.ok().body(requests);
    }
}
