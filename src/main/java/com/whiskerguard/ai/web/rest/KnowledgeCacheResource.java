package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.service.KnowledgeCacheService;
import com.whiskerguard.ai.service.dto.KnowledgeCacheDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.KnowledgeCache}.
 */
@RestController
@RequestMapping("/api/knowledge-caches")
public class KnowledgeCacheResource {

    private static final Logger LOG = LoggerFactory.getLogger(KnowledgeCacheResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceKnowledgeCache";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final KnowledgeCacheService knowledgeCacheService;

    private final KnowledgeCacheRepository knowledgeCacheRepository;

    public KnowledgeCacheResource(KnowledgeCacheService knowledgeCacheService, KnowledgeCacheRepository knowledgeCacheRepository) {
        this.knowledgeCacheService = knowledgeCacheService;
        this.knowledgeCacheRepository = knowledgeCacheRepository;
    }

    /**
     * {@code POST  /knowledge-caches} : Create a new knowledgeCache.
     *
     * @param knowledgeCacheDTO the knowledgeCacheDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new knowledgeCacheDTO, or with status {@code 400 (Bad Request)} if the knowledgeCache has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<KnowledgeCacheDTO> createKnowledgeCache(@Valid @RequestBody KnowledgeCacheDTO knowledgeCacheDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save KnowledgeCache : {}", knowledgeCacheDTO);
        if (knowledgeCacheDTO.getId() != null) {
            throw new BadRequestAlertException("A new knowledgeCache cannot already have an ID", ENTITY_NAME, "idexists");
        }
        knowledgeCacheDTO = knowledgeCacheService.save(knowledgeCacheDTO);
        return ResponseEntity.created(new URI("/api/knowledge-caches/" + knowledgeCacheDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, knowledgeCacheDTO.getId().toString()))
            .body(knowledgeCacheDTO);
    }

    /**
     * {@code PUT  /knowledge-caches/:id} : Updates an existing knowledgeCache.
     *
     * @param id the id of the knowledgeCacheDTO to save.
     * @param knowledgeCacheDTO the knowledgeCacheDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated knowledgeCacheDTO,
     * or with status {@code 400 (Bad Request)} if the knowledgeCacheDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the knowledgeCacheDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<KnowledgeCacheDTO> updateKnowledgeCache(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody KnowledgeCacheDTO knowledgeCacheDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update KnowledgeCache : {}, {}", id, knowledgeCacheDTO);
        if (knowledgeCacheDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, knowledgeCacheDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!knowledgeCacheRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        knowledgeCacheDTO = knowledgeCacheService.update(knowledgeCacheDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, knowledgeCacheDTO.getId().toString()))
            .body(knowledgeCacheDTO);
    }

    /**
     * {@code PATCH  /knowledge-caches/:id} : Partial updates given fields of an existing knowledgeCache, field will ignore if it is null
     *
     * @param id the id of the knowledgeCacheDTO to save.
     * @param knowledgeCacheDTO the knowledgeCacheDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated knowledgeCacheDTO,
     * or with status {@code 400 (Bad Request)} if the knowledgeCacheDTO is not valid,
     * or with status {@code 404 (Not Found)} if the knowledgeCacheDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the knowledgeCacheDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<KnowledgeCacheDTO> partialUpdateKnowledgeCache(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody KnowledgeCacheDTO knowledgeCacheDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update KnowledgeCache partially : {}, {}", id, knowledgeCacheDTO);
        if (knowledgeCacheDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, knowledgeCacheDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!knowledgeCacheRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<KnowledgeCacheDTO> result = knowledgeCacheService.partialUpdate(knowledgeCacheDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, knowledgeCacheDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /knowledge-caches} : get all the knowledgeCaches.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of knowledgeCaches in body.
     */
    @GetMapping("")
    public ResponseEntity<List<KnowledgeCacheDTO>> getAllKnowledgeCaches(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of KnowledgeCaches");
        Page<KnowledgeCacheDTO> page = knowledgeCacheService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /knowledge-caches/:id} : get the "id" knowledgeCache.
     *
     * @param id the id of the knowledgeCacheDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the knowledgeCacheDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<KnowledgeCacheDTO> getKnowledgeCache(@PathVariable("id") Long id) {
        LOG.debug("REST request to get KnowledgeCache : {}", id);
        Optional<KnowledgeCacheDTO> knowledgeCacheDTO = knowledgeCacheService.findOne(id);
        return ResponseUtil.wrapOrNotFound(knowledgeCacheDTO);
    }

    /**
     * {@code DELETE  /knowledge-caches/:id} : delete the "id" knowledgeCache.
     *
     * @param id the id of the knowledgeCacheDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteKnowledgeCache(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete KnowledgeCache : {}", id);
        knowledgeCacheService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
