package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PromptTemplateVersionRepository;
import com.whiskerguard.ai.service.PromptTemplateVersionService;
import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 提示词模板版本管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.PromptTemplateVersion}.
 */
@RestController
@RequestMapping("/api/prompt-template-versions")
public class PromptTemplateVersionResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateVersionResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePromptTemplateVersion";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PromptTemplateVersionService promptTemplateVersionService;

    private final PromptTemplateVersionRepository promptTemplateVersionRepository;

    /**
     * 构造函数
     * Constructor
     *
     * @param promptTemplateVersionService 提示词模板版本服务 prompt template version service
     * @param promptTemplateVersionRepository 提示词模板版本仓库 prompt template version repository
     */
    public PromptTemplateVersionResource(
        PromptTemplateVersionService promptTemplateVersionService,
        PromptTemplateVersionRepository promptTemplateVersionRepository
    ) {
        this.promptTemplateVersionService = promptTemplateVersionService;
        this.promptTemplateVersionRepository = promptTemplateVersionRepository;
    }

    /**
     * 创建新的提示词模板版本
     * {@code POST  /prompt-template-versions} : Create a new promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO 要创建的提示词模板版本DTO the promptTemplateVersionDTO to create.
     * @return 响应实体，状态码201（已创建）和新的提示词模板版本DTO，或状态码400（错误请求）如果版本已有ID
     *         the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new promptTemplateVersionDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVersion has already an ID.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PromptTemplateVersionDTO> createPromptTemplateVersion(
        @Valid @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save PromptTemplateVersion : {}", promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() != null) {
            throw new BadRequestAlertException("A new promptTemplateVersion cannot already have an ID", ENTITY_NAME, "idexists");
        }
        promptTemplateVersionDTO = promptTemplateVersionService.save(promptTemplateVersionDTO);
        return ResponseEntity.created(new URI("/api/prompt-template-versions/" + promptTemplateVersionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString()))
            .body(promptTemplateVersionDTO);
    }

    /**
     * 更新现有的提示词模板版本
     * {@code PUT  /prompt-template-versions/:id} : Updates an existing promptTemplateVersion.
     *
     * @param id 要保存的提示词模板版本DTO的ID the id of the promptTemplateVersionDTO to save.
     * @param promptTemplateVersionDTO 要更新的提示词模板版本DTO the promptTemplateVersionDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板版本DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVersionDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVersionDTO is not valid,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateVersionDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PromptTemplateVersionDTO> updatePromptTemplateVersion(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PromptTemplateVersion : {}, {}", id, promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVersionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVersionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        promptTemplateVersionDTO = promptTemplateVersionService.update(promptTemplateVersionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString()))
            .body(promptTemplateVersionDTO);
    }

    /**
     * 部分更新现有提示词模板版本的指定字段，空值字段将被忽略
     * {@code PATCH  /prompt-template-versions/:id} : Partial updates given fields of an existing promptTemplateVersion, field will ignore if it is null
     *
     * @param id 要保存的提示词模板版本DTO的ID the id of the promptTemplateVersionDTO to save.
     * @param promptTemplateVersionDTO 要更新的提示词模板版本DTO the promptTemplateVersionDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板版本DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码404（未找到）如果DTO不存在，或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVersionDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVersionDTO is not valid,
     *         or with status {@code 404 (Not Found)} if the promptTemplateVersionDTO is not found,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateVersionDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PromptTemplateVersionDTO> partialUpdatePromptTemplateVersion(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PromptTemplateVersion partially : {}, {}", id, promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVersionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVersionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PromptTemplateVersionDTO> result = promptTemplateVersionService.partialUpdate(promptTemplateVersionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString())
        );
    }

    /**
     * 获取所有提示词模板版本
     * {@code GET  /prompt-template-versions} : get all the promptTemplateVersions.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 响应实体，状态码200（成功）和提示词模板版本列表
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and the list of promptTemplateVersions in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PromptTemplateVersionDTO>> getAllPromptTemplateVersions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PromptTemplateVersions");
        Page<PromptTemplateVersionDTO> page = promptTemplateVersionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取提示词模板版本
     * {@code GET  /prompt-template-versions/:id} : get the "id" promptTemplateVersion.
     *
     * @param id 要检索的提示词模板版本DTO的ID the id of the promptTemplateVersionDTO to retrieve.
     * @return 响应实体，状态码200（成功）和提示词模板版本DTO，或状态码404（未找到）
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the promptTemplateVersionDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PromptTemplateVersionDTO> getPromptTemplateVersion(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PromptTemplateVersion : {}", id);
        Optional<PromptTemplateVersionDTO> promptTemplateVersionDTO = promptTemplateVersionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(promptTemplateVersionDTO);
    }

    /**
     * 根据ID删除提示词模板版本
     * {@code DELETE  /prompt-template-versions/:id} : delete the "id" promptTemplateVersion.
     *
     * @param id 要删除的提示词模板版本DTO的ID the id of the promptTemplateVersionDTO to delete.
     * @return 响应实体，状态码204（无内容）
     *         the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePromptTemplateVersion(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PromptTemplateVersion : {}", id);
        promptTemplateVersionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
