/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolMetricsResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI工具指标管理控制器，提供AI工具指标的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AiToolMetricsRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.dto.AiToolMetricsDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI工具指标管理
 * <p>
 * REST controller for managing {@link com.whiskerguard.ai.domain.AiToolMetrics}.
 * <p>
 * 负责处理AI工具指标的创建、查询、更新和删除等操作。
 * 提供了分页查询、按条件筛选等功能。
 */
@RestController
@RequestMapping("/api/ai-tool-metrics")
public class AiToolMetricsResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiToolMetricsResource.class);

    /**
     * 实体名称，用于异常消息
     */
    private static final String ENTITY_NAME = "whiskerguardAiServiceAiToolMetrics";

    /**
     * 应用名称，从配置文件中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * AI工具指标服务，负责处理AI工具指标的业务逻辑
     */
    private final AiToolMetricsService aiToolMetricsService;

    /**
     * AI工具指标仓库，用于直接访问数据
     */
    private final AiToolMetricsRepository aiToolMetricsRepository;

    public AiToolMetricsResource(AiToolMetricsService aiToolMetricsService, AiToolMetricsRepository aiToolMetricsRepository) {
        this.aiToolMetricsService = aiToolMetricsService;
        this.aiToolMetricsRepository = aiToolMetricsRepository;
    }

    /**
     * 创建新的AI工具指标。
     * {@code POST  /ai-tool-metrics} : Create a new aiToolMetrics.
     *
     * @param aiToolMetricsDTO 要创建的AI工具指标DTO。 the aiToolMetricsDTO to create.
     * @return 返回新建的AI工具指标DTO，状态为201（已创建）；如果AI工具指标已存在ID，则返回400（错误请求）。 the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new aiToolMetricsDTO, or with status {@code 400 (Bad Request)} if the aiToolMetrics has already an ID.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AiToolMetricsDTO> createAiToolMetrics(@Valid @RequestBody AiToolMetricsDTO aiToolMetricsDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save AiToolMetrics : {}", aiToolMetricsDTO);
        if (aiToolMetricsDTO.getId() != null) {
            throw new BadRequestAlertException("A new aiToolMetrics cannot already have an ID", ENTITY_NAME, "idexists");
        }
        aiToolMetricsDTO = aiToolMetricsService.save(aiToolMetricsDTO);
        return ResponseEntity.created(new URI("/api/ai-tool-metrics/" + aiToolMetricsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, aiToolMetricsDTO.getId().toString()))
            .body(aiToolMetricsDTO);
    }

    /**
     * 更新已有的AI工具指标。
     * {@code PUT  /ai-tool-metrics/:id} : Updates an existing aiToolMetrics.
     *
     * @param id 要保存的AI工具指标DTO的ID。 the id of the aiToolMetricsDTO to save.
     * @param aiToolMetricsDTO 要更新的AI工具指标DTO。 the aiToolMetricsDTO to update.
     * @return 返回更新后的AI工具指标DTO，状态为200（成功）；如果无效则返回400（错误请求），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiToolMetricsDTO,
     * or with status {@code 400 (Bad Request)} if the aiToolMetricsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the aiToolMetricsDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AiToolMetricsDTO> updateAiToolMetrics(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AiToolMetricsDTO aiToolMetricsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AiToolMetrics : {}, {}", id, aiToolMetricsDTO);
        if (aiToolMetricsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiToolMetricsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiToolMetricsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        aiToolMetricsDTO = aiToolMetricsService.update(aiToolMetricsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiToolMetricsDTO.getId().toString()))
            .body(aiToolMetricsDTO);
    }

    /**
     * 部分更新已有AI工具指标，仅更新非空字段。
     * {@code PATCH  /ai-tool-metrics/:id} : Partial updates given fields of an existing aiToolMetrics, field will ignore if it is null
     *
     * @param id 要保存的AI工具指标DTO的ID。 the id of the aiToolMetricsDTO to save.
     * @param aiToolMetricsDTO 要更新的AI工具指标DTO。 the aiToolMetricsDTO to update.
     * @return 返回更新后的AI工具指标DTO，状态为200（成功）；如果无效则返回400（错误请求），如果未找到则返回404（未找到），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiToolMetricsDTO,
     * or with status {@code 400 (Bad Request)} if the aiToolMetricsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the aiToolMetricsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the aiToolMetricsDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AiToolMetricsDTO> partialUpdateAiToolMetrics(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AiToolMetricsDTO aiToolMetricsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AiToolMetrics partially : {}, {}", id, aiToolMetricsDTO);
        if (aiToolMetricsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiToolMetricsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiToolMetricsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AiToolMetricsDTO> result = aiToolMetricsService.partialUpdate(aiToolMetricsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiToolMetricsDTO.getId().toString())
        );
    }

    /**
     * 获取所有AI工具指标。
     * {@code GET  /ai-tool-metrics} : get all the aiToolMetrics.
     *
     * @param pageable 分页信息。 the pagination information.
     * @return 返回AI工具指标列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiToolMetrics in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AiToolMetricsDTO>> getAllAiToolMetrics(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AiToolMetrics");
        Page<AiToolMetricsDTO> page = aiToolMetricsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取指定AI工具指标。
     * {@code GET  /ai-tool-metrics/:id} : get the "id" aiToolMetrics.
     *
     * @param id 要获取的AI工具指标DTO的ID。 the id of the aiToolMetricsDTO to retrieve.
     * @return 返回指定AI工具指标DTO，状态为200（成功）；如果未找到则返回404（未找到）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the aiToolMetricsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AiToolMetricsDTO> getAiToolMetrics(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AiToolMetrics : {}", id);
        Optional<AiToolMetricsDTO> aiToolMetricsDTO = aiToolMetricsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(aiToolMetricsDTO);
    }

    /**
     * 根据ID删除指定AI工具指标。
     * {@code DELETE  /ai-tool-metrics/:id} : delete the "id" aiToolMetrics.
     *
     * @param id 要删除的AI工具指标DTO的ID。 the id of the aiToolMetricsDTO to delete.
     * @return 返回204（无内容）状态。 the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAiToolMetrics(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AiToolMetrics : {}", id);
        aiToolMetricsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
