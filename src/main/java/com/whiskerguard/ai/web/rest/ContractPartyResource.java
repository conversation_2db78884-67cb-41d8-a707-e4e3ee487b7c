package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.ContractPartyRepository;
import com.whiskerguard.ai.service.ContractPartyService;
import com.whiskerguard.ai.service.dto.ContractPartyDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 合同当事方管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.ContractParty}.
 *
 */
@RestController
@RequestMapping("/api/contract-parties")
public class ContractPartyResource {

    private static final Logger LOG = LoggerFactory.getLogger(ContractPartyResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceContractParty";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ContractPartyService contractPartyService;

    private final ContractPartyRepository contractPartyRepository;

    public ContractPartyResource(ContractPartyService contractPartyService, ContractPartyRepository contractPartyRepository) {
        this.contractPartyService = contractPartyService;
        this.contractPartyRepository = contractPartyRepository;
    }

    /**
     * {@code POST  /contract-parties} : Create a new contractParty.
     * 创建新的合同当事方。
     *
     * @param contractPartyDTO the contractPartyDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new contractPartyDTO, or with status {@code 400 (Bad Request)} if the contractParty has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ContractPartyDTO> createContractParty(@Valid @RequestBody ContractPartyDTO contractPartyDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save ContractParty : {}", contractPartyDTO);
        if (contractPartyDTO.getId() != null) {
            throw new BadRequestAlertException("A new contractParty cannot already have an ID", ENTITY_NAME, "idexists");
        }
        contractPartyDTO = contractPartyService.save(contractPartyDTO);
        return ResponseEntity.created(new URI("/api/contract-parties/" + contractPartyDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, contractPartyDTO.getId().toString()))
            .body(contractPartyDTO);
    }

    /**
     * {@code PUT  /contract-parties/:id} : Updates an existing contractParty.
     * 更新现有的合同当事方。
     *
     * @param id the id of the contractPartyDTO to save.
     * @param contractPartyDTO the contractPartyDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractPartyDTO,
     * or with status {@code 400 (Bad Request)} if the contractPartyDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the contractPartyDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ContractPartyDTO> updateContractParty(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody ContractPartyDTO contractPartyDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update ContractParty : {}, {}", id, contractPartyDTO);
        if (contractPartyDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractPartyDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractPartyRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        contractPartyDTO = contractPartyService.update(contractPartyDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractPartyDTO.getId().toString()))
            .body(contractPartyDTO);
    }

    /**
     * {@code PATCH  /contract-parties/:id} : Partial updates given fields of an existing contractParty, field will ignore if it is null
     * 部分更新现有合同当事方的指定字段，如果字段为null则忽略。
     *
     * @param id the id of the contractPartyDTO to save.
     * @param contractPartyDTO the contractPartyDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractPartyDTO,
     * or with status {@code 400 (Bad Request)} if the contractPartyDTO is not valid,
     * or with status {@code 404 (Not Found)} if the contractPartyDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the contractPartyDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ContractPartyDTO> partialUpdateContractParty(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ContractPartyDTO contractPartyDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update ContractParty partially : {}, {}", id, contractPartyDTO);
        if (contractPartyDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractPartyDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractPartyRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ContractPartyDTO> result = contractPartyService.partialUpdate(contractPartyDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractPartyDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /contract-parties} : get all the contractParties.
     * 获取所有合同当事方。
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contractParties in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ContractPartyDTO>> getAllContractParties(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of ContractParties");
        Page<ContractPartyDTO> page = contractPartyService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /contract-parties/:id} : get the "id" contractParty.
     * 根据ID获取合同当事方。
     *
     * @param id the id of the contractPartyDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the contractPartyDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ContractPartyDTO> getContractParty(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ContractParty : {}", id);
        Optional<ContractPartyDTO> contractPartyDTO = contractPartyService.findOne(id);
        return ResponseUtil.wrapOrNotFound(contractPartyDTO);
    }

    /**
     * {@code DELETE  /contract-parties/:id} : delete the "id" contractParty.
     * 删除指定ID的合同当事方。
     *
     * @param id the id of the contractPartyDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContractParty(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ContractParty : {}", id);
        contractPartyService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
