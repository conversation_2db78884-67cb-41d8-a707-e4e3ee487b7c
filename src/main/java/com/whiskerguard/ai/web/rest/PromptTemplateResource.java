package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PromptTemplateRepository;
import com.whiskerguard.ai.service.PromptTemplateService;
import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 提示词模板管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.PromptTemplate}.
 */
@RestController
@RequestMapping("/api/prompt-templates")
public class PromptTemplateResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePromptTemplate";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PromptTemplateService promptTemplateService;

    private final PromptTemplateRepository promptTemplateRepository;

    /**
     * 构造函数
     * Constructor
     *
     * @param promptTemplateService 提示词模板服务 prompt template service
     * @param promptTemplateRepository 提示词模板仓库 prompt template repository
     */
    public PromptTemplateResource(PromptTemplateService promptTemplateService, PromptTemplateRepository promptTemplateRepository) {
        this.promptTemplateService = promptTemplateService;
        this.promptTemplateRepository = promptTemplateRepository;
    }

    /**
     * 创建新的提示词模板
     * {@code POST  /prompt-templates} : Create a new promptTemplate.
     *
     * @param promptTemplateDTO 要创建的提示词模板DTO the promptTemplateDTO to create.
     * @return 响应实体，状态码201（已创建）和新的提示词模板DTO，或状态码400（错误请求）如果模板已有ID
     *         the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new promptTemplateDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplate has already an ID.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PromptTemplateDTO> createPromptTemplate(@Valid @RequestBody PromptTemplateDTO promptTemplateDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save PromptTemplate : {}", promptTemplateDTO);
        if (promptTemplateDTO.getId() != null) {
            throw new BadRequestAlertException("A new promptTemplate cannot already have an ID", ENTITY_NAME, "idexists");
        }
        promptTemplateDTO = promptTemplateService.save(promptTemplateDTO);
        return ResponseEntity.created(new URI("/api/prompt-templates/" + promptTemplateDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, promptTemplateDTO.getId().toString()))
            .body(promptTemplateDTO);
    }

    /**
     * 更新现有的提示词模板
     * {@code PUT  /prompt-templates/:id} : Updates an existing promptTemplate.
     *
     * @param id 要保存的提示词模板DTO的ID the id of the promptTemplateDTO to save.
     * @param promptTemplateDTO 要更新的提示词模板DTO the promptTemplateDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateDTO is not valid,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PromptTemplateDTO> updatePromptTemplate(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PromptTemplateDTO promptTemplateDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PromptTemplate : {}, {}", id, promptTemplateDTO);
        if (promptTemplateDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        promptTemplateDTO = promptTemplateService.update(promptTemplateDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateDTO.getId().toString()))
            .body(promptTemplateDTO);
    }

    /**
     * 部分更新现有提示词模板的指定字段，空值字段将被忽略
     * {@code PATCH  /prompt-templates/:id} : Partial updates given fields of an existing promptTemplate, field will ignore if it is null
     *
     * @param id 要保存的提示词模板DTO的ID the id of the promptTemplateDTO to save.
     * @param promptTemplateDTO 要更新的提示词模板DTO the promptTemplateDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码404（未找到）如果DTO不存在，或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateDTO is not valid,
     *         or with status {@code 404 (Not Found)} if the promptTemplateDTO is not found,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PromptTemplateDTO> partialUpdatePromptTemplate(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PromptTemplateDTO promptTemplateDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PromptTemplate partially : {}, {}", id, promptTemplateDTO);
        if (promptTemplateDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PromptTemplateDTO> result = promptTemplateService.partialUpdate(promptTemplateDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateDTO.getId().toString())
        );
    }

    /**
     * 获取所有提示词模板
     * {@code GET  /prompt-templates} : get all the promptTemplates.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 响应实体，状态码200（成功）和提示词模板列表
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and the list of promptTemplates in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PromptTemplateDTO>> getAllPromptTemplates(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PromptTemplates");
        Page<PromptTemplateDTO> page = promptTemplateService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取提示词模板
     * {@code GET  /prompt-templates/:id} : get the "id" promptTemplate.
     *
     * @param id 要检索的提示词模板DTO的ID the id of the promptTemplateDTO to retrieve.
     * @return 响应实体，状态码200（成功）和提示词模板DTO，或状态码404（未找到）
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the promptTemplateDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PromptTemplateDTO> getPromptTemplate(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PromptTemplate : {}", id);
        Optional<PromptTemplateDTO> promptTemplateDTO = promptTemplateService.findOne(id);
        return ResponseUtil.wrapOrNotFound(promptTemplateDTO);
    }

    /**
     * 根据ID删除提示词模板
     * {@code DELETE  /prompt-templates/:id} : delete the "id" promptTemplate.
     *
     * @param id 要删除的提示词模板DTO的ID the id of the promptTemplateDTO to delete.
     * @return 响应实体，状态码204（无内容）
     *         the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePromptTemplate(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PromptTemplate : {}", id);
        promptTemplateService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
