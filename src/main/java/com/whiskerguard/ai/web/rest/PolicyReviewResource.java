/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：内部制度审查REST控制器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService;
import com.whiskerguard.ai.util.LlmResponseProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 内部制度审查
 * <p>
 * 提供内部制度智能审查的REST API接口，供法规微服务调用。
 * 该控制器负责接收制度审查请求，调用相应的服务进行处理，
 * 并返回详细的审查结果。
 *
 * 主要功能：
 * 1. 接收内部制度审查请求
 * 2. 参数验证和预处理
 * 3. 调用审查服务进行分析
 * 4. 返回结构化的审查结果
 * 5. 异常处理和错误响应
 */
@Tag(name = "内部制度审查", description = "内部制度智能审查API")
@RestController
@RequestMapping("/api/policy-review")
public class PolicyReviewResource {

    private static final Logger log = LoggerFactory.getLogger(PolicyReviewResource.class);

    private final InternalPolicyReviewService internalPolicyReviewService;

    public PolicyReviewResource(InternalPolicyReviewService internalPolicyReviewService) {
        this.internalPolicyReviewService = internalPolicyReviewService;
    }

    /**
     * 内部制度智能审查接口
     * <p>
     * POST /api/policy-review/internal : 对内部制度进行智能审查分析。
     * 该接口接收制度内容和相关参数，通过AI模型和多数据源整合，
     * 提供全面的制度风险评估、合规性检查和改进建议。
     *
     * @param request 内部制度审查请求DTO，包含制度内容、类型、企业信息等
     * @return 包含详细审查结果的ResponseEntity，状态码200(OK)，响应体为InternalPolicyReviewResponseDTO
     */
    @Operation(summary = "内部制度智能审查", description = "对企业内部制度进行全面的智能审查，包括风险识别、合规检查、条款分析和改进建议")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "200",
                description = "审查成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = InternalPolicyReviewResponseDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content),
            @ApiResponse(responseCode = "401", description = "未授权访问", content = @Content),
            @ApiResponse(responseCode = "403", description = "权限不足", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
            @ApiResponse(responseCode = "503", description = "服务暂时不可用", content = @Content),
        }
    )
    @PostMapping("/internal")
    public ResponseEntity<InternalPolicyReviewResponseDTO> reviewInternalPolicy(
        @Parameter(description = "内部制度审查请求", required = true) @Valid @RequestBody InternalPolicyReviewRequestDTO request
    ) {
        // 从 metadata 中获取AI模型选择
        String aiModel = "kimi"; // 默认模型
        if (request.getMetadata() != null && request.getMetadata().containsKey("aiModel")) {
            String specifiedModel = (String) request.getMetadata().get("aiModel");
            aiModel = LlmResponseProcessor.normalizeModelName(specifiedModel);
        }

        log.info(
            "收到内部制度审查请求，租户ID: {}, 制度类型: {}, 员工ID: {}, AI模型: {}",
            request.getTenantId(),
            request.getPolicyType(),
            request.getEmployeeId(),
            aiModel
        );

        try {
            // 记录请求的基本信息（不记录敏感内容）
            log.debug(
                "制度审查请求详情 - 制度标题: {}, 制定部门: {}, 公司名称: {}, 行业: {}, 优先级: {}",
                request.getPolicyTitle(),
                request.getDepartment(),
                request.getCompanyName(),
                request.getIndustry(),
                request.getPriority()
            );

            // 调用服务进行制度审查
            InternalPolicyReviewResponseDTO response = internalPolicyReviewService.reviewInternalPolicy(request);

            // 记录审查结果摘要
            log.info(
                "内部制度审查完成，租户ID: {}, 审查ID: {}, 风险等级: {}, 风险分数: {}, 耗时: {}ms",
                request.getTenantId(),
                response.getReviewId(),
                response.getOverallRiskLevel(),
                response.getRiskScore(),
                response.getReviewDuration()
            );

            // 记录关键指标
            if (response.getRiskPoints() != null) {
                log.debug("识别风险点数量: {}", response.getRiskPoints().size());
            }
            if (response.getClauseIssues() != null) {
                log.debug("发现条款问题数量: {}", response.getClauseIssues().size());
            }
            if (response.getRelatedPartyRisks() != null) {
                log.debug("关联方风险数量: {}", response.getRelatedPartyRisks().size());
            }

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.warn("内部制度审查请求参数错误，租户ID: {}, 错误: {}", request.getTenantId(), e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (SecurityException e) {
            log.warn("内部制度审查权限不足，租户ID: {}, 员工ID: {}", request.getTenantId(), request.getEmployeeId());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        } catch (Exception e) {
            log.error(
                "内部制度审查失败，租户ID: {}, 制度类型: {}, 错误: {}",
                request.getTenantId(),
                request.getPolicyType(),
                e.getMessage(),
                e
            );
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 健康检查接口
     * <p>
     * GET /api/policy-review/health : 检查制度审查服务的健康状态。
     * 用于监控和负载均衡器检查服务可用性。
     *
     * @return 服务健康状态
     */
    @Operation(summary = "健康检查", description = "检查内部制度审查服务的健康状态")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "服务正常", content = @Content),
            @ApiResponse(responseCode = "503", description = "服务不可用", content = @Content),
        }
    )
    @PostMapping("/health")
    public ResponseEntity<String> healthCheck() {
        try {
            // 这里可以添加更复杂的健康检查逻辑
            // 例如检查依赖服务的可用性、数据库连接等
            log.debug("内部制度审查服务健康检查");
            return ResponseEntity.ok("OK");
        } catch (Exception e) {
            log.error("内部制度审查服务健康检查失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body("Service Unavailable");
        }
    }

    /**
     * 获取支持的制度类型列表
     * <p>
     * GET /api/policy-review/supported-types : 获取当前支持的制度类型列表。
     * 用于前端界面显示可选的制度类型。
     *
     * @return 支持的制度类型列表
     */
    @Operation(summary = "获取支持的制度类型", description = "获取当前系统支持的内部制度类型列表")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
        }
    )
    @PostMapping("/supported-types")
    public ResponseEntity<String[]> getSupportedPolicyTypes() {
        try {
            String[] supportedTypes = {
                "人事制度",
                "财务制度",
                "采购制度",
                "销售制度",
                "生产制度",
                "质量制度",
                "安全制度",
                "信息安全制度",
                "合规制度",
                "风险管理制度",
                "内控制度",
                "审计制度",
                "行政管理制度",
                "技术管理制度",
                "客户服务制度",
                "供应商管理制度",
                "项目管理制度",
                "知识产权制度",
                "环保制度",
                "其他制度",
            };

            log.debug("返回支持的制度类型列表，数量: {}", supportedTypes.length);
            return ResponseEntity.ok(supportedTypes);
        } catch (Exception e) {
            log.error("获取支持的制度类型失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取审查配置信息
     * <p>
     * GET /api/policy-review/config : 获取制度审查的配置信息。
     * 包括支持的优先级、审查重点、风险容忍度等配置选项。
     *
     * @return 审查配置信息
     */
    @Operation(summary = "获取审查配置", description = "获取内部制度审查的配置信息和选项")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content),
        }
    )
    @PostMapping("/config")
    public ResponseEntity<Object> getReviewConfig() {
        try {
            // 构建配置信息
            var config = new Object() {
                public final String[] priorities = { "LOW", "NORMAL", "HIGH", "URGENT" };
                public final String[] reviewFocus = { "COMPREHENSIVE", "LEGAL_ONLY", "RISK_ONLY", "OPERATIONAL_ONLY", "FINANCIAL_ONLY" };
                public final String[] riskTolerance = { "LOW", "MEDIUM", "HIGH" };
                public final int maxContentLength = 1000000;
                public final int minContentLength = 10;
                public final boolean ragEnabled = true;
                public final boolean deepAnalysisEnabled = true;
                public final boolean relatedPartyCheckEnabled = true;
                public final boolean legalComplianceCheckEnabled = true;
            };

            log.debug("返回制度审查配置信息");
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("获取审查配置失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
