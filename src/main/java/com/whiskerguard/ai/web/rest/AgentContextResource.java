package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AgentContextRepository;
import com.whiskerguard.ai.service.AgentContextService;
import com.whiskerguard.ai.service.dto.AgentContextDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.AgentContext}.
 */
@RestController
@RequestMapping("/api/agent-contexts")
public class AgentContextResource {

    private static final Logger LOG = LoggerFactory.getLogger(AgentContextResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceAgentContext";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final AgentContextService agentContextService;

    private final AgentContextRepository agentContextRepository;

    public AgentContextResource(AgentContextService agentContextService, AgentContextRepository agentContextRepository) {
        this.agentContextService = agentContextService;
        this.agentContextRepository = agentContextRepository;
    }

    /**
     * {@code POST  /agent-contexts} : Create a new agentContext.
     *
     * @param agentContextDTO the agentContextDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new agentContextDTO, or with status {@code 400 (Bad Request)} if the agentContext has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AgentContextDTO> createAgentContext(@Valid @RequestBody AgentContextDTO agentContextDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save AgentContext : {}", agentContextDTO);
        if (agentContextDTO.getId() != null) {
            throw new BadRequestAlertException("A new agentContext cannot already have an ID", ENTITY_NAME, "idexists");
        }
        agentContextDTO = agentContextService.save(agentContextDTO);
        return ResponseEntity.created(new URI("/api/agent-contexts/" + agentContextDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, agentContextDTO.getId().toString()))
            .body(agentContextDTO);
    }

    /**
     * {@code PUT  /agent-contexts/:id} : Updates an existing agentContext.
     *
     * @param id the id of the agentContextDTO to save.
     * @param agentContextDTO the agentContextDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentContextDTO,
     * or with status {@code 400 (Bad Request)} if the agentContextDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the agentContextDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AgentContextDTO> updateAgentContext(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AgentContextDTO agentContextDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AgentContext : {}, {}", id, agentContextDTO);
        if (agentContextDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentContextDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentContextRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        agentContextDTO = agentContextService.update(agentContextDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentContextDTO.getId().toString()))
            .body(agentContextDTO);
    }

    /**
     * {@code PATCH  /agent-contexts/:id} : Partial updates given fields of an existing agentContext, field will ignore if it is null
     *
     * @param id the id of the agentContextDTO to save.
     * @param agentContextDTO the agentContextDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated agentContextDTO,
     * or with status {@code 400 (Bad Request)} if the agentContextDTO is not valid,
     * or with status {@code 404 (Not Found)} if the agentContextDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the agentContextDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AgentContextDTO> partialUpdateAgentContext(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AgentContextDTO agentContextDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AgentContext partially : {}, {}", id, agentContextDTO);
        if (agentContextDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, agentContextDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!agentContextRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AgentContextDTO> result = agentContextService.partialUpdate(agentContextDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, agentContextDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /agent-contexts} : get all the agentContexts.
     *
     * @param pageable the pagination information.
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many).
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of agentContexts in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AgentContextDTO>> getAllAgentContexts(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of AgentContexts");
        Page<AgentContextDTO> page;
        if (eagerload) {
            page = agentContextService.findAllWithEagerRelationships(pageable);
        } else {
            page = agentContextService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /agent-contexts/:id} : get the "id" agentContext.
     *
     * @param id the id of the agentContextDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the agentContextDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AgentContextDTO> getAgentContext(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AgentContext : {}", id);
        Optional<AgentContextDTO> agentContextDTO = agentContextService.findOne(id);
        return ResponseUtil.wrapOrNotFound(agentContextDTO);
    }

    /**
     * {@code DELETE  /agent-contexts/:id} : delete the "id" agentContext.
     *
     * @param id the id of the agentContextDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAgentContext(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AgentContext : {}", id);
        agentContextService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
