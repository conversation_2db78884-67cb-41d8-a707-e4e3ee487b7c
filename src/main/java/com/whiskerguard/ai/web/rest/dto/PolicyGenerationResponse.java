package com.whiskerguard.ai.web.rest.dto;

import java.io.Serializable;
import java.util.Map;

/**
 * 政策生成响应DTO
 * 包含生成的企业内部管理制度及相关元数据
 */
public class PolicyGenerationResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String content; // 生成的制度内容
    private Map<String, String> annotations; // 注解映射，包含法规引用注释等
    private double complianceScore; // 合规度评分（0-100）
    private String taskId; // 异步任务ID（用于长时间运行的任务）
    private PolicyGenerationStatus status; // 生成状态

    /**
     * 默认构造函数
     */
    public PolicyGenerationResponse() {}

    /**
     * 完整构造函数
     *
     * @param content 生成的制度内容
     * @param annotations 注解映射
     * @param complianceScore 合规度评分
     */
    public PolicyGenerationResponse(String content, Map<String, String> annotations, double complianceScore) {
        this.content = content;
        this.annotations = annotations;
        this.complianceScore = complianceScore;
        this.status = PolicyGenerationStatus.COMPLETED;
    }

    /**
     * 带任务ID的完整构造函数
     *
     * @param content 生成的制度内容
     * @param annotations 注解映射
     * @param complianceScore 合规度评分
     * @param taskId 任务ID
     */
    public PolicyGenerationResponse(String content, Map<String, String> annotations, double complianceScore, String taskId) {
        this.content = content;
        this.annotations = annotations;
        this.complianceScore = complianceScore;
        this.taskId = taskId;
        this.status = PolicyGenerationStatus.COMPLETED;
    }

    /**
     * 创建进行中响应
     *
     * @param taskId 异步任务ID
     * @return 进行中的响应对象
     */
    public static PolicyGenerationResponse inProgress(String taskId) {
        PolicyGenerationResponse response = new PolicyGenerationResponse();
        response.setTaskId(taskId);
        response.setStatus(PolicyGenerationStatus.IN_PROGRESS);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorMessage 错误消息
     * @return 失败的响应对象
     */
    public static PolicyGenerationResponse failed(String errorMessage) {
        PolicyGenerationResponse response = new PolicyGenerationResponse();
        response.setContent(errorMessage);
        response.setStatus(PolicyGenerationStatus.FAILED);
        return response;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Map<String, String> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Map<String, String> annotations) {
        this.annotations = annotations;
    }

    public double getComplianceScore() {
        return complianceScore;
    }

    public void setComplianceScore(double complianceScore) {
        this.complianceScore = complianceScore;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public PolicyGenerationStatus getStatus() {
        return status;
    }

    public void setStatus(PolicyGenerationStatus status) {
        this.status = status;
    }

    /**
     * 政策生成状态枚举
     */
    public enum PolicyGenerationStatus {
        COMPLETED, // 已完成
        IN_PROGRESS, // 处理中
        FAILED, // 失败
    }
}
