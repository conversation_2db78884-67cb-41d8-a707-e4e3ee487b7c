package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.service.TaskStepService;
import com.whiskerguard.ai.service.dto.TaskStepDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.TaskStep}.
 */
@RestController
@RequestMapping("/api/task-steps")
public class TaskStepResource {

    private static final Logger LOG = LoggerFactory.getLogger(TaskStepResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceTaskStep";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TaskStepService taskStepService;

    private final TaskStepRepository taskStepRepository;

    public TaskStepResource(TaskStepService taskStepService, TaskStepRepository taskStepRepository) {
        this.taskStepService = taskStepService;
        this.taskStepRepository = taskStepRepository;
    }

    /**
     * {@code POST  /task-steps} : Create a new taskStep.
     *
     * @param taskStepDTO the taskStepDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new taskStepDTO, or with status {@code 400 (Bad Request)} if the taskStep has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<TaskStepDTO> createTaskStep(@Valid @RequestBody TaskStepDTO taskStepDTO) throws URISyntaxException {
        LOG.debug("REST request to save TaskStep : {}", taskStepDTO);
        if (taskStepDTO.getId() != null) {
            throw new BadRequestAlertException("A new taskStep cannot already have an ID", ENTITY_NAME, "idexists");
        }
        taskStepDTO = taskStepService.save(taskStepDTO);
        return ResponseEntity.created(new URI("/api/task-steps/" + taskStepDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, taskStepDTO.getId().toString()))
            .body(taskStepDTO);
    }

    /**
     * {@code PUT  /task-steps/:id} : Updates an existing taskStep.
     *
     * @param id the id of the taskStepDTO to save.
     * @param taskStepDTO the taskStepDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated taskStepDTO,
     * or with status {@code 400 (Bad Request)} if the taskStepDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the taskStepDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<TaskStepDTO> updateTaskStep(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TaskStepDTO taskStepDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TaskStep : {}, {}", id, taskStepDTO);
        if (taskStepDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, taskStepDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!taskStepRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        taskStepDTO = taskStepService.update(taskStepDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, taskStepDTO.getId().toString()))
            .body(taskStepDTO);
    }

    /**
     * {@code PATCH  /task-steps/:id} : Partial updates given fields of an existing taskStep, field will ignore if it is null
     *
     * @param id the id of the taskStepDTO to save.
     * @param taskStepDTO the taskStepDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated taskStepDTO,
     * or with status {@code 400 (Bad Request)} if the taskStepDTO is not valid,
     * or with status {@code 404 (Not Found)} if the taskStepDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the taskStepDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TaskStepDTO> partialUpdateTaskStep(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody TaskStepDTO taskStepDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TaskStep partially : {}, {}", id, taskStepDTO);
        if (taskStepDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, taskStepDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!taskStepRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<TaskStepDTO> result = taskStepService.partialUpdate(taskStepDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, taskStepDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /task-steps} : get all the taskSteps.
     *
     * @param pageable the pagination information.
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many).
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of taskSteps in body.
     */
    @GetMapping("")
    public ResponseEntity<List<TaskStepDTO>> getAllTaskSteps(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of TaskSteps");
        Page<TaskStepDTO> page;
        if (eagerload) {
            page = taskStepService.findAllWithEagerRelationships(pageable);
        } else {
            page = taskStepService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /task-steps/:id} : get the "id" taskStep.
     *
     * @param id the id of the taskStepDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the taskStepDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<TaskStepDTO> getTaskStep(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TaskStep : {}", id);
        Optional<TaskStepDTO> taskStepDTO = taskStepService.findOne(id);
        return ResponseUtil.wrapOrNotFound(taskStepDTO);
    }

    /**
     * {@code DELETE  /task-steps/:id} : delete the "id" taskStep.
     *
     * @param id the id of the taskStepDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTaskStep(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TaskStep : {}", id);
        taskStepService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
