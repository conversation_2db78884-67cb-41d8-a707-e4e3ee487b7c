/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiReviewResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI评审管理控制器，提供AI评审的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AiReviewRepository;
import com.whiskerguard.ai.service.AiReviewService;
import com.whiskerguard.ai.service.dto.AiReviewDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI评审管理控制器
 * <p>
 * REST controller for managing {@link com.whiskerguard.ai.domain.AiReview}.
 * <p>
 * 负责处理AI评审的创建、查询、更新和删除等操作。
 * 提供了分页查询、按条件筛选等功能。
 */
@RestController
@RequestMapping("/api/ai-reviews")
public class AiReviewResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiReviewResource.class);

    /**
     * 实体名称，用于异常消息
     */
    private static final String ENTITY_NAME = "whiskerguardAiServiceAiReview";

    /**
     * 应用名称，从配置文件中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * AI评审服务，负责处理AI评审的业务逻辑
     */
    private final AiReviewService aiReviewService;

    /**
     * AI评审仓库，用于直接访问数据
     */
    private final AiReviewRepository aiReviewRepository;

    public AiReviewResource(AiReviewService aiReviewService, AiReviewRepository aiReviewRepository) {
        this.aiReviewService = aiReviewService;
        this.aiReviewRepository = aiReviewRepository;
    }

    /**
     * 创建新的AI评审。
     * {@code POST  /ai-reviews} : Create a new aiReview.
     *
     * @param aiReviewDTO 要创建的AI评审DTO。 the aiReviewDTO to create.
     * @return 返回新建的AI评审DTO，状态为201（已创建）；如果AI评审已存在ID，则返回400（错误请求）。 the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new aiReviewDTO, or with status {@code 400 (Bad Request)} if the aiReview has already an ID.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AiReviewDTO> createAiReview(@Valid @RequestBody AiReviewDTO aiReviewDTO) throws URISyntaxException {
        LOG.debug("REST request to save AiReview : {}", aiReviewDTO);
        if (aiReviewDTO.getId() != null) {
            throw new BadRequestAlertException("A new aiReview cannot already have an ID", ENTITY_NAME, "idexists");
        }
        aiReviewDTO = aiReviewService.save(aiReviewDTO);
        return ResponseEntity.created(new URI("/api/ai-reviews/" + aiReviewDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, aiReviewDTO.getId().toString()))
            .body(aiReviewDTO);
    }

    /**
     * 更新已有的AI评审。
     * {@code PUT  /ai-reviews/:id} : Updates an existing aiReview.
     *
     * @param id 要保存的AI评审DTO的ID。 the id of the aiReviewDTO to save.
     * @param aiReviewDTO 要更新的AI评审DTO。 the aiReviewDTO to update.
     * @return 返回更新后的AI评审DTO，状态为200（成功）；如果无效则返回400（错误请求），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiReviewDTO,
     * or with status {@code 400 (Bad Request)} if the aiReviewDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the aiReviewDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AiReviewDTO> updateAiReview(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AiReviewDTO aiReviewDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AiReview : {}, {}", id, aiReviewDTO);
        if (aiReviewDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiReviewDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiReviewRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        aiReviewDTO = aiReviewService.update(aiReviewDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiReviewDTO.getId().toString()))
            .body(aiReviewDTO);
    }

    /**
     * 部分更新已有AI评审，仅更新非空字段。
     * {@code PATCH  /ai-reviews/:id} : Partial updates given fields of an existing aiReview, field will ignore if it is null
     *
     * @param id 要保存的AI评审DTO的ID。 the id of the aiReviewDTO to save.
     * @param aiReviewDTO 要更新的AI评审DTO。 the aiReviewDTO to update.
     * @return 返回更新后的AI评审DTO，状态为200（成功）；如果无效则返回400（错误请求），如果未找到则返回404（未找到），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiReviewDTO,
     * or with status {@code 400 (Bad Request)} if the aiReviewDTO is not valid,
     * or with status {@code 404 (Not Found)} if the aiReviewDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the aiReviewDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AiReviewDTO> partialUpdateAiReview(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AiReviewDTO aiReviewDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AiReview partially : {}, {}", id, aiReviewDTO);
        if (aiReviewDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiReviewDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiReviewRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AiReviewDTO> result = aiReviewService.partialUpdate(aiReviewDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiReviewDTO.getId().toString())
        );
    }

    /**
     * 获取所有AI评审。
     * {@code GET  /ai-reviews} : get all the aiReviews.
     *
     * @param pageable 分页信息。 the pagination information.
     * @return 返回AI评审列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiReviews in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AiReviewDTO>> getAllAiReviews(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AiReviews");
        Page<AiReviewDTO> page = aiReviewService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取指定AI评审。
     * {@code GET  /ai-reviews/:id} : get the "id" aiReview.
     *
     * @param id 要获取的AI评审DTO的ID。 the id of the aiReviewDTO to retrieve.
     * @return 返回指定AI评审DTO，状态为200（成功）；如果未找到则返回404（未找到）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the aiReviewDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AiReviewDTO> getAiReview(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AiReview : {}", id);
        Optional<AiReviewDTO> aiReviewDTO = aiReviewService.findOne(id);
        return ResponseUtil.wrapOrNotFound(aiReviewDTO);
    }

    /**
     * 根据ID删除指定AI评审。
     * {@code DELETE  /ai-reviews/:id} : delete the "id" aiReview.
     *
     * @param id 要删除的AI评审DTO的ID。 the id of the aiReviewDTO to delete.
     * @return 返回204（无内容）状态。 the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAiReview(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AiReview : {}", id);
        aiReviewService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
