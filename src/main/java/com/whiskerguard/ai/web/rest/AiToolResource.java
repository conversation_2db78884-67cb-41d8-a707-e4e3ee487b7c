/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI工具管理控制器，提供AI工具的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiToolService;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI工具管理控制器
 * <p>
 * REST controller for managing {@link com.whiskerguard.ai.domain.AiTool}.
 * <p>
 * 负责处理AI工具的创建、查询、更新和删除等操作。
 * 提供了分页查询、按条件筛选等功能。
 */
@RestController
@RequestMapping("/api/ai-tools")
public class AiToolResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiToolResource.class);

    /**
     * 实体名称，用于异常消息
     */
    private static final String ENTITY_NAME = "whiskerguardAiServiceAiTool";

    /**
     * 应用名称，从配置文件中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * AI工具服务，负责处理AI工具的业务逻辑
     */
    private final AiToolService aiToolService;

    /**
     * AI工具仓库，用于直接访问数据
     */
    private final AiToolRepository aiToolRepository;

    public AiToolResource(AiToolService aiToolService, AiToolRepository aiToolRepository) {
        this.aiToolService = aiToolService;
        this.aiToolRepository = aiToolRepository;
    }

    /**
     * 创建新的AI工具。
     * {@code POST  /ai-tools} : Create a new aiTool.
     *
     * @param aiToolDTO 要创建的AI工具DTO。 the aiToolDTO to create.
     * @return 返回新建的AI工具DTO，状态为201（已创建）；如果AI工具已存在ID，则返回400（错误请求）。 the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new aiToolDTO, or with status {@code 400 (Bad Request)} if the aiTool has already an ID.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PostMapping("/create")
    public ResponseEntity<AiToolDTO> createAiTool(@Valid @RequestBody AiToolDTO aiToolDTO) throws URISyntaxException {
        LOG.debug("REST request to save AiTool : {}", aiToolDTO);
        if (aiToolDTO.getId() != null) {
            throw new BadRequestAlertException("A new aiTool cannot already have an ID", ENTITY_NAME, "idexists");
        }
        aiToolDTO = aiToolService.save(aiToolDTO);
        return ResponseEntity.created(new URI("/api/ai-tools/" + aiToolDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, aiToolDTO.getId().toString()))
            .body(aiToolDTO);
    }

    /**
     * 更新已有的AI工具。
     * {@code PUT  /ai-tools/:id} : Updates an existing aiTool.
     *
     * @param id 要保存的AI工具DTO的ID。 the id of the aiToolDTO to save.
     * @param aiToolDTO 要更新的AI工具DTO。 the aiToolDTO to update.
     * @return 返回更新后的AI工具DTO，状态为200（成功）；如果无效则返回400（错误请求），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiToolDTO,
     * or with status {@code 400 (Bad Request)} if the aiToolDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the aiToolDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AiToolDTO> updateAiTool(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AiToolDTO aiToolDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AiTool : {}, {}", id, aiToolDTO);
        if (aiToolDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiToolDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiToolRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        aiToolDTO = aiToolService.update(aiToolDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiToolDTO.getId().toString()))
            .body(aiToolDTO);
    }

    /**
     * 部分更新已有AI工具，仅更新非空字段。
     * {@code PATCH  /ai-tools/:id} : Partial updates given fields of an existing aiTool, field will ignore if it is null
     *
     * @param id 要保存的AI工具DTO的ID。 the id of the aiToolDTO to save.
     * @param aiToolDTO 要更新的AI工具DTO。 the aiToolDTO to update.
     * @return 返回更新后的AI工具DTO，状态为200（成功）；如果无效则返回400（错误请求），如果未找到则返回404（未找到），如果无法更新则返回500（服务器内部错误）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aiToolDTO,
     * or with status {@code 400 (Bad Request)} if the aiToolDTO is not valid,
     * or with status {@code 404 (Not Found)} if the aiToolDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the aiToolDTO couldn't be updated.
     * @throws URISyntaxException 如果Location URI语法不正确。 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AiToolDTO> partialUpdateAiTool(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AiToolDTO aiToolDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AiTool partially : {}, {}", id, aiToolDTO);
        if (aiToolDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aiToolDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!aiToolRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AiToolDTO> result = aiToolService.partialUpdate(aiToolDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, aiToolDTO.getId().toString())
        );
    }

    /**
     * 获取所有AI工具。
     * {@code GET  /ai-tools} : get all the aiTools.
     *
     * @param pageable 分页信息。 the pagination information.
     * @return 返回AI工具列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aiTools in body.
     */
    @GetMapping("/list")
    public ResponseEntity<List<AiToolDTO>> getAllAiTools(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AiTools");
        Page<AiToolDTO> page = aiToolService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取所有模型列表。
     * {@code GET  /ai-tools/models} : get all models.
     *
     * @param pageable 分页信息。 the pagination information.
     * @return 返回模型列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of models in body.
     */
    @GetMapping("/models")
    public ResponseEntity<List<AiToolDTO>> getAllModels(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Models");
        Page<AiToolDTO> page = aiToolService.findAllModels(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据分类获取模型列表。
     * {@code POST  /ai-tools/models/query-by-category} : get models by category.
     *
     * @param category 模型分类。 the model category.
     * @param pageable 分页信息。 the pagination information.
     * @return 返回特定分类的模型列表，状态为200（成功）。 the {@link ResponseEntity} with status {@code 200 (OK)} and the list of models in body.
     */
    @PostMapping("/models/query-by-category")
    public ResponseEntity<List<AiToolDTO>> getModelsByCategory(
        @RequestParam String category,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Models by category: {}", category);
        Page<AiToolDTO> page = aiToolService.findModelsByCategory(category, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取指定AI工具。
     * {@code GET  /ai-tools/:id} : get the "id" aiTool.
     *
     * @param id 要获取的AI工具DTO的ID。 the id of the aiToolDTO to retrieve.
     * @return 返回指定AI工具DTO，状态为200（成功）；如果未找到则返回404（未找到）。 the {@link ResponseEntity} with status {@code 200 (OK)} and with body the aiToolDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AiToolDTO> getAiTool(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AiTool : {}", id);
        Optional<AiToolDTO> aiToolDTO = aiToolService.findOne(id);
        return ResponseUtil.wrapOrNotFound(aiToolDTO);
    }

    /**
     * 根据ID删除指定AI工具。
     * {@code DELETE  /ai-tools/:id} : delete the "id" aiTool.
     *
     * @param id 要删除的AI工具DTO的ID。 the id of the aiToolDTO to delete.
     * @return 返回204（无内容）状态。 the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAiTool(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AiTool : {}", id);
        aiToolService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
