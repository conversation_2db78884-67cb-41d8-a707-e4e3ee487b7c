package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PromptTemplateVariableRepository;
import com.whiskerguard.ai.service.PromptTemplateVariableService;
import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 提示词模板变量管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.PromptTemplateVariable}.
 */
@RestController
@RequestMapping("/api/prompt-template-variables")
public class PromptTemplateVariableResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateVariableResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePromptTemplateVariable";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PromptTemplateVariableService promptTemplateVariableService;

    private final PromptTemplateVariableRepository promptTemplateVariableRepository;

    /**
     * 构造函数
     * Constructor
     *
     * @param promptTemplateVariableService 提示词模板变量服务 prompt template variable service
     * @param promptTemplateVariableRepository 提示词模板变量仓库 prompt template variable repository
     */
    public PromptTemplateVariableResource(
        PromptTemplateVariableService promptTemplateVariableService,
        PromptTemplateVariableRepository promptTemplateVariableRepository
    ) {
        this.promptTemplateVariableService = promptTemplateVariableService;
        this.promptTemplateVariableRepository = promptTemplateVariableRepository;
    }

    /**
     * 创建新的提示词模板变量
     * {@code POST  /prompt-template-variables} : Create a new promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO 要创建的提示词模板变量DTO the promptTemplateVariableDTO to create.
     * @return 响应实体，状态码201（已创建）和新的提示词模板变量DTO，或状态码400（错误请求）如果变量已有ID
     *         the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new promptTemplateVariableDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVariable has already an ID.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PromptTemplateVariableDTO> createPromptTemplateVariable(
        @Valid @RequestBody PromptTemplateVariableDTO promptTemplateVariableDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save PromptTemplateVariable : {}", promptTemplateVariableDTO);
        if (promptTemplateVariableDTO.getId() != null) {
            throw new BadRequestAlertException("A new promptTemplateVariable cannot already have an ID", ENTITY_NAME, "idexists");
        }
        promptTemplateVariableDTO = promptTemplateVariableService.save(promptTemplateVariableDTO);
        return ResponseEntity.created(new URI("/api/prompt-template-variables/" + promptTemplateVariableDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, promptTemplateVariableDTO.getId().toString()))
            .body(promptTemplateVariableDTO);
    }

    /**
     * 更新现有的提示词模板变量
     * {@code PUT  /prompt-template-variables/:id} : Updates an existing promptTemplateVariable.
     *
     * @param id 要保存的提示词模板变量DTO的ID the id of the promptTemplateVariableDTO to save.
     * @param promptTemplateVariableDTO 要更新的提示词模板变量DTO the promptTemplateVariableDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板变量DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVariableDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVariableDTO is not valid,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateVariableDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PromptTemplateVariableDTO> updatePromptTemplateVariable(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PromptTemplateVariableDTO promptTemplateVariableDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PromptTemplateVariable : {}, {}", id, promptTemplateVariableDTO);
        if (promptTemplateVariableDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVariableDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVariableRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        promptTemplateVariableDTO = promptTemplateVariableService.update(promptTemplateVariableDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVariableDTO.getId().toString()))
            .body(promptTemplateVariableDTO);
    }

    /**
     * 部分更新现有提示词模板变量的指定字段，空值字段将被忽略
     * {@code PATCH  /prompt-template-variables/:id} : Partial updates given fields of an existing promptTemplateVariable, field will ignore if it is null
     *
     * @param id 要保存的提示词模板变量DTO的ID the id of the promptTemplateVariableDTO to save.
     * @param promptTemplateVariableDTO 要更新的提示词模板变量DTO the promptTemplateVariableDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的提示词模板变量DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码404（未找到）如果DTO不存在，或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVariableDTO,
     *         or with status {@code 400 (Bad Request)} if the promptTemplateVariableDTO is not valid,
     *         or with status {@code 404 (Not Found)} if the promptTemplateVariableDTO is not found,
     *         or with status {@code 500 (Internal Server Error)} if the promptTemplateVariableDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PromptTemplateVariableDTO> partialUpdatePromptTemplateVariable(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PromptTemplateVariableDTO promptTemplateVariableDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PromptTemplateVariable partially : {}, {}", id, promptTemplateVariableDTO);
        if (promptTemplateVariableDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVariableDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVariableRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PromptTemplateVariableDTO> result = promptTemplateVariableService.partialUpdate(promptTemplateVariableDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVariableDTO.getId().toString())
        );
    }

    /**
     * 获取所有提示词模板变量
     * {@code GET  /prompt-template-variables} : get all the promptTemplateVariables.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 响应实体，状态码200（成功）和提示词模板变量列表
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and the list of promptTemplateVariables in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PromptTemplateVariableDTO>> getAllPromptTemplateVariables(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PromptTemplateVariables");
        Page<PromptTemplateVariableDTO> page = promptTemplateVariableService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取提示词模板变量
     * {@code GET  /prompt-template-variables/:id} : get the "id" promptTemplateVariable.
     *
     * @param id 要检索的提示词模板变量DTO的ID the id of the promptTemplateVariableDTO to retrieve.
     * @return 响应实体，状态码200（成功）和提示词模板变量DTO，或状态码404（未找到）
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the promptTemplateVariableDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PromptTemplateVariableDTO> getPromptTemplateVariable(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PromptTemplateVariable : {}", id);
        Optional<PromptTemplateVariableDTO> promptTemplateVariableDTO = promptTemplateVariableService.findOne(id);
        return ResponseUtil.wrapOrNotFound(promptTemplateVariableDTO);
    }

    /**
     * 根据ID删除提示词模板变量
     * {@code DELETE  /prompt-template-variables/:id} : delete the "id" promptTemplateVariable.
     *
     * @param id 要删除的提示词模板变量DTO的ID the id of the promptTemplateVariableDTO to delete.
     * @return 响应实体，状态码204（无内容）
     *         the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePromptTemplateVariable(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PromptTemplateVariable : {}", id);
        promptTemplateVariableService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
