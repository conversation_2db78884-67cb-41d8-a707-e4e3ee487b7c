package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.TenantPromptConfigRepository;
import com.whiskerguard.ai.service.TenantPromptConfigService;
import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 租户提示词配置管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.TenantPromptConfig}.
 */
@RestController
@RequestMapping("/api/tenant-prompt-configs")
public class TenantPromptConfigResource {

    private static final Logger LOG = LoggerFactory.getLogger(TenantPromptConfigResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceTenantPromptConfig";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TenantPromptConfigService tenantPromptConfigService;

    private final TenantPromptConfigRepository tenantPromptConfigRepository;

    /**
     * 构造函数
     * Constructor
     *
     * @param tenantPromptConfigService 租户提示词配置服务 tenant prompt config service
     * @param tenantPromptConfigRepository 租户提示词配置仓库 tenant prompt config repository
     */
    public TenantPromptConfigResource(
        TenantPromptConfigService tenantPromptConfigService,
        TenantPromptConfigRepository tenantPromptConfigRepository
    ) {
        this.tenantPromptConfigService = tenantPromptConfigService;
        this.tenantPromptConfigRepository = tenantPromptConfigRepository;
    }

    /**
     * 创建新的租户提示词配置
     * {@code POST  /tenant-prompt-configs} : Create a new tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO 要创建的租户提示词配置DTO the tenantPromptConfigDTO to create.
     * @return 响应实体，状态码201（已创建）和新的租户提示词配置DTO，或状态码400（错误请求）如果配置已有ID
     *         the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new tenantPromptConfigDTO,
     *         or with status {@code 400 (Bad Request)} if the tenantPromptConfig has already an ID.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<TenantPromptConfigDTO> createTenantPromptConfig(@Valid @RequestBody TenantPromptConfigDTO tenantPromptConfigDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save TenantPromptConfig : {}", tenantPromptConfigDTO);
        if (tenantPromptConfigDTO.getId() != null) {
            throw new BadRequestAlertException("A new tenantPromptConfig cannot already have an ID", ENTITY_NAME, "idexists");
        }
        tenantPromptConfigDTO = tenantPromptConfigService.save(tenantPromptConfigDTO);
        return ResponseEntity.created(new URI("/api/tenant-prompt-configs/" + tenantPromptConfigDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tenantPromptConfigDTO.getId().toString()))
            .body(tenantPromptConfigDTO);
    }

    /**
     * 更新现有的租户提示词配置
     * {@code PUT  /tenant-prompt-configs/:id} : Updates an existing tenantPromptConfig.
     *
     * @param id 要保存的租户提示词配置DTO的ID the id of the tenantPromptConfigDTO to save.
     * @param tenantPromptConfigDTO 要更新的租户提示词配置DTO the tenantPromptConfigDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的租户提示词配置DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantPromptConfigDTO,
     *         or with status {@code 400 (Bad Request)} if the tenantPromptConfigDTO is not valid,
     *         or with status {@code 500 (Internal Server Error)} if the tenantPromptConfigDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<TenantPromptConfigDTO> updateTenantPromptConfig(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TenantPromptConfigDTO tenantPromptConfigDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TenantPromptConfig : {}, {}", id, tenantPromptConfigDTO);
        if (tenantPromptConfigDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantPromptConfigDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantPromptConfigRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        tenantPromptConfigDTO = tenantPromptConfigService.update(tenantPromptConfigDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantPromptConfigDTO.getId().toString()))
            .body(tenantPromptConfigDTO);
    }

    /**
     * 部分更新现有租户提示词配置的指定字段，空值字段将被忽略
     * {@code PATCH  /tenant-prompt-configs/:id} : Partial updates given fields of an existing tenantPromptConfig, field will ignore if it is null
     *
     * @param id 要保存的租户提示词配置DTO的ID the id of the tenantPromptConfigDTO to save.
     * @param tenantPromptConfigDTO 要更新的租户提示词配置DTO the tenantPromptConfigDTO to update.
     * @return 响应实体，状态码200（成功）和更新后的租户提示词配置DTO，或状态码400（错误请求）如果DTO无效，
     *         或状态码404（未找到）如果DTO不存在，或状态码500（内部服务器错误）如果DTO无法更新
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantPromptConfigDTO,
     *         or with status {@code 400 (Bad Request)} if the tenantPromptConfigDTO is not valid,
     *         or with status {@code 404 (Not Found)} if the tenantPromptConfigDTO is not found,
     *         or with status {@code 500 (Internal Server Error)} if the tenantPromptConfigDTO couldn't be updated.
     * @throws URISyntaxException 如果位置URI语法不正确 if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TenantPromptConfigDTO> partialUpdateTenantPromptConfig(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody TenantPromptConfigDTO tenantPromptConfigDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TenantPromptConfig partially : {}, {}", id, tenantPromptConfigDTO);
        if (tenantPromptConfigDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantPromptConfigDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantPromptConfigRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<TenantPromptConfigDTO> result = tenantPromptConfigService.partialUpdate(tenantPromptConfigDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantPromptConfigDTO.getId().toString())
        );
    }

    /**
     * 获取所有租户提示词配置
     * {@code GET  /tenant-prompt-configs} : get all the tenantPromptConfigs.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 响应实体，状态码200（成功）和租户提示词配置列表
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and the list of tenantPromptConfigs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<TenantPromptConfigDTO>> getAllTenantPromptConfigs(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of TenantPromptConfigs");
        Page<TenantPromptConfigDTO> page = tenantPromptConfigService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据ID获取租户提示词配置
     * {@code GET  /tenant-prompt-configs/:id} : get the "id" tenantPromptConfig.
     *
     * @param id 要检索的租户提示词配置DTO的ID the id of the tenantPromptConfigDTO to retrieve.
     * @return 响应实体，状态码200（成功）和租户提示词配置DTO，或状态码404（未找到）
     *         the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenantPromptConfigDTO,
     *         or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<TenantPromptConfigDTO> getTenantPromptConfig(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TenantPromptConfig : {}", id);
        Optional<TenantPromptConfigDTO> tenantPromptConfigDTO = tenantPromptConfigService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tenantPromptConfigDTO);
    }

    /**
     * 根据ID删除租户提示词配置
     * {@code DELETE  /tenant-prompt-configs/:id} : delete the "id" tenantPromptConfig.
     *
     * @param id 要删除的租户提示词配置DTO的ID the id of the tenantPromptConfigDTO to delete.
     * @return 响应实体，状态码204（无内容）
     *         the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTenantPromptConfig(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TenantPromptConfig : {}", id);
        tenantPromptConfigService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
