/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewExceptionHandler.java
 * 包    名：com.whiskerguard.ai.web.rest.errors
 * 描    述：内部制度审查异常处理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest.errors;

import com.whiskerguard.ai.service.policy.InternalPolicyReviewService.PolicyReviewException;
import java.util.concurrent.TimeoutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

/**
 * 内部制度审查异常处理器
 * <p>
 * 专门处理内部制度审查相关的异常，提供统一的错误响应格式。
 * 确保异常信息的安全性和用户友好性。
 *
 * 主要功能：
 * 1. 处理制度审查业务异常
 * 2. 处理超时异常
 * 3. 处理参数验证异常
 * 4. 处理权限异常
 * 5. 提供统一的错误响应格式
 */
@ControllerAdvice(basePackages = "com.whiskerguard.ai.web.rest")
public class PolicyReviewExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(PolicyReviewExceptionHandler.class);

    /**
     * 处理制度审查业务异常
     */
    @ExceptionHandler(PolicyReviewException.class)
    public ResponseEntity<ErrorResponse> handlePolicyReviewException(PolicyReviewException ex, WebRequest request) {
        log.error("内部制度审查异常: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "POLICY_REVIEW_ERROR",
            "制度审查处理失败，请检查输入内容或稍后重试",
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 处理超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    public ResponseEntity<ErrorResponse> handleTimeoutException(TimeoutException ex, WebRequest request) {
        log.warn("内部制度审查超时: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            "POLICY_REVIEW_TIMEOUT",
            "制度审查处理超时，请稍后重试或联系技术支持",
            HttpStatus.REQUEST_TIMEOUT.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(errorResponse);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex, WebRequest request) {
        log.warn("内部制度审查参数错误: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            "INVALID_PARAMETER",
            "请求参数不正确：" + ex.getMessage(),
            HttpStatus.BAD_REQUEST.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(SecurityException.class)
    public ResponseEntity<ErrorResponse> handleSecurityException(SecurityException ex, WebRequest request) {
        log.warn("内部制度审查权限不足: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            "ACCESS_DENIED",
            "权限不足，无法访问该资源",
            HttpStatus.FORBIDDEN.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * 处理AI服务异常
     */
    @ExceptionHandler(AiServiceException.class)
    public ResponseEntity<ErrorResponse> handleAiServiceException(AiServiceException ex, WebRequest request) {
        log.error("AI服务异常: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "AI_SERVICE_ERROR",
            "AI分析服务暂时不可用，请稍后重试",
            HttpStatus.SERVICE_UNAVAILABLE.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * 处理外部服务异常
     */
    @ExceptionHandler(ExternalServiceException.class)
    public ResponseEntity<ErrorResponse> handleExternalServiceException(ExternalServiceException ex, WebRequest request) {
        log.error("外部服务异常: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "EXTERNAL_SERVICE_ERROR",
            "外部服务暂时不可用，部分功能可能受影响",
            HttpStatus.BAD_GATEWAY.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(errorResponse);
    }

    /**
     * 处理数据处理异常
     */
    @ExceptionHandler(DataProcessingException.class)
    public ResponseEntity<ErrorResponse> handleDataProcessingException(DataProcessingException ex, WebRequest request) {
        log.error("数据处理异常: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "DATA_PROCESSING_ERROR",
            "数据处理失败，请检查输入格式或内容",
            HttpStatus.UNPROCESSABLE_ENTITY.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(errorResponse);
    }

    /**
     * 处理通用运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleRuntimeException(RuntimeException ex, WebRequest request) {
        log.error("运行时异常: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "INTERNAL_ERROR",
            "系统内部错误，请联系技术支持",
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            request.getDescription(false)
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 错误响应类
     */
    public static class ErrorResponse {

        private String errorCode;
        private String message;
        private int status;
        private String path;
        private long timestamp;

        public ErrorResponse(String errorCode, String message, int status, String path) {
            this.errorCode = errorCode;
            this.message = message;
            this.status = status;
            this.path = path;
            this.timestamp = System.currentTimeMillis();
        }

        // Getters and Setters
        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }

    /**
     * AI服务异常
     */
    public static class AiServiceException extends RuntimeException {

        public AiServiceException(String message) {
            super(message);
        }

        public AiServiceException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 外部服务异常
     */
    public static class ExternalServiceException extends RuntimeException {

        public ExternalServiceException(String message) {
            super(message);
        }

        public ExternalServiceException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 数据处理异常
     */
    public static class DataProcessingException extends RuntimeException {

        public DataProcessingException(String message) {
            super(message);
        }

        public DataProcessingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
