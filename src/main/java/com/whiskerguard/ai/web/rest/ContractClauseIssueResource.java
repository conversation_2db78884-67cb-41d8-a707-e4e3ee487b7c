package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.ContractClauseIssueRepository;
import com.whiskerguard.ai.service.ContractClauseIssueService;
import com.whiskerguard.ai.service.dto.ContractClauseIssueDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 合同条款问题管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.ContractClauseIssue}.
 * 合同条款问题管理
 */
@RestController
@RequestMapping("/api/contract-clause-issues")
public class ContractClauseIssueResource {

    private static final Logger LOG = LoggerFactory.getLogger(ContractClauseIssueResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServiceContractClauseIssue";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ContractClauseIssueService contractClauseIssueService;

    private final ContractClauseIssueRepository contractClauseIssueRepository;

    public ContractClauseIssueResource(
        ContractClauseIssueService contractClauseIssueService,
        ContractClauseIssueRepository contractClauseIssueRepository
    ) {
        this.contractClauseIssueService = contractClauseIssueService;
        this.contractClauseIssueRepository = contractClauseIssueRepository;
    }

    /**
     * {@code POST  /contract-clause-issues} : Create a new contractClauseIssue.
     * 创建新的合同条款问题。
     *
     * @param contractClauseIssueDTO the contractClauseIssueDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new contractClauseIssueDTO, or with status {@code 400 (Bad Request)} if the contractClauseIssue has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ContractClauseIssueDTO> createContractClauseIssue(
        @Valid @RequestBody ContractClauseIssueDTO contractClauseIssueDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save ContractClauseIssue : {}", contractClauseIssueDTO);
        if (contractClauseIssueDTO.getId() != null) {
            throw new BadRequestAlertException("A new contractClauseIssue cannot already have an ID", ENTITY_NAME, "idexists");
        }
        contractClauseIssueDTO = contractClauseIssueService.save(contractClauseIssueDTO);
        return ResponseEntity.created(new URI("/api/contract-clause-issues/" + contractClauseIssueDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, contractClauseIssueDTO.getId().toString()))
            .body(contractClauseIssueDTO);
    }

    /**
     * {@code PUT  /contract-clause-issues/:id} : Updates an existing contractClauseIssue.
     * 更新现有的合同条款问题。
     *
     * @param id the id of the contractClauseIssueDTO to save.
     * @param contractClauseIssueDTO the contractClauseIssueDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractClauseIssueDTO,
     * or with status {@code 400 (Bad Request)} if the contractClauseIssueDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the contractClauseIssueDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ContractClauseIssueDTO> updateContractClauseIssue(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody ContractClauseIssueDTO contractClauseIssueDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update ContractClauseIssue : {}, {}", id, contractClauseIssueDTO);
        if (contractClauseIssueDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractClauseIssueDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractClauseIssueRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        contractClauseIssueDTO = contractClauseIssueService.update(contractClauseIssueDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractClauseIssueDTO.getId().toString()))
            .body(contractClauseIssueDTO);
    }

    /**
     * {@code PATCH  /contract-clause-issues/:id} : Partial updates given fields of an existing contractClauseIssue, field will ignore if it is null
     * 部分更新现有合同条款问题的指定字段，如果字段为null则忽略。
     *
     * @param id the id of the contractClauseIssueDTO to save.
     * @param contractClauseIssueDTO the contractClauseIssueDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated contractClauseIssueDTO,
     * or with status {@code 400 (Bad Request)} if the contractClauseIssueDTO is not valid,
     * or with status {@code 404 (Not Found)} if the contractClauseIssueDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the contractClauseIssueDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ContractClauseIssueDTO> partialUpdateContractClauseIssue(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ContractClauseIssueDTO contractClauseIssueDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update ContractClauseIssue partially : {}, {}", id, contractClauseIssueDTO);
        if (contractClauseIssueDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, contractClauseIssueDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!contractClauseIssueRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ContractClauseIssueDTO> result = contractClauseIssueService.partialUpdate(contractClauseIssueDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, contractClauseIssueDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /contract-clause-issues} : get all the contractClauseIssues.
     * 获取所有合同条款问题。
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contractClauseIssues in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ContractClauseIssueDTO>> getAllContractClauseIssues(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of ContractClauseIssues");
        Page<ContractClauseIssueDTO> page = contractClauseIssueService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /contract-clause-issues/:id} : get the "id" contractClauseIssue.
     * 根据ID获取合同条款问题。
     *
     * @param id the id of the contractClauseIssueDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the contractClauseIssueDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ContractClauseIssueDTO> getContractClauseIssue(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ContractClauseIssue : {}", id);
        Optional<ContractClauseIssueDTO> contractClauseIssueDTO = contractClauseIssueService.findOne(id);
        return ResponseUtil.wrapOrNotFound(contractClauseIssueDTO);
    }

    /**
     * {@code DELETE  /contract-clause-issues/:id} : delete the "id" contractClauseIssue.
     * 删除指定ID的合同条款问题。
     *
     * @param id the id of the contractClauseIssueDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContractClauseIssue(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ContractClauseIssue : {}", id);
        contractClauseIssueService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
