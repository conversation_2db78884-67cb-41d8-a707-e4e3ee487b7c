package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 租户提示词配置管理服务接口
 * Service Interface for managing {@link com.whiskerguard.ai.domain.TenantPromptConfig}.
 */
public interface TenantPromptConfigService {
    /**
     * 保存租户提示词配置
     * Save a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO 要保存的实体 the entity to save.
     * @return 持久化后的实体 the persisted entity.
     */
    TenantPromptConfigDTO save(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * 更新租户提示词配置
     * Updates a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO 要更新的实体 the entity to update.
     * @return 持久化后的实体 the persisted entity.
     */
    TenantPromptConfigDTO update(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * 部分更新租户提示词配置
     * Partially updates a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO 要部分更新的实体 the entity to update partially.
     * @return 持久化后的实体 the persisted entity.
     */
    Optional<TenantPromptConfigDTO> partialUpdate(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * 获取所有租户提示词配置
     * Get all the tenantPromptConfigs.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 实体列表 the list of entities.
     */
    Page<TenantPromptConfigDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取租户提示词配置
     * Get the "id" tenantPromptConfig.
     *
     * @param id 实体ID the id of the entity.
     * @return 实体 the entity.
     */
    Optional<TenantPromptConfigDTO> findOne(Long id);

    /**
     * 根据ID删除租户提示词配置
     * Delete the "id" tenantPromptConfig.
     *
     * @param id 实体ID the id of the entity.
     */
    void delete(Long id);
}
