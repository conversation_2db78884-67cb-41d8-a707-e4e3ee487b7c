package com.whiskerguard.ai.service.invocation;

/**
 * 统一的 AI 调用结果模型
 */
public class AiResult {

    /** 调用返回的主要内容 */
    private String content;
    /** 可选的使用量或其他指标 */
    private java.util.Map<String, Object> usage;
    /** 调用耗时（毫秒） */
    private long durationMs;

    public AiResult() {}

    public AiResult(String content, java.util.Map<String, Object> usage, long durationMs) {
        this.content = content;
        this.usage = usage;
        this.durationMs = durationMs;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public java.util.Map<String, Object> getUsage() {
        return usage;
    }

    public void setUsage(java.util.Map<String, Object> usage) {
        this.usage = usage;
    }

    public long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(long durationMs) {
        this.durationMs = durationMs;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String content;
        private java.util.Map<String, Object> usage;
        private long durationMs;

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder usage(java.util.Map<String, Object> usage) {
            this.usage = usage;
            return this;
        }

        public Builder durationMs(long durationMs) {
            this.durationMs = durationMs;
            return this;
        }

        public AiResult build() {
            return new AiResult(content, usage, durationMs);
        }
    }
}
