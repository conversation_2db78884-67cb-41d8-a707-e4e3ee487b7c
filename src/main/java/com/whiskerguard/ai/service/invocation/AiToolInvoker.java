package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import reactor.core.publisher.Flux;

/**
 * AI 工具调用接口
 */
public interface AiToolInvoker {
    /**
     * 返回此 Invoker 对应的工具 key，与 AiTool.toolKey 一一对应
     */
    String getToolKey();

    /**
     * 调用 LLM 并返回统一结果
     *
     * @param dto 调用请求信息
     * @param toolConfig 数据库配置的工具信息
     * @return 统一的调用结果模型
     */
    AiResult invoke(AiInvocationRequestDTO dto, AiTool toolConfig);

    /**
     * 流式调用 LLM 并返回响应流
     *
     * @param dto 流式调用请求信息
     * @param toolConfig 数据库配置的工具信息
     * @return 响应流，包含内容片段和最终使用统计
     */
    default Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool toolConfig) {
        // 默认实现：将非流式调用转换为单个流响应
        AiInvocationRequestDTO invocationDto = new AiInvocationRequestDTO(
            dto.getToolKey(),
            dto.getPrompt(),
            dto.getMetadata(),
            dto.getTenantId(),
            dto.getEmployeeId()
        );

        // 正确传递两个参数：dto 和 toolConfig
        AiResult result = invoke(invocationDto, toolConfig);

        // 创建响应对象
        AiStreamResponseDTO response = new AiStreamResponseDTO();
        response.setContent(result.getContent());
        response.setUsage(result.getUsage());
        response.setDone(true);

        return Flux.just(response);
    }

    /**
     * 检查此调用器是否支持流式输出
     *
     * @return 是否支持流式输出
     */
    default boolean supportsStreaming() {
        return false;
    }
}
