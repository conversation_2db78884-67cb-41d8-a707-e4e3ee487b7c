package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.service.invocation.ModelEnsembleService.ModelResponse;
import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 结果整合引擎
 * 负责整合多个LLM模型的输出结果，提供多种整合策略，如仲裁模式、投票算法和互补整合
 */
@Component
public class ResultIntegrationEngine {

    private final Logger log = LoggerFactory.getLogger(ResultIntegrationEngine.class);

    /**
     * 整合策略枚举
     */
    public enum IntegrationStrategy {
        ARBITRATION, // 仲裁模式：使用强模型整合其他模型结果
        VOTING, // 投票算法：基于多数共识生成最终结果
        COMPLEMENTARY, // 互补整合：利用不同模型的优势领域
    }

    // 默认的整合策略
    private IntegrationStrategy defaultStrategy = IntegrationStrategy.ARBITRATION;

    // 强模型优先级列表（仲裁模式使用）
    private final List<String> strongModelsOrder = Arrays.asList("gpt-4", "deepseek", "gpt-3.5-turbo", "kimi", "doubao");

    /**
     * 整合多个模型的响应结果
     *
     * @param responses 多个模型的响应结果列表
     * @param originalPrompt 原始提示词
     * @return 整合后的结果
     */
    public String integrate(List<ModelResponse> responses, String originalPrompt) {
        return integrate(responses, originalPrompt, defaultStrategy);
    }

    /**
     * 使用指定的整合策略整合多个模型的响应结果
     *
     * @param responses 多个模型的响应结果列表
     * @param originalPrompt 原始提示词
     * @param strategy 整合策略
     * @return 整合后的结果
     */
    public String integrate(List<ModelResponse> responses, String originalPrompt, IntegrationStrategy strategy) {
        log.info("使用 {} 策略整合 {} 个模型的响应", strategy, responses.size());

        return switch (strategy) {
            case ARBITRATION -> arbitrationIntegration(responses, originalPrompt);
            case VOTING -> votingIntegration(responses, originalPrompt);
            case COMPLEMENTARY -> complementaryIntegration(responses, originalPrompt);
            default -> {
                log.warn("未知的整合策略 {}, 使用默认的仲裁模式", strategy);
                yield arbitrationIntegration(responses, originalPrompt);
            }
        };
    }

    /**
     * 仲裁模式整合
     * 使用预定义的强模型优先级列表，从中选择可用的最强模型作为仲裁者，
     * 整合方法是将其他模型的输出作为参考，由仲裁模型生成最终答案
     *
     * @param responses 多个模型的响应结果列表
     * @param originalPrompt 原始提示词（保留此参数以便将来扩展）
     * @return 整合后的结果
     */
    private String arbitrationIntegration(List<ModelResponse> responses, String originalPrompt) {
        // 根据强模型优先级列表，查找可用的最强模型
        Optional<ModelResponse> arbitratorOpt = strongModelsOrder
            .stream()
            .map(modelName -> responses.stream().filter(r -> r.getModelName().toLowerCase().contains(modelName.toLowerCase())).findFirst())
            .filter(Optional::isPresent)
            .map(Optional::get)
            .findFirst();

        // 如果没有找到仲裁者，使用响应时间最短的模型
        ModelResponse arbitrator = arbitratorOpt.orElseGet(() ->
            responses
                .stream()
                .min(Comparator.comparing(ModelResponse::getResponseTime))
                .orElseThrow(() -> new IllegalStateException("无可用的响应结果"))
        );

        log.info("已选择 {} 作为仲裁模型", arbitrator.getModelName());

        // 如果只有一个模型响应，直接返回其结果
        if (responses.size() == 1) {
            return arbitrator.getResponse();
        }

        // 获取所有非仲裁者的响应 (保留此代码以便将来扩展)
        @SuppressWarnings("unused") // 备注：虽然现在未使用，但保留以便未来扩展
        List<ModelResponse> otherResponses = responses.stream().filter(r -> !r.getModelName().equals(arbitrator.getModelName())).toList();

        // 简化起见，这里直接返回仲裁者的结果
        // 实际实现中，应该将其他模型的响应作为参考，再次调用仲裁模型生成最终结果
        return arbitrator.getResponse();
        // TODO: 实际项目中，应该实现类似下面的逻辑
        /*
        // 构建新的提示词，包含其他模型的输出作为参考
        StringBuilder arbitrationPrompt = new StringBuilder();
        arbitrationPrompt.append("原始任务：\n").append(originalPrompt).append("\n\n");
        arbitrationPrompt.append("以下是其他AI模型的输出结果，请作为专家评判，综合这些结果，给出最优质、最准确的最终答案：\n\n");

        for (ModelResponse response : otherResponses) {
            arbitrationPrompt.append(response.getModelName()).append(" 的回答：\n");
            arbitrationPrompt.append(response.getResponse()).append("\n\n");
        }

        arbitrationPrompt.append("请综合以上输出，给出最终答案：");

        // 使用仲裁模型生成最终结果
        AiInvocationRequestDTO arbitrationRequest = new AiInvocationRequestDTO(arbitrationPrompt.toString());
        String finalResult = arbitrator.invoke(arbitrationRequest);
        return finalResult;
        */
    }

    /**
     * 投票算法整合
     * 基于多数共识生成最终结果，适用于生成有明确选项的结果
     *
     * @param responses 多个模型的响应结果列表
     * @param originalPrompt 原始提示词（保留此参数以便将来扩展）
     * @return 整合后的结果
     */
    private String votingIntegration(List<ModelResponse> responses, String originalPrompt) {
        // 此处简化实现，实际项目中应该实现更复杂的文本比较和投票机制
        log.info("使用投票算法整合 {} 个模型的响应", responses.size());

        // 如果只有一个模型响应，直接返回其结果
        if (responses.size() == 1) {
            return responses.get(0).getResponse();
        }

        // 简单起见，返回响应内容最长的结果
        return responses
            .stream()
            .max(Comparator.comparing(r -> r.getResponse().length()))
            .map(ModelResponse::getResponse)
            .orElse("无法通过投票算法得出结果");
        // TODO: 实际项目中，应该实现更复杂的文本比较和投票机制
    }

    /**
     * 互补整合
     * 利用不同模型的优势领域，组合生成最终结果
     *
     * @param responses 多个模型的响应结果列表
     * @param originalPrompt 原始提示词（保留此参数以便将来扩展）
     * @return 整合后的结果
     */
    private String complementaryIntegration(List<ModelResponse> responses, String originalPrompt) {
        // 此处简化实现，实际项目中应该根据不同领域的特点实现复杂的互补整合
        log.info("使用互补整合算法整合 {} 个模型的响应", responses.size());

        // 如果只有一个模型响应，直接返回其结果
        if (responses.size() == 1) {
            return responses.get(0).getResponse();
        }

        // 简单起见，当前实现返回使用响应时间最短的模型的结果
        return responses
            .stream()
            .min(Comparator.comparing(ModelResponse::getResponseTime))
            .map(ModelResponse::getResponse)
            .orElse("无法通过互补整合算法得出结果");
        // TODO: 实际项目中，应该实现根据原始提示词的领域、模型的专长等因素，选择或组合不同模型的结果
    }

    /**
     * 设置默认整合策略
     *
     * @param strategy 整合策略
     */
    public void setDefaultStrategy(IntegrationStrategy strategy) {
        this.defaultStrategy = strategy;
        log.info("已将默认整合策略设置为: {}", strategy);
    }
}
