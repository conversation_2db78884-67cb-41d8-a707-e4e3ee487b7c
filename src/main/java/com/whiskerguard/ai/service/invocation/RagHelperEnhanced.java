package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.service.AiResponseCacheService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * RAG (Retrieval-Augmented Generation) 辅助类 - 增强版
 * <p>
 * 该类负责与检索服务交互，获取相关上下文，并将其格式化为LLM可用的提示词。
 * 用于增强LLM的回答质量，减少幻觉。
 *
 * 增强功能：
 * 1. 智能缓存 - 缓存RAG检索结果，避免重复调用
 * 2. 快速失败 - 优化超时处理
 * 3. 性能监控 - 详细的性能指标记录
 */
@Component
public class RagHelperEnhanced {

    private final Logger log = LoggerFactory.getLogger(RagHelperEnhanced.class);
    private final RetrievalServiceClient retrievalServiceClient;
    private final AiResponseCacheService cacheService;

    // RAG功能是否启用，默认为true
    @Value("${whiskerguard.ai.rag.enabled:true}")
    private boolean ragEnabled;

    // 默认租户ID，当请求中没有提供租户ID时使用
    @Value("${whiskerguard.ai.rag.default-tenant-id:1}")
    private String defaultTenantId;

    // 默认检索结果数量，返回相似度最高的前K个结果
    @Value("${whiskerguard.ai.rag.top-k:3}")
    private Integer defaultTopK;

    // 默认距离计算方式，用于计算向量相似度，默认使用余弦相似度
    @Value("${whiskerguard.ai.rag.distance-metric:cosine}")
    private String defaultDistanceMetric;

    // 最小相似度阈值，低于该阈值的检索结果将被过滤掉
    @Value("${whiskerguard.ai.rag.min-score:0.6}")
    private Float minScore;

    // RAG 调用超时时间（毫秒）
    @Value("${whiskerguard.ai.rag.timeout:2000}") // 降低到2秒
    private long ragTimeout;

    /**
     * 构造函数
     */
    public RagHelperEnhanced(
        RetrievalServiceClient retrievalServiceClient,
        @Autowired(required = false) AiResponseCacheService cacheService
    ) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.cacheService = cacheService;
    }

    /**
     * 使用RAG增强提示词 - 带缓存版本
     *
     * @param dto 原始调用请求
     * @return 增强后的提示词
     */
    @CircuitBreaker(name = "rag-service", fallbackMethod = "enhancePromptWithRagFallback")
    @Retry(name = "rag-service")
    public String enhancePromptWithRag(AiInvocationRequestDTO dto) {
        if (!ragEnabled) {
            log.debug("RAG功能已禁用，使用原始提示词");
            return dto.getPrompt();
        }

        long startTime = System.currentTimeMillis();

        try {
            // 1. 检查缓存
            if (cacheService != null) {
                String cachedResult = cacheService.getCachedRagResult(dto.getPrompt());
                if (cachedResult != null) {
                    log.info("RAG缓存命中，节省检索时间: {}ms", System.currentTimeMillis() - startTime);
                    return cachedResult;
                }
            }

            // 2. 获取租户ID
            String tenantId = dto.getTenantId() != null ? dto.getTenantId().toString() : defaultTenantId;

            log.debug(
                "开始调用 RAG 检索服务，TenantId: {}, Query: {}",
                tenantId,
                dto.getPrompt().length() > 50 ? dto.getPrompt().substring(0, 50) + "..." : dto.getPrompt()
            );

            // 3. 创建检索请求
            RetrieveRequestDTO retrieveRequest = createRetrieveRequest(dto.getPrompt());

            // 4. 调用检索服务（设置严格超时）
            RetrieveResponseDTO response = retrievalServiceClient.retrieve(tenantId, retrieveRequest);

            long retrievalTime = System.currentTimeMillis() - startTime;
            log.debug(
                "RAG 检索服务调用完成，耗时: {}ms，返回结果数量: {}",
                retrievalTime,
                response.getResults() != null ? response.getResults().size() : 0
            );

            // 5. 处理检索结果
            if (response.getResults() == null || response.getResults().isEmpty()) {
                log.debug("未检索到相关上下文，使用原始提示词");
                return dto.getPrompt();
            }

            // 6. 过滤低相似度的结果
            List<RetrieveResponseDTO.Result> filteredResults = response
                .getResults()
                .stream()
                .filter(result -> result.getScore() >= minScore)
                .collect(Collectors.toList());

            if (filteredResults.isEmpty()) {
                log.debug("检索结果相似度低于阈值 {}，使用原始提示词", minScore);
                return dto.getPrompt();
            }

            // 7. 构建增强提示词
            String enhancedPrompt = buildEnhancedPrompt(dto.getPrompt(), filteredResults);

            // 8. 缓存结果
            if (cacheService != null) {
                cacheService.cacheRagResult(dto.getPrompt(), enhancedPrompt);
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("RAG 增强成功，总耗时: {}ms，使用 {} 个检索结果增强提示词", totalTime, filteredResults.size());

            return enhancedPrompt;
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis() - startTime;
            log.warn("RAG 增强失败，耗时: {}ms，使用原始提示词继续处理: {}", errorTime, e.getMessage());
            return dto.getPrompt();
        }
    }

    /**
     * RAG 增强提示词的回退方法
     */
    public String enhancePromptWithRagFallback(AiInvocationRequestDTO dto) {
        log.warn("RAG 服务熔断器激活，使用原始提示词继续处理");
        return dto.getPrompt();
    }

    public String enhancePromptWithRagFallback(AiInvocationRequestDTO dto, Exception ex) {
        log.warn("RAG 服务熔断器激活或服务不可用，使用原始提示词继续处理: {}", ex != null ? ex.getMessage() : "熔断器打开");
        return dto.getPrompt();
    }

    /**
     * 创建检索请求对象
     */
    private RetrieveRequestDTO createRetrieveRequest(String query) {
        return new RetrieveRequestDTO(
            query,
            defaultTopK,
            defaultDistanceMetric,
            4096, // 减少上下文长度，提高处理速度
            "RAW",
            true,
            true
        );
    }

    /**
     * 构建增强提示词
     */
    private String buildEnhancedPrompt(String originalPrompt, List<RetrieveResponseDTO.Result> results) {
        StringBuilder enhancedPrompt = new StringBuilder();

        // 简化版本，减少构建时间
        enhancedPrompt.append("### 相关上下文：\n");

        // 只使用前3个结果，减少处理时间
        int maxResults = Math.min(results.size(), 3);
        for (int i = 0; i < maxResults; i++) {
            RetrieveResponseDTO.Result result = results.get(i);
            enhancedPrompt.append("参考 ").append(i + 1).append(": ");

            // 限制每个结果的长度，避免prompt过长
            String text = result.getText();
            if (text.length() > 500) {
                text = text.substring(0, 497) + "...";
            }
            enhancedPrompt.append(text).append("\n\n");
        }

        enhancedPrompt.append("### 问题：\n").append(originalPrompt).append("\n\n");
        enhancedPrompt.append("请基于上述上下文回答：\n");

        return enhancedPrompt.toString();
    }

    /**
     * 从元数据中提取来源信息
     */
    private String extractSourceFromMetadata(Map<String, Object> metadata) {
        if (metadata != null && metadata.containsKey("source")) {
            return metadata.get("source").toString();
        }
        return null;
    }
}
