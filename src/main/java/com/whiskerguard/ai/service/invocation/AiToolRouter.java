package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * AI 工具路由器：根据工具 key 和数据库配置，调度到对应的 Invoker
 * 支持流式和非流式调用
 */
@Service
public class AiToolRouter {

    private final Logger log = LoggerFactory.getLogger(AiToolRouter.class);
    private final Map<String, AiToolInvoker> invokers;
    private final AiToolRepository toolRepo;

    public AiToolRouter(List<AiToolInvoker> invokerList, AiToolRepository toolRepo) {
        this.invokers = invokerList.stream().collect(Collectors.toMap(AiToolInvoker::getToolKey, inv -> inv));
        this.toolRepo = toolRepo;
    }

    /**
     * 路由非流式调用请求
     *
     * @param dto 调用请求
     * @return 调用结果
     */
    public AiResult route(AiInvocationRequestDTO dto) {
        AiTool cfg = getToolConfig(dto.getToolKey());
        // 调用具体 Invoker
        return invokers.get(dto.getToolKey()).invoke(dto, cfg);
    }

    /**
     * 路由流式调用请求
     *
     * @param dto 流式调用请求
     * @return 流式响应
     */
    public Flux<AiStreamResponseDTO> routeStream(AiStreamRequestDTO dto) {
        AiTool cfg = getToolConfig(dto.getToolKey());
        AiToolInvoker invoker = invokers.get(dto.getToolKey());

        // 检查是否支持流式输出
        if (dto.isStreaming() && !invoker.supportsStreaming()) {
            log.warn("工具 {} 不支持流式输出，将使用非流式调用", dto.getToolKey());
        }

        // 调用具体 Invoker 的流式方法
        return invoker.invokeStream(dto, cfg);
    }

    /**
     * 获取工具配置并验证状态
     *
     * @param toolKey 工具标识
     * @return 工具配置
     */
    private AiTool getToolConfig(String toolKey) {
        AiTool cfg = toolRepo
            .findByToolKey(toolKey)
            .orElseThrow(() -> new BadRequestAlertException("Unknown AI tool: " + toolKey, "AiTool", "toolnotfound"));
        if (cfg.getStatus() != ToolStatus.AVAILABLE) {
            throw new BadRequestAlertException("AI tool disabled: " + toolKey, "AiTool", "tooldisabled");
        }
        return cfg;
    }
}
