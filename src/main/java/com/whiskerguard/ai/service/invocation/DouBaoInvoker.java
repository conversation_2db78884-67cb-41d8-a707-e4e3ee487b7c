/**
 * 豆包调用实现类
 * <p>
 * 负责与字节跳动的豆包 API 进行通信，支持 RAG 增强功能。
 */
package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Component
@SuppressWarnings("unchecked")
public class DouBaoInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(DouBaoInvoker.class);
    private final WebClient.Builder webClientBuilder;
    private final RagHelper ragHelper;
    private final ObjectMapper objectMapper;

    public DouBaoInvoker(WebClient.Builder webClientBuilder, RagHelper ragHelper, ObjectMapper objectMapper) {
        this.webClientBuilder = webClientBuilder;
        this.ragHelper = ragHelper;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String getToolKey() {
        return "doubao";
    }

    @Override
    @Bulkhead(name = "doubao", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "doubao")
    @Retry(name = "doubao")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        try {
            // 基本配置检查
            if (cfg.getApiKey() == null || cfg.getApiKey().trim().isEmpty()) {
                throw new IllegalArgumentException("豆包 API Key 未配置");
            }

            if (cfg.getApiUrl() == null || cfg.getApiUrl().trim().isEmpty()) {
                throw new IllegalArgumentException("豆包 API URL 未配置");
            }

            // 构建 WebClient，使用 Bearer Token 认证
            WebClient client = webClientBuilder
                .baseUrl(cfg.getApiUrl())
                .defaultHeader("Authorization", "Bearer " + cfg.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("豆包调用 - 原始提示词: {}", originalPrompt);
            log.debug("豆包调用 - 增强提示词: {}", enhancedPrompt);

            // 构建豆包 API 请求体 (OpenAI 兼容格式)
            Map<String, Object> body = new HashMap<>();
            body.put("model", "doubao-seed-1-6-250615"); // 使用您提供的模型名称

            // 构建 messages 列表
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", enhancedPrompt);
            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);
            body.put("messages", messages);

            // 添加其他可选参数
            if (dto.getMetadata() != null) {
                if (dto.getMetadata().containsKey("temperature")) {
                    body.put("temperature", dto.getMetadata().get("temperature"));
                }
                if (dto.getMetadata().containsKey("max_tokens")) {
                    body.put("max_tokens", dto.getMetadata().get("max_tokens"));
                }
                if (dto.getMetadata().containsKey("top_p")) {
                    body.put("top_p", dto.getMetadata().get("top_p"));
                }
            }

            log.debug("豆包请求体: {}", body);

            long start = System.currentTimeMillis();

            // 发送请求到豆包 API
            String path = cfg.getPath() != null ? cfg.getPath() : "/chat/completions";
            Map<String, Object> resp = client.post().uri(path).bodyValue(body).retrieve().bodyToMono(Map.class).block();

            long duration = System.currentTimeMillis() - start;

            log.debug("豆包响应: {}", resp);

            // 解析豆包响应 (OpenAI 兼容格式)
            if (resp == null) {
                throw new RuntimeException("豆包 API 返回空响应");
            }

            // 提取响应内容
            Object choicesObj = resp.get("choices");
            if (choicesObj == null) {
                throw new RuntimeException("豆包 API 响应中缺少 choices 字段");
            }

            List<Map<String, Object>> choices = (List<Map<String, Object>>) choicesObj;
            if (choices.isEmpty()) {
                throw new RuntimeException("豆包 API 响应中 choices 数组为空");
            }

            Map<String, Object> choice = choices.get(0);
            Map<String, Object> messageResp = (Map<String, Object>) choice.get("message");
            String content = (String) messageResp.get("content");

            // 提取使用量信息
            Map<String, Object> usage = (Map<String, Object>) resp.get("usage");
            if (usage == null) {
                usage = new HashMap<>();
                usage.put("total_tokens", 0);
                usage.put("prompt_tokens", 0);
                usage.put("completion_tokens", 0);
            }

            log.info("豆包 API 调用成功，耗时: {}ms, 使用量: {}", duration, usage);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            log.error("调用豆包 API 失败", e);
            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content("调用豆包 API 失败: " + e.getMessage()).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    @Bulkhead(name = "doubao-stream", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "doubao-stream")
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        try {
            // 基本配置检查
            if (cfg.getApiKey() == null || cfg.getApiKey().trim().isEmpty()) {
                return Flux.just(new AiStreamResponseDTO("豆包 API Key 未配置"));
            }

            if (cfg.getApiUrl() == null || cfg.getApiUrl().trim().isEmpty()) {
                return Flux.just(new AiStreamResponseDTO("豆包 API URL 未配置"));
            }

            // 构建 WebClient，使用 Bearer Token 认证
            WebClient client = webClientBuilder
                .baseUrl(cfg.getApiUrl())
                .defaultHeader("Authorization", "Bearer " + cfg.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(
                new AiInvocationRequestDTO(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getTenantId(), dto.getEmployeeId())
            );
            log.debug("豆包流式调用 - 原始提示词: {}", originalPrompt);
            log.debug("豆包流式调用 - 增强提示词: {}", enhancedPrompt);

            // 构建豆包 API 请求体 (OpenAI 兼容格式)
            Map<String, Object> body = new HashMap<>();
            body.put("model", "doubao-seed-1-6-250615"); // 使用您提供的模型名称

            // 构建 messages 列表
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", enhancedPrompt);
            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);
            body.put("messages", messages);
            body.put("stream", true); // 启用流式输出

            // 添加其他可选参数
            if (dto.getMetadata() != null) {
                if (dto.getMetadata().containsKey("temperature")) {
                    body.put("temperature", dto.getMetadata().get("temperature"));
                }
                if (dto.getMetadata().containsKey("max_tokens")) {
                    body.put("max_tokens", dto.getMetadata().get("max_tokens"));
                }
                if (dto.getMetadata().containsKey("top_p")) {
                    body.put("top_p", dto.getMetadata().get("top_p"));
                }
            }

            log.debug("豆包流式请求体: {}", body);

            // 用于累积完整响应以计算最终使用量
            AtomicReference<StringBuilder> fullResponseBuilder = new AtomicReference<>(new StringBuilder());

            // 发起流式请求
            String path = cfg.getPath() != null ? cfg.getPath() : "/chat/completions";
            return client
                .post()
                .uri(path)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .map(chunk -> {
                    try {
                        // 处理 SSE 格式的响应
                        if (chunk.startsWith("data: ")) {
                            chunk = chunk.substring(6);
                        }

                        // 处理流结束标记
                        if ("[DONE]".equals(chunk.trim())) {
                            // 创建最终使用量统计
                            Map<String, Object> usage = new HashMap<>();
                            usage.put("total_tokens", fullResponseBuilder.get().length() / 4); // 粗略估计
                            usage.put("prompt_tokens", 0);
                            usage.put("completion_tokens", fullResponseBuilder.get().length() / 4);
                            return new AiStreamResponseDTO("", usage);
                        }

                        // 跳过空行
                        if (chunk.trim().isEmpty()) {
                            return new AiStreamResponseDTO("", false);
                        }

                        // 解析 JSON 响应 (OpenAI 兼容格式)
                        Map<String, Object> response = objectMapper.readValue(chunk, Map.class);

                        // 提取流式响应内容
                        Object choicesObj = response.get("choices");
                        if (choicesObj != null) {
                            List<Map<String, Object>> choices = (List<Map<String, Object>>) choicesObj;
                            if (!choices.isEmpty()) {
                                Map<String, Object> choice = choices.get(0);
                                Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                                if (delta != null) {
                                    String content = (String) delta.get("content");
                                    if (content != null) {
                                        fullResponseBuilder.get().append(content);
                                        return new AiStreamResponseDTO(content, false);
                                    }
                                }
                            }
                        }

                        // 如果没有找到内容，返回空内容
                        return new AiStreamResponseDTO("", false);
                    } catch (JsonProcessingException e) {
                        log.error("解析豆包流式响应失败: {}", chunk, e);
                        return new AiStreamResponseDTO("解析豆包流式响应失败: " + e.getMessage());
                    }
                })
                .onErrorResume(e -> {
                    log.error("豆包流式调用失败", e);
                    return Flux.just(new AiStreamResponseDTO("豆包流式调用失败: " + e.getMessage()));
                });
        } catch (Exception e) {
            log.error("豆包流式调用初始化失败", e);
            return Flux.just(new AiStreamResponseDTO("豆包流式调用初始化失败: " + e.getMessage()));
        }
    }
}
