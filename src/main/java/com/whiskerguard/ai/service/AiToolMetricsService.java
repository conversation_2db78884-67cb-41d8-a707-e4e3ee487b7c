/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolMetricsService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：AI工具指标管理服务接口，定义AI工具指标相关的业务操作
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.service.dto.AiToolMetricsDTO;
import com.whiskerguard.ai.service.workflow.AiWorkflowResult;
import java.time.Instant;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * AI工具指标管理服务接口
 * <p>
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AiToolMetrics}.
 * <p>
 * 定义了AI工具指标实体的增删改查以及统计分析等业务操作方法。
 * 支持收集和分析AI工具的使用情况和性能指标。
 */
public interface AiToolMetricsService {
    /**
     * 保存AI工具指标
     * <p>
     * Save a aiToolMetrics.
     *
     * @param aiToolMetricsDTO 要保存的实体。the entity to save.
     * @return 持久化后的实体。the persisted entity.
     */
    AiToolMetricsDTO save(AiToolMetricsDTO aiToolMetricsDTO);

    /**
     * 更新AI工具指标
     * <p>
     * Updates a aiToolMetrics.
     *
     * @param aiToolMetricsDTO 要更新的实体。the entity to update.
     * @return 持久化后的实体。the persisted entity.
     */
    AiToolMetricsDTO update(AiToolMetricsDTO aiToolMetricsDTO);

    /**
     * 部分更新AI工具指标
     * <p>
     * Partially updates a aiToolMetrics.
     *
     * @param aiToolMetricsDTO 要部分更新的实体。the entity to update partially.
     * @return 持久化后的实体。the persisted entity.
     */
    Optional<AiToolMetricsDTO> partialUpdate(AiToolMetricsDTO aiToolMetricsDTO);

    /**
     * 获取所有AI工具指标
     * <p>
     * Get all the aiToolMetrics.
     *
     * @param pageable 分页信息。the pagination information.
     * @return 实体列表。the list of entities.
     */
    Page<AiToolMetricsDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取AI工具指标
     * <p>
     * Get the "id" aiToolMetrics.
     *
     * @param id 实体的ID。the id of the entity.
     * @return 实体。the entity.
     */
    Optional<AiToolMetricsDTO> findOne(Long id);

    /**
     * 根据ID删除AI工具指标
     * <p>
     * Delete the "id" aiToolMetrics.
     *
     * @param id 要删除实体的ID。the id of the entity.
     */
    void delete(Long id);

    /**
     * 保存AI调用指标（用于非流式调用）
     * <p>
     * Save AI call metrics (for non-streaming calls).
     *
     * @param toolKey AI工具键。AI tool key.
     * @param result AI工作流结果。AI workflow result.
     * @param request AI请求记录。AI request record.
     */
    void saveAiCallMetrics(String toolKey, AiWorkflowResult result, AiRequest request);

    /**
     * 保存AI流式调用指标
     * <p>
     * Save AI streaming call metrics.
     *
     * @param toolKey AI工具键。AI tool key.
     * @param tenantId 租户ID。tenant ID.
     * @param request AI请求记录。AI request record.
     * @param responseTime 响应时间。response time.
     */
    void saveStreamCallMetrics(String toolKey, Long tenantId, AiRequest request, Instant responseTime);
}
