package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AgentTask}.
 */
public interface AgentTaskService {
    /**
     * Save a agentTask.
     *
     * @param agentTaskDTO the entity to save.
     * @return the persisted entity.
     */
    AgentTaskDTO save(AgentTaskDTO agentTaskDTO);

    /**
     * Updates a agentTask.
     *
     * @param agentTaskDTO the entity to update.
     * @return the persisted entity.
     */
    AgentTaskDTO update(AgentTaskDTO agentTaskDTO);

    /**
     * Partially updates a agentTask.
     *
     * @param agentTaskDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<AgentTaskDTO> partialUpdate(AgentTaskDTO agentTaskDTO);

    /**
     * Get all the agentTasks.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<AgentTaskDTO> findAll(Pageable pageable);

    /**
     * Get the "id" agentTask.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<AgentTaskDTO> findOne(Long id);

    /**
     * Delete the "id" agentTask.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
