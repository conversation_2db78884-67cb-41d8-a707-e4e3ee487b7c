package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.AgentConfigDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AgentConfig}.
 */
public interface AgentConfigService {
    /**
     * Save a agentConfig.
     *
     * @param agentConfigDTO the entity to save.
     * @return the persisted entity.
     */
    AgentConfigDTO save(AgentConfigDTO agentConfigDTO);

    /**
     * Updates a agentConfig.
     *
     * @param agentConfigDTO the entity to update.
     * @return the persisted entity.
     */
    AgentConfigDTO update(AgentConfigDTO agentConfigDTO);

    /**
     * Partially updates a agentConfig.
     *
     * @param agentConfigDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<AgentConfigDTO> partialUpdate(AgentConfigDTO agentConfigDTO);

    /**
     * Get all the agentConfigs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<AgentConfigDTO> findAll(Pageable pageable);

    /**
     * Get the "id" agentConfig.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<AgentConfigDTO> findOne(Long id);

    /**
     * Delete the "id" agentConfig.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
