package com.whiskerguard.ai.service.compliance;

import java.io.Serializable;

/**
 * 企业信息DTO
 * 用于传递企业背景信息，为法规到制度转换过程提供企业上下文
 */
public class CompanyInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String name; // 企业名称
    private String industry; // 所属行业
    private String scale; // 企业规模（大型/中型/小型）
    private String type; // 企业类型（国企/央企/民企等）
    private String regLocation; // 注册地
    private String businessScope; // 经营范围
    private Integer employeeCount; // 员工数量
    private String organizationalStructure; // 组织架构简述
    private String existingPolicies; // 现有制度概述
    private String complianceHistory; // 合规历史

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getScale() {
        return scale;
    }

    public void setScale(String scale) {
        this.scale = scale;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public String getOrganizationalStructure() {
        return organizationalStructure;
    }

    public void setOrganizationalStructure(String organizationalStructure) {
        this.organizationalStructure = organizationalStructure;
    }

    public String getExistingPolicies() {
        return existingPolicies;
    }

    public void setExistingPolicies(String existingPolicies) {
        this.existingPolicies = existingPolicies;
    }

    public String getComplianceHistory() {
        return complianceHistory;
    }

    public void setComplianceHistory(String complianceHistory) {
        this.complianceHistory = complianceHistory;
    }

    @Override
    public String toString() {
        return (
            "CompanyInfoDTO{" +
            "name='" +
            name +
            '\'' +
            ", industry='" +
            industry +
            '\'' +
            ", scale='" +
            scale +
            '\'' +
            ", type='" +
            type +
            '\'' +
            ", regLocation='" +
            regLocation +
            '\'' +
            ", businessScope='" +
            businessScope +
            '\'' +
            ", employeeCount=" +
            employeeCount +
            '}'
        );
    }
}
