package com.whiskerguard.ai.service.compliance.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.service.compliance.AsyncResultRepository;
import com.whiskerguard.ai.web.rest.dto.PolicyGenerationResponse;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

/**
 * 异步结果存储库的Redis实现
 * 用于在Redis中存储和检索异步任务的结果
 */
@Repository
public class RedisAsyncResultRepository implements AsyncResultRepository {

    private final Logger log = LoggerFactory.getLogger(RedisAsyncResultRepository.class);
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;

    // Redis中存储政策生成结果的键前缀
    private static final String KEY_PREFIX = "policy-generation:result:";
    // 结果在Redis中的过期时间（24小时）
    private static final Duration RESULT_EXPIRATION = Duration.ofHours(24);

    /**
     * 构造函数，注入RedissonClient和ObjectMapper
     *
     * @param redissonClient Redisson客户端
     * @param objectMapper JSON序列化/反序列化工具
     */
    public RedisAsyncResultRepository(RedissonClient redissonClient, ObjectMapper objectMapper) {
        this.redissonClient = redissonClient;
        this.objectMapper = objectMapper;
        log.info("Redis异步结果存储库已初始化");
    }

    /**
     * 保存异步任务结果
     *
     * @param taskId 任务ID
     * @param response 政策生成响应
     */
    @Override
    public void save(String taskId, PolicyGenerationResponse response) {
        try {
            String key = generateKey(taskId);
            String value = objectMapper.writeValueAsString(response);

            RBucket<String> bucket = redissonClient.getBucket(key);
            bucket.set(value, RESULT_EXPIRATION.toMillis(), TimeUnit.MILLISECONDS);

            log.debug("已保存任务 {} 的结果到Redis", taskId);
        } catch (JsonProcessingException e) {
            log.error("保存任务结果时发生序列化错误", e);
            throw new RuntimeException("保存异步任务结果失败", e);
        }
    }

    /**
     * 保存异步任务错误信息
     *
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     */
    @Override
    public void saveError(String taskId, String errorMessage) {
        PolicyGenerationResponse response = PolicyGenerationResponse.failed(errorMessage);
        save(taskId, response);
    }

    /**
     * 根据任务ID获取结果
     *
     * @param taskId 任务ID
     * @return 政策生成响应，如果任务尚未完成或不存在则返回null
     */
    @Override
    public PolicyGenerationResponse findById(String taskId) {
        String key = generateKey(taskId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        String value = bucket.get();

        if (value == null) {
            return null;
        }

        try {
            return objectMapper.readValue(value, PolicyGenerationResponse.class);
        } catch (JsonProcessingException e) {
            log.error("读取任务结果时发生反序列化错误", e);
            throw new RuntimeException("获取异步任务结果失败", e);
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 如果任务存在则返回true
     */
    @Override
    public boolean exists(String taskId) {
        String key = generateKey(taskId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.isExists();
    }

    /**
     * 删除任务结果
     *
     * @param taskId 任务ID
     */
    @Override
    public void delete(String taskId) {
        String key = generateKey(taskId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.delete();
        log.debug("已删除任务 {} 的结果", taskId);
    }

    /**
     * 生成Redis键
     *
     * @param taskId 任务ID
     * @return Redis键
     */
    private String generateKey(String taskId) {
        return KEY_PREFIX + taskId;
    }
}
