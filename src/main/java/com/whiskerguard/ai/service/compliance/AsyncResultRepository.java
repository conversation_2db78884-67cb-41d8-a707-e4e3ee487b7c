package com.whiskerguard.ai.service.compliance;

import com.whiskerguard.ai.web.rest.dto.PolicyGenerationResponse;

/**
 * 异步结果存储库接口
 * 用于存储和检索异步任务的结果
 */
public interface AsyncResultRepository {
    /**
     * 保存异步任务结果
     *
     * @param taskId 任务ID
     * @param response 政策生成响应
     */
    void save(String taskId, PolicyGenerationResponse response);

    /**
     * 保存异步任务错误信息
     *
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     */
    void saveError(String taskId, String errorMessage);

    /**
     * 根据任务ID获取结果
     *
     * @param taskId 任务ID
     * @return 政策生成响应，如果任务尚未完成或不存在则返回null
     */
    PolicyGenerationResponse findById(String taskId);

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 如果任务存在则返回true
     */
    boolean exists(String taskId);

    /**
     * 删除任务结果
     *
     * @param taskId 任务ID
     */
    void delete(String taskId);
}
