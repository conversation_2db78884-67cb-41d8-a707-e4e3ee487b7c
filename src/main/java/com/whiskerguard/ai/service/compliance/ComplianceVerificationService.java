package com.whiskerguard.ai.service.compliance;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 合规验证服务
 * 负责验证生成内容的合规性，包括法规条款引用验证、合规度评估和专业术语标准化
 */
@Service
public class ComplianceVerificationService {

    private final Logger log = LoggerFactory.getLogger(ComplianceVerificationService.class);

    // 敏感词列表（示例，实际应从配置或数据库加载）
    private final Set<String> sensitiveWords = new HashSet<>(Arrays.asList("违法", "违规", "走后门", "潜规则", "暗箱操作"));

    // 常见法规引用模式（示例）
    private final Pattern legalReferencePattern = Pattern.compile("《([^》]+)》\\s*第([\\d十百千万亿]+)条");

    // 专业术语标准化映射（示例，实际应从配置或数据库加载）
    private final Map<String, String> termStandardization = new HashMap<>();

    /**
     * 构造函数，初始化术语标准化映射
     */
    public ComplianceVerificationService() {
        // 初始化术语标准化映射（示例数据）
        termStandardization.put("董事会", "董事会");
        termStandardization.put("董事局", "董事会");
        termStandardization.put("总经理", "总经理");
        termStandardization.put("总裁", "总经理");
        termStandardization.put("CEO", "总经理");
        log.info("ComplianceVerificationService已初始化");
    }

    /**
     * 验证生成的内容
     *
     * @param content 需要验证的内容
     * @param legalText 原始法规文本
     * @param companyInfo 企业信息
     * @return 验证结果
     */
    public VerificationResult verify(String content, String legalText, CompanyInfoDTO companyInfo) {
        log.debug("开始验证生成内容的合规性");

        // 创建验证结果对象
        VerificationResult result = new VerificationResult();
        result.setOriginalContent(content);

        // 敏感词过滤
        String filteredContent = filterSensitiveWords(content);

        // 法规条款引用验证
        List<LegalReference> references = validateLegalReferences(filteredContent, legalText);
        result.setLegalReferences(references);

        // 专业术语标准化
        String standardizedContent = standardizeTerminology(filteredContent);
        result.setVerifiedContent(standardizedContent);

        // 考虑企业特定信息进行合规度评估（当前未实际使用，但保留参数以便将来扩展）
        double complianceScore = evaluateCompliance(standardizedContent, legalText, references);
        result.setComplianceScore(complianceScore);

        // 生成注解
        Map<String, String> annotations = generateAnnotations(standardizedContent, references);
        result.setAnnotations(annotations);

        log.info("内容验证完成，合规度评分: {}", complianceScore);
        return result;
    }

    /**
     * 敏感词过滤
     *
     * @param content 原始内容
     * @return 过滤后的内容
     */
    private String filterSensitiveWords(String content) {
        String filteredContent = content;
        for (String word : sensitiveWords) {
            filteredContent = filteredContent.replaceAll(word, "***");
        }
        return filteredContent;
    }

    /**
     * 验证法规条款引用
     *
     * @param content 内容
     * @param legalText 原始法规文本
     * @return 法规引用列表
     */
    private List<LegalReference> validateLegalReferences(String content, String legalText) {
        List<LegalReference> references = new ArrayList<>();

        // 查找所有法规引用
        Matcher matcher = legalReferencePattern.matcher(content);
        while (matcher.find()) {
            String lawName = matcher.group(1);
            String articleNumber = matcher.group(2);

            LegalReference reference = new LegalReference();
            reference.setLawName(lawName);
            reference.setArticleNumber(articleNumber);
            reference.setOriginalText(matcher.group(0));
            reference.setValid(legalText.contains(lawName) && legalText.contains("第" + articleNumber + "条"));

            references.add(reference);
            log.debug("找到法规引用: {} 第{}条, 有效性: {}", lawName, articleNumber, reference.isValid());
        }

        return references;
    }

    /**
     * 专业术语标准化
     *
     * @param content 原始内容
     * @return 标准化后的内容
     */
    private String standardizeTerminology(String content) {
        String standardized = content;

        for (Map.Entry<String, String> entry : termStandardization.entrySet()) {
            if (!entry.getKey().equals(entry.getValue())) {
                standardized = standardized.replaceAll(entry.getKey(), entry.getValue());
            }
        }

        return standardized;
    }

    /**
     * 合规度评估
     *
     * @param content 内容
     * @param legalText 原始法规文本（当前未实际使用，但保留参数以便将来扩展）
     * @param references 法规引用列表
     * @return 合规度分数（0-100）
     */
    private double evaluateCompliance(String content, String legalText, List<LegalReference> references) {
        // 基础分数
        double baseScore = 70.0;

        // 根据有效法规引用数量加分
        long validReferences = references.stream().filter(LegalReference::isValid).count();
        double referenceScore = Math.min(validReferences * 5.0, 20.0);

        // 非法内容扣分（简化实现）
        double penaltyScore = 0.0;
        for (String word : sensitiveWords) {
            if (content.contains(word)) {
                penaltyScore += 10.0;
            }
        }

        // 计算最终分数
        double finalScore = baseScore + referenceScore - penaltyScore;
        return Math.max(0.0, Math.min(100.0, finalScore));
    }

    /**
     * 生成注解
     *
     * @param content 内容
     * @param references 法规引用列表
     * @return 注解映射（位置 -> 注解内容）
     */
    private Map<String, String> generateAnnotations(String content, List<LegalReference> references) {
        Map<String, String> annotations = new HashMap<>();

        // 为每个法规引用生成注解
        for (LegalReference reference : references) {
            String originalText = reference.getOriginalText();
            int position = content.indexOf(originalText);
            if (position >= 0) {
                String key = position + ":" + (position + originalText.length());
                String value;

                if (reference.isValid()) {
                    value = "引用自《" + reference.getLawName() + "》第" + reference.getArticleNumber() + "条";
                } else {
                    value = "警告：未验证的法规引用《" + reference.getLawName() + "》第" + reference.getArticleNumber() + "条";
                }

                annotations.put(key, value);
            }
        }

        return annotations;
    }

    /**
     * 法规引用类
     */
    public static class LegalReference {

        private String lawName;
        private String articleNumber;
        private String originalText;
        private boolean valid;

        public String getLawName() {
            return lawName;
        }

        public void setLawName(String lawName) {
            this.lawName = lawName;
        }

        public String getArticleNumber() {
            return articleNumber;
        }

        public void setArticleNumber(String articleNumber) {
            this.articleNumber = articleNumber;
        }

        public String getOriginalText() {
            return originalText;
        }

        public void setOriginalText(String originalText) {
            this.originalText = originalText;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }
    }

    /**
     * 验证结果类
     */
    public static class VerificationResult {

        private String originalContent;
        private String verifiedContent;
        private double complianceScore;
        private List<LegalReference> legalReferences;
        private Map<String, String> annotations;

        // 所有的getter和setter方法虽然部分当前未使用，
        // 但作为DTO类的完整API保留，以便外部依赖和序列化使用
        public String getOriginalContent() {
            return originalContent;
        }

        public void setOriginalContent(String originalContent) {
            this.originalContent = originalContent;
        }

        public String getVerifiedContent() {
            return verifiedContent;
        }

        public void setVerifiedContent(String verifiedContent) {
            this.verifiedContent = verifiedContent;
        }

        public double getComplianceScore() {
            return complianceScore;
        }

        public void setComplianceScore(double complianceScore) {
            this.complianceScore = complianceScore;
        }

        public List<LegalReference> getLegalReferences() {
            return legalReferences;
        }

        public void setLegalReferences(List<LegalReference> legalReferences) {
            this.legalReferences = legalReferences;
        }

        public Map<String, String> getAnnotations() {
            return annotations;
        }

        public void setAnnotations(Map<String, String> annotations) {
            this.annotations = annotations;
        }
    }
}
