package com.whiskerguard.ai.service.workflow;

import java.io.Serializable;
import java.util.Map;

/**
 * Workflow 执行完成后返回的结果模型
 */
public class AiWorkflowResult implements Serializable {

    private String content;
    private Map<String, Object> usage;
    private long durationMs;

    public AiWorkflowResult() {}

    public AiWorkflowResult(String content, Map<String, Object> usage) {
        this.content = content;
        this.usage = usage;
    }

    public AiWorkflowResult(String content, Map<String, Object> usage, long durationMs) {
        this.content = content;
        this.usage = usage;
        this.durationMs = durationMs;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Map<String, Object> getUsage() {
        return usage;
    }

    public void setUsage(Map<String, Object> usage) {
        this.usage = usage;
    }

    public long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(long durationMs) {
        this.durationMs = durationMs;
    }
}
