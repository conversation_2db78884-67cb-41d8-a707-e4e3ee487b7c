package com.whiskerguard.ai.service.workflow;

import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.invocation.AiResult;
import com.whiskerguard.ai.service.invocation.AiToolRouter;
import io.temporal.activity.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Activity 实现：封装预处理、调用、后处理的业务逻辑
 *
 * 优化说明：
 * 1. 添加详细的日志记录用于问题排查
 * 2. 增强错误处理和异常信息
 * 3. 添加活动执行时间监控
 */
@Component
public class AiTaskActivitiesImpl implements AiTaskActivities {

    private static final Logger log = LoggerFactory.getLogger(AiTaskActivitiesImpl.class);

    private final AiToolRouter router;
    private final AiRequestRepository requestRepo;

    public AiTaskActivitiesImpl(AiToolRouter router, AiRequestRepository requestRepo) {
        this.router = router;
        this.requestRepo = requestRepo;
    }

    @Override
    public PreprocessResult preprocess(AiInvocationRequestDTO dto) {
        long startTime = System.currentTimeMillis();
        String activityId = Activity.getExecutionContext().getInfo().getActivityId();

        log.debug("开始预处理活动，ActivityId: {}, ToolKey: {}", activityId, dto.getToolKey());

        try {
            // 简单示例：直接包装，亦可加清洗/校验逻辑
            PreprocessResult result = new PreprocessResult(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getEmployeeId());

            long duration = System.currentTimeMillis() - startTime;
            log.debug("预处理活动完成，ActivityId: {}, 耗时: {} ms", activityId, duration);

            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("预处理活动失败，ActivityId: {}, 耗时: {} ms, 错误: {}", activityId, duration, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public AiResult invokeTool(PreprocessResult pre) {
        long startTime = System.currentTimeMillis();
        String activityId = Activity.getExecutionContext().getInfo().getActivityId();

        log.info("开始 AI 工具调用活动，ActivityId: {}, ToolKey: {}", activityId, pre.getToolKey());

        try {
            // 发送心跳，告知 Temporal 活动正在执行
            Activity.getExecutionContext().heartbeat("开始调用 AI 工具: " + pre.getToolKey());

            // 调用通用路由器
            // 这里把 PreprocessResult 转为 AiInvocationRequestDTO 以复用
            AiInvocationRequestDTO dto = new AiInvocationRequestDTO(
                pre.getToolKey(),
                pre.getPrompt(),
                pre.getMetadata(),
                null,
                pre.getEmployeeId()
            );

            // 在调用前再次发送心跳
            Activity.getExecutionContext().heartbeat("正在调用 AI 工具: " + pre.getToolKey());

            AiResult result = router.route(dto);

            // 调用完成后发送心跳
            Activity.getExecutionContext().heartbeat("AI 工具调用完成: " + pre.getToolKey());

            long duration = System.currentTimeMillis() - startTime;
            log.info(
                "AI 工具调用活动完成，ActivityId: {}, ToolKey: {}, 耗时: {} ms, 响应长度: {} 字符",
                activityId,
                pre.getToolKey(),
                duration,
                result.getContent() != null ? result.getContent().length() : 0
            );

            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error(
                "AI 工具调用活动失败，ActivityId: {}, ToolKey: {}, 耗时: {} ms, 错误: {}",
                activityId,
                pre.getToolKey(),
                duration,
                e.getMessage(),
                e
            );

            // 即使失败也发送心跳，确保 Temporal 知道活动状态
            try {
                Activity.getExecutionContext().heartbeat("AI 工具调用失败: " + e.getMessage());
            } catch (Exception heartbeatException) {
                log.warn("发送心跳失败: {}", heartbeatException.getMessage());
            }

            throw e;
        }
    }

    @Override
    public AiWorkflowResult postprocess(AiResult aiResult) {
        long startTime = System.currentTimeMillis();
        String activityId = Activity.getExecutionContext().getInfo().getActivityId();

        log.debug("开始后处理活动，ActivityId: {}", activityId);

        try {
            // 示例：直接转换成 WorkflowResult
            AiWorkflowResult result = new AiWorkflowResult(aiResult.getContent(), aiResult.getUsage());

            long duration = System.currentTimeMillis() - startTime;
            log.debug("后处理活动完成，ActivityId: {}, 耗时: {} ms", activityId, duration);

            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("后处理活动失败，ActivityId: {}, 耗时: {} ms, 错误: {}", activityId, duration, e.getMessage(), e);
            throw e;
        }
    }
}
