package com.whiskerguard.ai.service.workflow;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.invocation.AiResult;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.workflow.Workflow;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Temporal Workflow 实现：按顺序执行各个 Activity
 *
 * 优化说明：
 * 1. 增加活动超时时间到 5 分钟，适应 AI 工具调用的长时间响应
 * 2. 优化重试策略，使用指数退避算法
 * 3. 添加详细的日志记录用于问题排查
 */
public class AiWorkflowImpl implements AiWorkflow {

    private static final Logger log = LoggerFactory.getLogger(AiWorkflowImpl.class);

    private final AiTaskActivities activities;

    public AiWorkflowImpl() {
        this.activities = Workflow.newActivityStub(
            AiTaskActivities.class,
            ActivityOptions.newBuilder()
                // 优化超时时间到 2 分钟，提高响应速度
                .setStartToCloseTimeout(Duration.ofMinutes(2))
                // 设置调度到开始的超时时间为 30 秒
                .setScheduleToStartTimeout(Duration.ofSeconds(30))
                // 设置心跳超时为 30 秒，更频繁的心跳检测
                .setHeartbeatTimeout(Duration.ofSeconds(30))
                // 优化重试策略，快速失败
                .setRetryOptions(
                    RetryOptions.newBuilder()
                        .setInitialInterval(Duration.ofSeconds(1)) // 初始重试间隔 1 秒
                        .setMaximumInterval(Duration.ofSeconds(10)) // 最大重试间隔 10 秒
                        .setBackoffCoefficient(2.0) // 指数退避系数
                        .setMaximumAttempts(2) // 最多重试 2 次
                        .build()
                )
                .build()
        );
    }

    @Override
    public AiWorkflowResult execute(AiInvocationRequestDTO dto) {
        long startTime = System.currentTimeMillis();
        String workflowId = Workflow.getInfo().getWorkflowId();

        log.info("开始执行 AI 工作流，WorkflowId: {}, ToolKey: {}", workflowId, dto.getToolKey());

        try {
            // 1. 预处理
            log.debug("开始预处理阶段，WorkflowId: {}", workflowId);
            PreprocessResult pre = activities.preprocess(dto);
            log.debug("预处理完成，WorkflowId: {}, ToolKey: {}", workflowId, pre.getToolKey());

            // 2. 调用 LLM
            log.info("开始调用 AI 工具，WorkflowId: {}, ToolKey: {}", workflowId, pre.getToolKey());
            AiResult ai = activities.invokeTool(pre);
            log.info(
                "AI 工具调用完成，WorkflowId: {}, 响应长度: {} 字符",
                workflowId,
                ai.getContent() != null ? ai.getContent().length() : 0
            );

            // 3. 后处理
            log.debug("开始后处理阶段，WorkflowId: {}", workflowId);
            AiWorkflowResult result = activities.postprocess(ai);

            // 设置总执行时间
            long duration = System.currentTimeMillis() - startTime;
            result.setDurationMs(duration);

            log.info("AI 工作流执行完成，WorkflowId: {}, 总耗时: {} ms", workflowId, duration);
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("AI 工作流执行失败，WorkflowId: {}, 耗时: {} ms, 错误: {}", workflowId, duration, e.getMessage(), e);
            throw e;
        }
    }
}
