package com.whiskerguard.ai.service.workflow;

import java.io.Serializable;
import java.util.Map;

/**
 * Workflow 的预处理结果，用于 Activity 之间传递参数
 */
public class PreprocessResult implements Serializable {

    private String toolKey;
    private String prompt;
    private Map<String, Object> metadata;
    private Long employeeId;

    public PreprocessResult() {}

    public PreprocessResult(String toolKey, String prompt, Map<String, Object> metadata) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
    }

    public PreprocessResult(String toolKey, String prompt, Map<String, Object> metadata, Long employeeId) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.employeeId = employeeId;
    }

    public String getToolKey() {
        return toolKey;
    }

    public void setToolKey(String toolKey) {
        this.toolKey = toolKey;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }
}
