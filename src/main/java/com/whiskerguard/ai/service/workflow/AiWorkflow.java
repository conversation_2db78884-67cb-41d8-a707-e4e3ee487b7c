package com.whiskerguard.ai.service.workflow;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

/**
 * Temporal Workflow 接口，定义业务编排契约
 */
@WorkflowInterface
public interface AiWorkflow {
    /**
     * 运行 AI 工作流：预处理 → 调用 → 后处理
     */
    @WorkflowMethod
    AiWorkflowResult execute(AiInvocationRequestDTO dto);
}
