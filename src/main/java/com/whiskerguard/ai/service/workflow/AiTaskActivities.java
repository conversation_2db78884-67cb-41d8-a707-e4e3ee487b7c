package com.whiskerguard.ai.service.workflow;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.invocation.AiResult;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * 定义工作流可以执行的各个活动（Activity）方法
 */
@ActivityInterface
public interface AiTaskActivities {
    /**
     * 预处理输入请求，返回预处理结果
     */
    @ActivityMethod
    PreprocessResult preprocess(AiInvocationRequestDTO dto);

    /**
     * 调用 AI 工具，返回 AiResult
     */
    @ActivityMethod
    AiResult invokeTool(PreprocessResult pre);

    /**
     * 后处理 AI 调用结果，返回最终 WorkflowResult
     */
    @ActivityMethod
    AiWorkflowResult postprocess(AiResult aiResult);
}
