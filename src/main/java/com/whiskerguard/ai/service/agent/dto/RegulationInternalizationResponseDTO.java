/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulationInternalizationResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：外规内化响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 外规内化响应DTO
 * <p>
 * 外规内化功能的响应结果。
 * 包含生成的内部制度和相关建议。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "外规内化响应")
public class RegulationInternalizationResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 法规ID
     */
    @Schema(description = "法规ID", example = "REG_2024_001")
    private String regulationId;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID", example = "1")
    private Long companyId;

    /**
     * 行业类型
     */
    @Schema(description = "行业类型", example = "电力")
    private String industryType;

    /**
     * 生成的内部制度
     */
    @Schema(description = "生成的内部制度")
    private String internalPolicy;

    /**
     * 合规性验证结果
     */
    @Schema(description = "合规性验证结果")
    private String complianceValidation;

    /**
     * 实施建议
     */
    @Schema(description = "实施建议")
    private List<String> recommendations;

    /**
     * 风险提示
     */
    @Schema(description = "风险提示")
    private List<String> riskWarnings;

    /**
     * 参考资料
     */
    @Schema(description = "参考资料")
    private List<String> references;
}

// 构造函数
    public RegulationInternalizationResponseDTO() {}
    
    // TODO: 添加getter和setter方法
