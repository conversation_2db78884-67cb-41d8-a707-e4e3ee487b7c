/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：合同审查响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 合同审查响应DTO
 * <p>
 * 合同审查功能的响应结果。
 * 包含审查结果和风险评估。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "合同审查响应")
public class ContractReviewResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID", example = "CON_2024_001")
    private String contractId;

    /**
     * 合同类型
     */
    @Schema(description = "合同类型", example = "采购合同")
    private String contractType;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID", example = "1")
    private Long companyId;

    /**
     * 关联方审查结果
     */
    @Schema(description = "关联方审查结果")
    private String partyReview;

    /**
     * 条款分析结果
     */
    @Schema(description = "条款分析结果")
    private String clauseAnalysis;

    /**
     * 法律合规性检查结果
     */
    @Schema(description = "法律合规性检查结果")
    private String legalComplianceCheck;

    /**
     * 内部制度符合性检查结果
     */
    @Schema(description = "内部制度符合性检查结果")
    private String internalPolicyCheck;

    /**
     * 内部流程符合性检查结果
     */
    @Schema(description = "内部流程符合性检查结果")
    private String processComplianceCheck;

    /**
     * 风险点列表
     */
    @Schema(description = "风险点列表")
    private List<String> riskPoints;

    /**
     * 风险等级
     */
    @Schema(description = "风险等级", example = "中等")
    private String riskLevel;

    /**
     * 审查建议列表
     */
    @Schema(description = "审查建议列表")
    private List<String> recommendations;

    /**
     * 整体评分（0-100）
     */
    @Schema(description = "整体评分", example = "78")
    private Integer overallScore;

    /**
     * 审查总结
     */
    @Schema(description = "审查总结")
    private String reviewSummary;

    /**
     * 签署建议
     */
    @Schema(description = "签署建议", example = "建议修改部分条款后签署")
    private String signingRecommendation;
}

