/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：制度审查请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 制度审查请求DTO
 * <p>
 * 用于制度审查功能的请求参数。
 * 包含制度信息、企业信息等必要参数。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "制度审查请求")
public class PolicyReviewRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    /**
     * 制度ID
     */
    @NotBlank(message = "制度ID不能为空")
    @Schema(description = "制度ID", required = true, example = "POL_2024_001")
    private String policyId;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    @Schema(description = "企业ID", required = true, example = "1")
    private Long companyId;

    /**
     * 制度类型
     */
    @NotBlank(message = "制度类型不能为空")
    @Schema(description = "制度类型", required = true, example = "安全生产制度")
    private String policyType;

    /**
     * 行业类型
     */
    @Schema(description = "行业类型", example = "电力")
    private String industryType;

    /**
     * 制度内容
     */
    @NotBlank(message = "制度内容不能为空")
    @Schema(description = "制度内容", required = true)
    private String policyContent;

    /**
     * 审查重点
     */
    @Schema(description = "审查重点")
    private String reviewFocus;

    /**
     * 特殊要求
     */
    @Schema(description = "特殊要求")
    private String specialRequirements;
}

