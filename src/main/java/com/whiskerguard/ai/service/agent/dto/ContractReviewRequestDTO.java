/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：合同审查请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * 合同审查请求DTO
 * <p>
 * 用于合同审查功能的请求参数。
 * 包含合同信息、企业信息等必要参数。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "合同审查请求")
public class ContractReviewRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    /**
     * 合同ID
     */
    @NotBlank(message = "合同ID不能为空")
    @Schema(description = "合同ID", required = true, example = "CON_2024_001")
    private String contractId;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    @Schema(description = "企业ID", required = true, example = "1")
    private Long companyId;

    /**
     * 合同类型
     */
    @NotBlank(message = "合同类型不能为空")
    @Schema(description = "合同类型", required = true, example = "采购合同")
    private String contractType;

    /**
     * 行业类型
     */
    @Schema(description = "行业类型", example = "电力")
    private String industryType;

    /**
     * 合同内容
     */
    @NotBlank(message = "合同内容不能为空")
    @Schema(description = "合同内容", required = true)
    private String contractContent;

    /**
     * 合同金额
     */
    @Schema(description = "合同金额", example = "1000000.00")
    private Double contractAmount;

    /**
     * 审查重点
     */
    @Schema(description = "审查重点")
    private String reviewFocus;

    /**
     * 特殊要求
     */
    @Schema(description = "特殊要求")
    private String specialRequirements;
}

// 构造函数
    public ContractReviewRequestDTO() {}
    
    // TODO: 添加getter和setter方法
