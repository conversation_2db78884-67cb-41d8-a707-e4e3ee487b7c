/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent
 * 描    述：合规智能体核心服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.agent.business.ContractReviewAgentService;
import com.whiskerguard.ai.service.agent.business.PolicyReviewAgentService;
import com.whiskerguard.ai.service.agent.business.RegulationInternalizationAgentService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorService;
import com.whiskerguard.ai.service.agent.dto.*;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import com.whiskerguard.ai.util.SecurityUtils;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 合规智能体核心服务
 * <p>
 * 提供统一的Agent服务入口，协调各个业务Agent的执行。
 * 负责任务创建、状态管理、结果整合等核心功能。
 * 
 * 主要功能：
 * 1. 任务创建和管理
 * 2. 业务Agent调度
 * 3. 执行状态跟踪
 * 4. 结果整合返回
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class ComplianceAgentService {

    private static final Logger log = LoggerFactory.getLogger(ComplianceAgentService.class);

    private final AgentTaskRepository agentTaskRepository;
    private final AgentTaskMapper agentTaskMapper;
    private final TaskOrchestratorService taskOrchestratorService;
    private final RegulationInternalizationAgentService regulationInternalizationService;
    private final PolicyReviewAgentService policyReviewService;
    private final ContractReviewAgentService contractReviewService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public ComplianceAgentService(
            AgentTaskRepository agentTaskRepository,
            AgentTaskMapper agentTaskMapper,
            TaskOrchestratorService taskOrchestratorService,
            RegulationInternalizationAgentService regulationInternalizationService,
            PolicyReviewAgentService policyReviewService,
            ContractReviewAgentService contractReviewService) {
        this.agentTaskRepository = agentTaskRepository;
        this.agentTaskMapper = agentTaskMapper;
        this.taskOrchestratorService = taskOrchestratorService;
        this.regulationInternalizationService = regulationInternalizationService;
        this.policyReviewService = policyReviewService;
        this.contractReviewService = contractReviewService;
    }

    /**
     * 创建Agent任务
     * 
     * @param request 任务请求
     * @return 任务响应
     */
    public AgentTaskResponseDTO createTask(@Valid AgentTaskRequestDTO request) {
        log.info("创建Agent任务，类型: {}, 租户: {}", request.getTaskType(), request.getTenantId());

        try {
            // 1. 创建任务实体
            AgentTask agentTask = createAgentTaskEntity(request);
            agentTask = agentTaskRepository.save(agentTask);

            // 2. 异步执行任务
            CompletableFuture.runAsync(() -> executeTaskAsync(agentTask));

            // 3. 返回任务响应
            return buildTaskResponse(agentTask, null);

        } catch (Exception e) {
            log.error("创建Agent任务失败", e);
            throw new RuntimeException("创建Agent任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    @Transactional(readOnly = true)
    public AgentTaskStatusDTO getTaskStatus(Long taskId) {
        log.debug("获取任务状态，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.get();
        return AgentTaskStatusDTO.builder()
                .taskId(task.getId())
                .status(task.getStatus())
                .progress(task.getProgress())
                .startTime(task.getStartTime())
                .endTime(task.getEndTime())
                .errorMessage(task.getErrorMessage())
                .build();
    }

    /**
     * 获取任务结果
     * 
     * @param taskId 任务ID
     * @return 任务结果
     */
    @Transactional(readOnly = true)
    public AgentTaskResponseDTO getTaskResult(Long taskId) {
        log.debug("获取任务结果，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.get();
        return buildTaskResponse(task, task.getResponseData());
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     */
    public void cancelTask(Long taskId) {
        log.info("取消任务，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.get();
        if (task.getStatus() == AgentTaskStatus.RUNNING) {
            task.setStatus(AgentTaskStatus.CANCELLED);
            task.setEndTime(Instant.now());
            task.setUpdatedAt(Instant.now());
            task.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            agentTaskRepository.save(task);
        }
    }

    /**
     * 创建Agent任务实体
     */
    private AgentTask createAgentTaskEntity(AgentTaskRequestDTO request) {
        AgentTask agentTask = new AgentTask();
        agentTask.setTenantId(request.getTenantId());
        agentTask.setTaskType(request.getTaskType());
        agentTask.setTitle(request.getTitle());
        agentTask.setDescription(request.getDescription());
        agentTask.setStatus(AgentTaskStatus.PENDING);
        agentTask.setPriority(request.getPriority() != null ? request.getPriority() : TaskPriority.NORMAL);
        agentTask.setRequestData(request.getRequestData());
        agentTask.setProgress(0);
        agentTask.setVersion(1);
        agentTask.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setCreatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setIsDeleted(false);
        return agentTask;
    }

    /**
     * 异步执行任务
     */
    private void executeTaskAsync(AgentTask agentTask) {
        try {
            log.info("开始执行Agent任务，ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

            // 更新任务状态为运行中
            updateTaskStatus(agentTask, AgentTaskStatus.RUNNING, 10);

            // 根据任务类型调用相应的业务Agent
            String result = switch (agentTask.getTaskType()) {
                case REGULATION_INTERNALIZATION -> executeRegulationInternalization(agentTask);
                case POLICY_REVIEW -> executePolicyReview(agentTask);
                case CONTRACT_REVIEW -> executeContractReview(agentTask);
            };

            // 更新任务状态为完成
            updateTaskCompletion(agentTask, AgentTaskStatus.COMPLETED, result);

            log.info("Agent任务执行完成，ID: {}", agentTask.getId());

        } catch (Exception e) {
            log.error("Agent任务执行失败，ID: {}", agentTask.getId(), e);
            updateTaskCompletion(agentTask, AgentTaskStatus.FAILED, null);
            updateTaskError(agentTask, e.getMessage());
        }
    }

    /**
     * 执行外规内化任务
     */
    private String executeRegulationInternalization(AgentTask agentTask) {
        log.debug("执行外规内化任务，ID: {}", agentTask.getId());
        
        RegulationInternalizationRequestDTO request = 
            parseRequestData(agentTask.getRequestData(), RegulationInternalizationRequestDTO.class);
        
        RegulationInternalizationResponseDTO response = 
            regulationInternalizationService.processInternalization(request);
        
        return convertToJson(response);
    }

    /**
     * 执行制度审查任务
     */
    private String executePolicyReview(AgentTask agentTask) {
        log.debug("执行制度审查任务，ID: {}", agentTask.getId());
        
        PolicyReviewRequestDTO request = 
            parseRequestData(agentTask.getRequestData(), PolicyReviewRequestDTO.class);
        
        PolicyReviewResponseDTO response = 
            policyReviewService.reviewPolicy(request);
        
        return convertToJson(response);
    }

    /**
     * 执行合同审查任务
     */
    private String executeContractReview(AgentTask agentTask) {
        log.debug("执行合同审查任务，ID: {}", agentTask.getId());
        
        ContractReviewRequestDTO request = 
            parseRequestData(agentTask.getRequestData(), ContractReviewRequestDTO.class);
        
        ContractReviewResponseDTO response = 
            contractReviewService.reviewContract(request);
        
        return convertToJson(response);
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(AgentTask agentTask, AgentTaskStatus status, Integer progress) {
        agentTask.setStatus(status);
        if (progress != null) {
            agentTask.setProgress(progress);
        }
        if (status == AgentTaskStatus.RUNNING && agentTask.getStartTime() == null) {
            agentTask.setStartTime(Instant.now());
        }
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTaskRepository.save(agentTask);
    }

    /**
     * 更新任务完成状态
     */
    private void updateTaskCompletion(AgentTask agentTask, AgentTaskStatus status, String result) {
        agentTask.setStatus(status);
        agentTask.setProgress(100);
        agentTask.setEndTime(Instant.now());
        if (result != null) {
            agentTask.setResponseData(result);
        }
        if (agentTask.getStartTime() != null) {
            agentTask.setExecutionTime(
                agentTask.getEndTime().toEpochMilli() - agentTask.getStartTime().toEpochMilli());
        }
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTaskRepository.save(agentTask);
    }

    /**
     * 更新任务错误信息
     */
    private void updateTaskError(AgentTask agentTask, String errorMessage) {
        agentTask.setErrorMessage(errorMessage);
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTaskRepository.save(agentTask);
    }

    /**
     * 构建任务响应
     */
    private AgentTaskResponseDTO buildTaskResponse(AgentTask agentTask, String result) {
        return AgentTaskResponseDTO.builder()
                .taskId(agentTask.getId())
                .taskType(agentTask.getTaskType())
                .title(agentTask.getTitle())
                .status(agentTask.getStatus())
                .progress(agentTask.getProgress())
                .result(result)
                .startTime(agentTask.getStartTime())
                .endTime(agentTask.getEndTime())
                .executionTime(agentTask.getExecutionTime())
                .errorMessage(agentTask.getErrorMessage())
                .createdAt(agentTask.getCreatedAt())
                .build();
    }

    /**
     * 解析请求数据
     */
    private <T> T parseRequestData(String requestData, Class<T> clazz) {
        // TODO: 实现JSON解析逻辑
        return null;
    }

    /**
     * 转换为JSON字符串
     */
    private String convertToJson(Object object) {
        // TODO: 实现JSON转换逻辑
        return object.toString();
    }
}

