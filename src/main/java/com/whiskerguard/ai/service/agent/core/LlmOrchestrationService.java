/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LlmOrchestrationService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：LLM编排服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.service.invocation.AiInvocationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * LLM编排服务
 * <p>
 * 负责协调和编排多个LLM调用，实现复杂的AI任务处理。
 * 提供统一的LLM调用接口和结果整合能力。
 * 
 * 主要功能：
 * 1. LLM调用编排
 * 2. 多模型协同
 * 3. 结果整合
 * 4. 错误处理和重试
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class LlmOrchestrationService {

    private static final Logger log = LoggerFactory.getLogger(LlmOrchestrationService.class);

    private final AiInvocationService aiInvocationService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public LlmOrchestrationService(AiInvocationService aiInvocationService) {
        this.aiInvocationService = aiInvocationService;
    }

    /**
     * 单次LLM调用
     * 
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @return LLM响应
     */
    public String invokeLlm(Long tenantId, String toolType, String prompt, Map<String, Object> parameters) {
        log.debug("单次LLM调用，租户: {}, 工具类型: {}", tenantId, toolType);

        try {
            Map<String, Object> request = new HashMap<>();
            request.put("tenantId", tenantId);
            request.put("toolType", toolType);
            request.put("prompt", prompt);
            if (parameters != null) {
                request.putAll(parameters);
            }

            // TODO: 调用aiInvocationService
            return "LLM响应结果";

        } catch (Exception e) {
            log.error("LLM调用失败", e);
            throw new RuntimeException("LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量LLM调用
     * 
     * @param tenantId 租户ID
     * @param requests 请求列表
     * @return 响应列表
     */
    public List<String> batchInvokeLlm(Long tenantId, List<LlmRequest> requests) {
        log.debug("批量LLM调用，租户: {}, 请求数量: {}", tenantId, requests.size());

        try {
            return requests.stream()
                    .map(request -> invokeLlm(tenantId, request.getToolType(), request.getPrompt(), request.getParameters()))
                    .toList();

        } catch (Exception e) {
            log.error("批量LLM调用失败", e);
            throw new RuntimeException("批量LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步LLM调用
     * 
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @return 异步响应
     */
    public CompletableFuture<String> asyncInvokeLlm(Long tenantId, String toolType, String prompt, Map<String, Object> parameters) {
        log.debug("异步LLM调用，租户: {}, 工具类型: {}", tenantId, toolType);

        return CompletableFuture.supplyAsync(() -> {
            try {
                return invokeLlm(tenantId, toolType, prompt, parameters);
            } catch (Exception e) {
                log.error("异步LLM调用失败", e);
                throw new RuntimeException("异步LLM调用失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 链式LLM调用
     * 将前一个LLM的输出作为下一个LLM的输入
     * 
     * @param tenantId 租户ID
     * @param chainRequests 链式请求
     * @return 最终响应
     */
    public String chainInvokeLlm(Long tenantId, List<ChainLlmRequest> chainRequests) {
        log.debug("链式LLM调用，租户: {}, 链长度: {}", tenantId, chainRequests.size());

        try {
            String currentOutput = null;

            for (int i = 0; i < chainRequests.size(); i++) {
                ChainLlmRequest request = chainRequests.get(i);
                
                // 构建提示词，如果不是第一个请求，则包含前一个输出
                String prompt = request.getPrompt();
                if (i > 0 && currentOutput != null) {
                    prompt = request.getPrompt() + "\n\n【前一步输出】\n" + currentOutput;
                }

                // 调用LLM
                currentOutput = invokeLlm(tenantId, request.getToolType(), prompt, request.getParameters());
                
                log.debug("链式调用第{}步完成", i + 1);
            }

            return currentOutput;

        } catch (Exception e) {
            log.error("链式LLM调用失败", e);
            throw new RuntimeException("链式LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 并行LLM调用并整合结果
     * 
     * @param tenantId 租户ID
     * @param parallelRequests 并行请求
     * @param integrationPrompt 整合提示词
     * @return 整合后的响应
     */
    public String parallelInvokeLlmWithIntegration(Long tenantId, 
                                                  List<LlmRequest> parallelRequests, 
                                                  String integrationPrompt) {
        log.debug("并行LLM调用并整合，租户: {}, 并行数量: {}", tenantId, parallelRequests.size());

        try {
            // 1. 并行调用LLM
            List<CompletableFuture<String>> futures = parallelRequests.stream()
                    .map(request -> asyncInvokeLlm(tenantId, request.getToolType(), request.getPrompt(), request.getParameters()))
                    .toList();

            // 2. 等待所有调用完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join();

            // 3. 收集所有结果
            List<String> results = futures.stream()
                    .map(CompletableFuture::join)
                    .toList();

            // 4. 整合结果
            String combinedResults = String.join("\n\n", results);
            String finalPrompt = integrationPrompt + "\n\n【各部分结果】\n" + combinedResults;

            return invokeLlm(tenantId, "result_integration", finalPrompt, null);

        } catch (Exception e) {
            log.error("并行LLM调用并整合失败", e);
            throw new RuntimeException("并行LLM调用并整合失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带重试的LLM调用
     * 
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @param maxRetries 最大重试次数
     * @return LLM响应
     */
    public String invokeLlmWithRetry(Long tenantId, String toolType, String prompt, Map<String, Object> parameters, int maxRetries) {
        log.debug("带重试的LLM调用，租户: {}, 工具类型: {}, 最大重试次数: {}", tenantId, toolType, maxRetries);

        Exception lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return invokeLlm(tenantId, toolType, prompt, parameters);
            } catch (Exception e) {
                lastException = e;
                log.warn("LLM调用失败，尝试次数: {}/{}", attempt + 1, maxRetries + 1, e);
                
                if (attempt < maxRetries) {
                    // 等待一段时间后重试
                    try {
                        Thread.sleep(1000 * (attempt + 1)); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                }
            }
        }

        throw new RuntimeException("LLM调用重试失败，已达到最大重试次数: " + maxRetries, lastException);
    }

    /**
     * LLM请求对象
     */
    public static class LlmRequest {
        private String toolType;
        private String prompt;
        private Map<String, Object> parameters;

        public LlmRequest(String toolType, String prompt, Map<String, Object> parameters) {
            this.toolType = toolType;
            this.prompt = prompt;
            this.parameters = parameters;
        }

        // Getters
        public String getToolType() { return toolType; }
        public String getPrompt() { return prompt; }
        public Map<String, Object> getParameters() { return parameters; }
    }

    /**
     * 链式LLM请求对象
     */
    public static class ChainLlmRequest extends LlmRequest {
        public ChainLlmRequest(String toolType, String prompt, Map<String, Object> parameters) {
            super(toolType, prompt, parameters);
        }
    }
}

