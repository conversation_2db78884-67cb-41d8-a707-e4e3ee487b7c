/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulationInternalizationRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：外规内化请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * 外规内化请求DTO
 * <p>
 * 用于外规内化功能的请求参数。
 * 包含法规信息、企业信息等必要参数。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "外规内化请求")
public class RegulationInternalizationRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    /**
     * 法规ID
     */
    @NotBlank(message = "法规ID不能为空")
    @Schema(description = "法规ID", required = true, example = "REG_2024_001")
    private String regulationId;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    @Schema(description = "企业ID", required = true, example = "1")
    private Long companyId;

    /**
     * 行业类型
     */
    @NotBlank(message = "行业类型不能为空")
    @Schema(description = "行业类型", required = true, example = "电力")
    private String industryType;

    /**
     * 企业规模
     */
    @Schema(description = "企业规模", example = "大型企业")
    private String companyScale;

    /**
     * 法规内容
     */
    @Schema(description = "法规内容")
    private String regulationContent;

    /**
     * 特殊要求
     */
    @Schema(description = "特殊要求")
    private String specialRequirements;
}

// 构造函数
    public RegulationInternalizationRequestDTO() {}
    
    // TODO: 添加getter和setter方法
