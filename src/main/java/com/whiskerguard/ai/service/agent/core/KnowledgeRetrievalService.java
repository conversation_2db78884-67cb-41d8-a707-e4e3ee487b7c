/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：KnowledgeRetrievalService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：知识检索服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.domain.KnowledgeCache;
import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 知识检索服务
 * <p>
 * 负责从RAG服务检索知识，并提供缓存机制。
 * 统一管理知识检索的接口和缓存策略。
 * 
 * 主要功能：
 * 1. 知识检索和缓存
 * 2. 缓存管理和过期处理
 * 3. 检索结果优化
 * 4. 多租户数据隔离
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class KnowledgeRetrievalService {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeRetrievalService.class);

    private final RetrievalServiceClient retrievalServiceClient;
    private final KnowledgeCacheRepository knowledgeCacheRepository;

    /**
     * 缓存过期时间（小时）
     */
    private static final int CACHE_EXPIRE_HOURS = 24;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public KnowledgeRetrievalService(
            RetrievalServiceClient retrievalServiceClient,
            KnowledgeCacheRepository knowledgeCacheRepository) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.knowledgeCacheRepository = knowledgeCacheRepository;
    }

    /**
     * 检索知识内容
     * 
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param queryParams 查询参数
     * @return 知识内容
     */
    public String retrieveKnowledge(Long tenantId, String knowledgeType, String queryKey, Map<String, Object> queryParams) {
        log.debug("检索知识内容，租户: {}, 类型: {}, 关键词: {}", tenantId, knowledgeType, queryKey);

        try {
            // 1. 尝试从缓存获取
            Optional<String> cachedContent = getCachedKnowledge(tenantId, knowledgeType, queryKey);
            if (cachedContent.isPresent()) {
                log.debug("从缓存获取知识内容成功");
                return cachedContent.get();
            }

            // 2. 从RAG服务检索
            String content = retrieveFromRagService(tenantId, knowledgeType, queryParams);

            // 3. 缓存检索结果
            cacheKnowledge(tenantId, knowledgeType, queryKey, content);

            return content;

        } catch (Exception e) {
            log.error("检索知识内容失败", e);
            throw new RuntimeException("知识检索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量检索知识内容
     * 
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKeys 查询关键词列表
     * @param queryParams 查询参数
     * @return 知识内容列表
     */
    public List<String> batchRetrieveKnowledge(Long tenantId, String knowledgeType, List<String> queryKeys, Map<String, Object> queryParams) {
        log.debug("批量检索知识内容，租户: {}, 类型: {}, 关键词数量: {}", tenantId, knowledgeType, queryKeys.size());

        return queryKeys.stream()
                .map(queryKey -> retrieveKnowledge(tenantId, knowledgeType, queryKey, queryParams))
                .toList();
    }

    /**
     * 清理过期缓存
     */
    @Transactional
    public void cleanExpiredCache() {
        log.info("开始清理过期缓存");

        try {
            Instant expireTime = Instant.now();
            List<KnowledgeCache> expiredCaches = knowledgeCacheRepository.findByExpireTimeBefore(expireTime);
            
            if (!expiredCaches.isEmpty()) {
                knowledgeCacheRepository.deleteAll(expiredCaches);
                log.info("清理过期缓存完成，清理数量: {}", expiredCaches.size());
            } else {
                log.debug("没有过期缓存需要清理");
            }

        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    /**
     * 从缓存获取知识内容
     */
    private Optional<String> getCachedKnowledge(Long tenantId, String knowledgeType, String queryKey) {
        try {
            Optional<KnowledgeCache> cacheOpt = knowledgeCacheRepository
                    .findByTenantIdAndKnowledgeTypeAndQueryKeyAndExpireTimeAfter(
                            tenantId, knowledgeType, queryKey, Instant.now());

            if (cacheOpt.isPresent()) {
                KnowledgeCache cache = cacheOpt.get();
                
                // 更新访问统计
                cache.setAccessCount(cache.getAccessCount() + 1);
                cache.setLastAccessTime(Instant.now());
                cache.setUpdatedAt(Instant.now());
                cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                knowledgeCacheRepository.save(cache);

                return Optional.of(cache.getContent());
            }

            return Optional.empty();

        } catch (Exception e) {
            log.warn("从缓存获取知识内容失败", e);
            return Optional.empty();
        }
    }

    /**
     * 从RAG服务检索知识
     */
    private String retrieveFromRagService(Long tenantId, String knowledgeType, Map<String, Object> queryParams) {
        log.debug("从RAG服务检索知识，租户: {}, 类型: {}", tenantId, knowledgeType);

        try {
            // 构建检索请求
            Map<String, Object> request = new HashMap<>();
            request.put("tenantId", tenantId);
            request.put("knowledgeType", knowledgeType);
            if (queryParams != null) {
                request.putAll(queryParams);
            }

            // 调用retrievalServiceClient检索知识
            String response = retrievalServiceClient.retrieve(request);

            // 处理响应结果
            if (response != null && !response.trim().isEmpty()) {
                return response;
            } else {
                return "未找到相关知识内容";
            }

        } catch (Exception e) {
            log.error("从RAG服务检索知识失败", e);
            throw new RuntimeException("RAG服务检索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 缓存知识内容
     */
    private void cacheKnowledge(Long tenantId, String knowledgeType, String queryKey, String content) {
        try {
            KnowledgeCache cache = new KnowledgeCache();
            cache.setTenantId(tenantId);
            cache.setKnowledgeType(knowledgeType);
            cache.setQueryKey(queryKey);
            cache.setContent(content);
            cache.setSimilarityScore(0.0);
            cache.setSourceService("retrieval-service");
            cache.setExpireTime(Instant.now().plusSeconds(CACHE_EXPIRE_HOURS * 3600));
            cache.setAccessCount(1L);
            cache.setLastAccessTime(Instant.now());
            cache.setVersion(1);
            cache.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            cache.setCreatedAt(Instant.now());
            cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            cache.setUpdatedAt(Instant.now());
            cache.setIsDeleted(false);

            knowledgeCacheRepository.save(cache);
            log.debug("缓存知识内容成功");

        } catch (Exception e) {
            log.warn("缓存知识内容失败", e);
            // 缓存失败不影响主流程
        }
    }
}

