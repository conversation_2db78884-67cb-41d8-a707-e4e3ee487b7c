/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskStatusDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：Agent任务状态DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.Instant;

/**
 * Agent任务状态DTO
 * <p>
 * 用于返回Agent任务的当前状态信息。
 * 主要用于任务状态查询接口。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "Agent任务状态")
public class AgentTaskStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "RUNNING")
    private AgentTaskStatus status;

    /**
     * 执行进度（0-100）
     */
    @Schema(description = "执行进度", example = "50")
    private Integer progress;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Instant endTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    // 构造函数
    public AgentTaskStatusDTO() {}

    // Getter和Setter方法
    public Long getTaskId() { return taskId; }
    public void setTaskId(Long taskId) { this.taskId = taskId; }

    public AgentTaskStatus getStatus() { return status; }
    public void setStatus(AgentTaskStatus status) { this.status = status; }

    public Integer getProgress() { return progress; }
    public void setProgress(Integer progress) { this.progress = progress; }

    public Instant getStartTime() { return startTime; }
    public void setStartTime(Instant startTime) { this.startTime = startTime; }

    public Instant getEndTime() { return endTime; }
    public void setEndTime(Instant endTime) { this.endTime = endTime; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
}

