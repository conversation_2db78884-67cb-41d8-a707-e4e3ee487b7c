/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：制度审查响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 制度审查响应DTO
 * <p>
 * 制度审查功能的响应结果。
 * 包含审查结果和优化建议。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Schema(description = "制度审查响应")
public class PolicyReviewResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 制度ID
     */
    @Schema(description = "制度ID", example = "POL_2024_001")
    private String policyId;

    /**
     * 制度类型
     */
    @Schema(description = "制度类型", example = "安全生产制度")
    private String policyType;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID", example = "1")
    private Long companyId;

    /**
     * 内容分析结果
     */
    @Schema(description = "内容分析结果")
    private String contentAnalysis;

    /**
     * 合规性检查结果
     */
    @Schema(description = "合规性检查结果")
    private String complianceCheck;

    /**
     * 风险点列表
     */
    @Schema(description = "风险点列表")
    private List<String> riskPoints;

    /**
     * 优化建议列表
     */
    @Schema(description = "优化建议列表")
    private List<String> optimizationSuggestions;

    /**
     * 整体评分（0-100）
     */
    @Schema(description = "整体评分", example = "85")
    private Integer overallScore;

    /**
     * 审查总结
     */
    @Schema(description = "审查总结")
    private String reviewSummary;

    /**
     * 改进优先级
     */
    @Schema(description = "改进优先级")
    private List<String> improvementPriorities;
}

// 构造函数
    public PolicyReviewResponseDTO() {}
    
    // TODO: 添加getter和setter方法
