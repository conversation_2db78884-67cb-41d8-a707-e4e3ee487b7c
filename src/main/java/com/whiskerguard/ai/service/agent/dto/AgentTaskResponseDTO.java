/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：Agent任务响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * Agent任务响应DTO
 * <p>
 * 用于返回Agent任务的执行结果和状态信息。
 * 包含任务的完整执行信息。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Agent任务响应")
public class AgentTaskResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "1")
    private Long taskId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型", example = "REGULATION_INTERNALIZATION")
    private AgentTaskType taskType;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题", example = "电力行业安全生产法规内化")
    private String title;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "COMPLETED")
    private AgentTaskStatus status;

    /**
     * 执行进度（0-100）
     */
    @Schema(description = "执行进度", example = "100")
    private Integer progress;

    /**
     * 执行结果（JSON格式）
     */
    @Schema(description = "执行结果（JSON格式）")
    private String result;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Instant endTime;

    /**
     * 执行时长（毫秒）
     */
    @Schema(description = "执行时长（毫秒）", example = "5000")
    private Long executionTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Instant createdAt;
}

