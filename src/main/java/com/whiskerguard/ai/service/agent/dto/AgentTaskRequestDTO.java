/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.agent.dto
 * 描    述：Agent任务请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.dto;

import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Agent任务请求DTO
 * <p>
 * 用于接收客户端的Agent任务创建请求。
 * 包含任务的基本信息和执行参数。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Agent任务请求")
public class AgentTaskRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    /**
     * 任务类型
     */
    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型", required = true, example = "REGULATION_INTERNALIZATION")
    private AgentTaskType taskType;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(max = 200, message = "任务标题长度不能超过200个字符")
    @Schema(description = "任务标题", required = true, example = "电力行业安全生产法规内化")
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 1000, message = "任务描述长度不能超过1000个字符")
    @Schema(description = "任务描述", example = "将国家电力安全生产相关法规转化为公司内部管理制度")
    private String description;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "NORMAL")
    private TaskPriority priority;

    /**
     * 请求数据（JSON格式）
     */
    @NotBlank(message = "请求数据不能为空")
    @Schema(description = "请求数据（JSON格式）", required = true)
    private String requestData;

    /**
     * 扩展元数据（JSON格式）
     */
    @Schema(description = "扩展元数据（JSON格式）")
    private String metadata;
}

