/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiRequestService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：AI请求管理服务接口，定义AI请求相关的业务操作
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.service.dto.AiRequestChatDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * AI请求管理服务接口
 * <p>
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AiRequest}.
 * <p>
 * 定义了AI请求实体的增删改查以及按条件筛选等业务操作方法。
 */
public interface AiRequestService {
    /**
     * 保存AI请求
     * <p>
     * Save a aiRequest.
     *
     * @param aiRequestDTO 要保存的实体。the entity to save.
     * @return 持久化后的实体。the persisted entity.
     */
    AiRequestDTO save(AiRequestDTO aiRequestDTO);

    /**
     * 更新AI请求
     * <p>
     * Updates a aiRequest.
     *
     * @param aiRequestDTO 要更新的实体。the entity to update.
     * @return 持久化后的实体。the persisted entity.
     */
    AiRequestDTO update(AiRequestDTO aiRequestDTO);

    /**
     * 部分更新AI请求
     * <p>
     * Partially updates a aiRequest.
     *
     * @param aiRequestDTO 要部分更新的实体。the entity to update partially.
     * @return 持久化后的实体。the persisted entity.
     */
    Optional<AiRequestDTO> partialUpdate(AiRequestDTO aiRequestDTO);

    /**
     * 获取所有AI请求
     * <p>
     * Get all the aiRequests.
     *
     * @param pageable 分页信息。the pagination information.
     * @return 实体列表。the list of entities.
     */
    Page<AiRequestDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取AI请求
     * <p>
     * Get the "id" aiRequest.
     *
     * @param id 实体的ID。the id of the entity.
     * @return 实体。the entity.
     */
    Optional<AiRequestDTO> findOne(Long id);

    /**
     * 根据ID删除AI请求
     * <p>
     * Delete the "id" aiRequest.
     *
     * @param id 要删除实体的ID。the id of the entity.
     */
    void delete(Long id);

    /**
     * 根据员工ID分页查询AI请求列表
     * <p>
     * Find AI requests by employee ID with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI请求DTO列表。paginated list of AI request DTOs.
     */
    Page<AiRequestDTO> findByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据员工ID和请求状态分页查询AI请求列表
     * <p>
     * Find AI requests by employee ID and status with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param status 请求状态。request status.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI请求DTO列表。paginated list of AI request DTOs.
     */
    Page<AiRequestDTO> findByEmployeeIdAndStatus(Long employeeId, RequestStatus status, Pageable pageable);

    /**
     * 根据员工ID和工具类型分页查询AI请求列表
     * <p>
     * Find AI requests by employee ID and tool type with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param toolType 工具类型。tool type.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI请求DTO列表。paginated list of AI request DTOs.
     */
    Page<AiRequestDTO> findByEmployeeIdAndToolType(Long employeeId, String toolType, Pageable pageable);

    /**
     * 根据员工ID查询所有AI请求列表（不分页）
     * <p>
     * Find all AI requests by employee ID (without pagination).
     *
     * @param employeeId 员工ID。employee ID.
     * @return AI请求DTO列表。list of AI request DTOs.
     */
    List<AiRequestDTO> findAllByEmployeeId(Long employeeId);

    /**
     * 将AiRequestDTO分页数据转换为AiRequestChatDTO分页数据
     * 每条AiRequestDTO记录将被转换成两条AiRequestChatDTO记录（用户提问和AI回复）
     * <p>
     * Convert AiRequestDTO page data to AiRequestChatDTO page data.
     * Each AiRequestDTO record will be converted to two AiRequestChatDTO records (user question and AI response).
     *
     * @param page AiRequestDTO分页数据。AiRequestDTO page data.
     * @return 转换后的AiRequestChatDTO分页数据。converted AiRequestChatDTO page data.
     */
    Page<AiRequestChatDTO> convertToAiRequestChatDTOPage(Page<AiRequestDTO> page);

    /**
     * 根据员工ID和请求状态分页查询AI请求聊天记录
     * <p>
     * Find AI request chat records by employee ID and status with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param status 请求状态。request status.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI请求聊天DTO列表。paginated list of AI request chat DTOs.
     */
    Page<AiRequestChatDTO> findChatByEmployeeIdAndStatus(Long employeeId, RequestStatus status, Pageable pageable);
}
