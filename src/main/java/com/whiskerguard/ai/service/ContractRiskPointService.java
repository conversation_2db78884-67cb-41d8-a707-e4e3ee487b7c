package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.ContractRiskPointDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.ContractRiskPoint}.
 */
public interface ContractRiskPointService {
    /**
     * Save a contractRiskPoint.
     *
     * @param contractRiskPointDTO the entity to save.
     * @return the persisted entity.
     */
    ContractRiskPointDTO save(ContractRiskPointDTO contractRiskPointDTO);

    /**
     * Updates a contractRiskPoint.
     *
     * @param contractRiskPointDTO the entity to update.
     * @return the persisted entity.
     */
    ContractRiskPointDTO update(ContractRiskPointDTO contractRiskPointDTO);

    /**
     * Partially updates a contractRiskPoint.
     *
     * @param contractRiskPointDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ContractRiskPointDTO> partialUpdate(ContractRiskPointDTO contractRiskPointDTO);

    /**
     * Get all the contractRiskPoints.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ContractRiskPointDTO> findAll(Pageable pageable);

    /**
     * Get the "id" contractRiskPoint.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ContractRiskPointDTO> findOne(Long id);

    /**
     * Delete the "id" contractRiskPoint.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
