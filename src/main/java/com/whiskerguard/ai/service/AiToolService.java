/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：AI工具管理服务接口，定义AI工具相关的业务操作
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * AI工具管理服务接口
 * <p>
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AiTool}.
 * <p>
 * 定义了AI工具实体的增删改查以及按条件筛选等业务操作方法。
 */
public interface AiToolService {
    /**
     * 保存AI工具
     * <p>
     * Save a aiTool.
     *
     * @param aiToolDTO 要保存的实体。the entity to save.
     * @return 持久化后的实体。the persisted entity.
     */
    AiToolDTO save(AiToolDTO aiToolDTO);

    /**
     * 更新AI工具
     * <p>
     * Updates a aiTool.
     *
     * @param aiToolDTO 要更新的实体。the entity to update.
     * @return 持久化后的实体。the persisted entity.
     */
    AiToolDTO update(AiToolDTO aiToolDTO);

    /**
     * 部分更新AI工具
     * <p>
     * Partially updates a aiTool.
     *
     * @param aiToolDTO 要部分更新的实体。the entity to update partially.
     * @return 持久化后的实体。the persisted entity.
     */
    Optional<AiToolDTO> partialUpdate(AiToolDTO aiToolDTO);

    /**
     * 获取所有AI工具
     * <p>
     * Get all the aiTools.
     *
     * @param pageable 分页信息。the pagination information.
     * @return 实体列表。the list of entities.
     */
    Page<AiToolDTO> findAll(Pageable pageable);

    /**
     * 获取所有模型列表
     * <p>
     * Get all models.
     *
     * @param pageable 分页信息。pagination information.
     * @return 模型列表。list of models.
     */
    Page<AiToolDTO> findAllModels(Pageable pageable);

    /**
     * 根据模型分类获取模型列表
     * <p>
     * Get models by category.
     *
     * @param modelCategory 模型分类。model category.
     * @param pageable 分页信息。pagination information.
     * @return 特定分类的模型列表。list of models of the specified category.
     */
    Page<AiToolDTO> findModelsByCategory(String modelCategory, Pageable pageable);

    /**
     * 根据ID获取AI工具
     * <p>
     * Get the "id" aiTool.
     *
     * @param id 实体的ID。the id of the entity.
     * @return 实体。the entity.
     */
    Optional<AiToolDTO> findOne(Long id);

    /**
     * 根据ID删除AI工具
     * <p>
     * Delete the "id" aiTool.
     *
     * @param id 要删除实体的ID。the id of the entity.
     */
    void delete(Long id);

    /**
     * 根据工具键查找未删除的AI工具
     *
     * @param toolKey 工具键
     * @return 未删除的AI工具，如果不存在则返回null
     */
    com.whiskerguard.ai.domain.AiTool findByToolKey(String toolKey);
}
