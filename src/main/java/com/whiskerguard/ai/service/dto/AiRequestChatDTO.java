package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.AiRequest} entity.
 * Extended version with chat formatting support.
 */
@Schema(description = "AI聊天请求DTO（适用于聊天界面展示）")
public class AiRequestChatDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "租户 ID")
    private Long tenantId;

    @Schema(description = "职工（用户）ID")
    private Long employeeId;

    @Schema(description = "工具类型")
    private String toolType;

    @Schema(description = "消息内容（提问或回答）")
    private String content;

    @Schema(description = "请求时间")
    private Instant requestTime;

    @Schema(description = "响应时间")
    private Instant responseTime;

    @Schema(description = "请求状态")
    private RequestStatus status;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "乐观锁版本")
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @Schema(description = "软删除标志")
    private Boolean isDeleted;

    @Schema(description = "是否为用户消息")
    private Boolean isUser;

    @Schema(description = "原始请求ID（用于关联原始请求）")
    private Long originalRequestId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getToolType() {
        return toolType;
    }

    public void setToolType(String toolType) {
        this.toolType = toolType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Instant getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Instant requestTime) {
        this.requestTime = requestTime;
    }

    public Instant getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Instant responseTime) {
        this.responseTime = responseTime;
    }

    public RequestStatus getStatus() {
        return status;
    }

    public void setStatus(RequestStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsUser() {
        return isUser;
    }

    public void setIsUser(Boolean isUser) {
        this.isUser = isUser;
    }

    public Long getOriginalRequestId() {
        return originalRequestId;
    }

    public void setOriginalRequestId(Long originalRequestId) {
        this.originalRequestId = originalRequestId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiRequestChatDTO)) {
            return false;
        }

        AiRequestChatDTO aiRequestChatDTO = (AiRequestChatDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, aiRequestChatDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    @Override
    public String toString() {
        return (
            "AiRequestChatDTO{" +
            "id=" +
            getId() +
            ", tenantId=" +
            getTenantId() +
            ", employeeId=" +
            getEmployeeId() +
            ", toolType='" +
            getToolType() +
            "'" +
            ", content='" +
            getContent() +
            "'" +
            ", requestTime='" +
            getRequestTime() +
            "'" +
            ", responseTime='" +
            getResponseTime() +
            "'" +
            ", status='" +
            getStatus() +
            "'" +
            ", errorMessage='" +
            getErrorMessage() +
            "'" +
            ", metadata='" +
            getMetadata() +
            "'" +
            ", version=" +
            getVersion() +
            ", createdBy='" +
            getCreatedBy() +
            "'" +
            ", createdAt='" +
            getCreatedAt() +
            "'" +
            ", updatedBy='" +
            getUpdatedBy() +
            "'" +
            ", updatedAt='" +
            getUpdatedAt() +
            "'" +
            ", isDeleted='" +
            getIsDeleted() +
            "'" +
            ", isUser='" +
            getIsUser() +
            "'" +
            ", originalRequestId=" +
            getOriginalRequestId() +
            "}"
        );
    }
}
