package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.AiTool} entity.
 */
@Schema(description = "AI工具（AiTool）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiToolDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Size(max = 32)
    @Schema(description = "工具名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotNull
    @Size(max = 32)
    @Schema(description = "工具关键词", requiredMode = Schema.RequiredMode.REQUIRED)
    private String toolKey;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @NotNull
    @Size(max = 256)
    @Schema(description = "API 地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String apiUrl;

    @NotNull
    @Size(max = 256)
    @Schema(description = "API 密钥", requiredMode = Schema.RequiredMode.REQUIRED)
    private String apiKey;

    @Size(max = 32)
    @Schema(description = "鉴权类型")
    private String authType;

    @Size(max = 128)
    @Schema(description = "接口路径")
    private String path;

    @NotNull
    @Schema(description = "工具状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private ToolStatus status;

    @NotNull
    @Schema(description = "路由权重", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer weight;

    @NotNull
    @Schema(description = "并发许可数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer maxConcurrentCalls;

    @Schema(description = "是否为模型类型")
    private Boolean isModel;

    @Size(max = 32)
    @Schema(description = "模型分类")
    private String modelCategory;

    @Size(max = 64)
    @Schema(description = "模型提供商")
    private String modelProvider;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getToolKey() {
        return toolKey;
    }

    public void setToolKey(String toolKey) {
        this.toolKey = toolKey;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public ToolStatus getStatus() {
        return status;
    }

    public void setStatus(ToolStatus status) {
        this.status = status;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public Integer getMaxConcurrentCalls() {
        return maxConcurrentCalls;
    }

    public void setMaxConcurrentCalls(Integer maxConcurrentCalls) {
        this.maxConcurrentCalls = maxConcurrentCalls;
    }

    public Boolean getIsModel() {
        return isModel;
    }

    public void setIsModel(Boolean isModel) {
        this.isModel = isModel;
    }

    public String getModelCategory() {
        return modelCategory;
    }

    public void setModelCategory(String modelCategory) {
        this.modelCategory = modelCategory;
    }

    public String getModelProvider() {
        return modelProvider;
    }

    public void setModelProvider(String modelProvider) {
        this.modelProvider = modelProvider;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiToolDTO)) {
            return false;
        }

        AiToolDTO aiToolDTO = (AiToolDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, aiToolDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiToolDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", name='" + getName() + "'" +
            ", toolKey='" + getToolKey() + "'" +
            ", version=" + getVersion() +
            ", apiUrl='" + getApiUrl() + "'" +
            ", apiKey='" + getApiKey() + "'" +
            ", authType='" + getAuthType() + "'" +
            ", path='" + getPath() + "'" +
            ", status='" + getStatus() + "'" +
            ", weight=" + getWeight() +
            ", maxConcurrentCalls=" + getMaxConcurrentCalls() +
            ", isModel='" + getIsModel() + "'" +
            ", modelCategory='" + getModelCategory() + "'" +
            ", modelProvider='" + getModelProvider() + "'" +
            ", remark='" + getRemark() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
