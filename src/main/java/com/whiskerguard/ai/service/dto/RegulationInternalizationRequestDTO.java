package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "外规内化请求DTO")
public class RegulationInternalizationRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "租户ID")
    private Long tenantId;
    
    @Schema(description = "法规内容")
    private String regulationContent;
    
    @Schema(description = "行业类型")
    private String industryType;
    
    public RegulationInternalizationRequestDTO() {}
    
    public Long getTenantId() { return tenantId; }
    public void setTenantId(Long tenantId) { this.tenantId = tenantId; }
    
    public String getRegulationContent() { return regulationContent; }
    public void setRegulationContent(String regulationContent) { this.regulationContent = regulationContent; }
    
    public String getIndustryType() { return industryType; }
    public void setIndustryType(String industryType) { this.industryType = industryType; }
}
