package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.AgentTask} entity.
 */
@Schema(description = "Agent任务实体\n记录Agent执行的任务信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AgentTaskDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private AgentTaskType taskType;

    @NotNull
    @Size(max = 200)
    @Schema(description = "任务标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Size(max = 1000)
    @Schema(description = "任务描述")
    private String description;

    @NotNull
    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private AgentTaskStatus status;

    @NotNull
    @Schema(description = "任务优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    private TaskPriority priority;

    @Schema(description = "请求数据")
    @Lob
    private String requestData;

    @Schema(description = "响应数据")
    @Lob
    private String responseData;

    @Size(max = 2000)
    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "执行时长(毫秒)")
    private Long executionTime;

    @Min(value = 0)
    @Max(value = 100)
    @Schema(description = "进度百分比")
    private Integer progress;

    @Schema(description = "扩展元数据")
    @Lob
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public AgentTaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(AgentTaskType taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public AgentTaskStatus getStatus() {
        return status;
    }

    public void setStatus(AgentTaskStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return endTime;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AgentTaskDTO)) {
            return false;
        }

        AgentTaskDTO agentTaskDTO = (AgentTaskDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, agentTaskDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AgentTaskDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", taskType='" + getTaskType() + "'" +
            ", title='" + getTitle() + "'" +
            ", description='" + getDescription() + "'" +
            ", status='" + getStatus() + "'" +
            ", priority='" + getPriority() + "'" +
            ", requestData='" + getRequestData() + "'" +
            ", responseData='" + getResponseData() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", startTime='" + getStartTime() + "'" +
            ", endTime='" + getEndTime() + "'" +
            ", executionTime=" + getExecutionTime() +
            ", progress=" + getProgress() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
