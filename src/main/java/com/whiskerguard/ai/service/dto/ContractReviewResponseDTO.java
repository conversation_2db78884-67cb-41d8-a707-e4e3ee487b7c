/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.dto
 * 描    述：合同智能审查响应数据传输对象
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 合同智能审查响应数据传输对象
 * <p>
 * 用于封装合同智能审查的结果信息，
 * 提供结构化的风险分析和合规建议。
 */
public class ContractReviewResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 审查记录ID */
    private Long reviewId;

    /** 整体风险等级 */
    private RiskLevel overallRiskLevel;

    /** 风险分数 (0-100) */
    private Integer riskScore;

    /** 风险总结 */
    private String riskSummary;

    /** 具体风险点列表 */
    private List<RiskPointDTO> riskPoints;

    /** 关联方风险分析 */
    private List<PartyRiskAnalysisDTO> partyAnalysis;

    /** 条款问题分析 */
    private List<ClauseIssueDTO> clauseIssues;

    /** 合规性检查结果 */
    private ComplianceCheckResultDTO complianceCheck;

    /** 审查时间 */
    private Instant reviewTime;

    /** 审查耗时（毫秒） */
    private Long reviewDuration;

    /** 审查状态 */
    private String reviewStatus;

    /** AI模型信息 */
    private String aiModelInfo;

    /** 置信度 (0-100) */
    private Integer confidence;

    /** 建议措施 */
    private List<String> recommendations;

    /** 后续行动建议 */
    private List<String> nextActions;

    public ContractReviewResponseDTO() {}

    // 内部类：风险点DTO
    public static class RiskPointDTO implements Serializable {

        private String category;
        private String description;
        private RiskLevel severity;
        private List<String> affectedClauses;
        private String legalBasis;
        private List<String> suggestions;
        private Integer riskScore;
        private Boolean isCritical;

        // Getters and Setters
        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public RiskLevel getSeverity() {
            return severity;
        }

        public void setSeverity(RiskLevel severity) {
            this.severity = severity;
        }

        public List<String> getAffectedClauses() {
            return affectedClauses;
        }

        public void setAffectedClauses(List<String> affectedClauses) {
            this.affectedClauses = affectedClauses;
        }

        public String getLegalBasis() {
            return legalBasis;
        }

        public void setLegalBasis(String legalBasis) {
            this.legalBasis = legalBasis;
        }

        public List<String> getSuggestions() {
            return suggestions;
        }

        public void setSuggestions(List<String> suggestions) {
            this.suggestions = suggestions;
        }

        public Integer getRiskScore() {
            return riskScore;
        }

        public void setRiskScore(Integer riskScore) {
            this.riskScore = riskScore;
        }

        public Boolean getIsCritical() {
            return isCritical;
        }

        public void setIsCritical(Boolean isCritical) {
            this.isCritical = isCritical;
        }
    }

    // 内部类：关联方风险分析DTO
    public static class PartyRiskAnalysisDTO implements Serializable {

        private String partyName;
        private String partyType;
        private RiskLevel riskLevel;
        private List<String> riskFactors;
        private List<String> complianceIssues;
        private List<String> recommendations;
        private String creditRating;
        private String businessStatus;

        // Getters and Setters
        public String getPartyName() {
            return partyName;
        }

        public void setPartyName(String partyName) {
            this.partyName = partyName;
        }

        public String getPartyType() {
            return partyType;
        }

        public void setPartyType(String partyType) {
            this.partyType = partyType;
        }

        public RiskLevel getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(RiskLevel riskLevel) {
            this.riskLevel = riskLevel;
        }

        public List<String> getRiskFactors() {
            return riskFactors;
        }

        public void setRiskFactors(List<String> riskFactors) {
            this.riskFactors = riskFactors;
        }

        public List<String> getComplianceIssues() {
            return complianceIssues;
        }

        public void setComplianceIssues(List<String> complianceIssues) {
            this.complianceIssues = complianceIssues;
        }

        public List<String> getRecommendations() {
            return recommendations;
        }

        public void setRecommendations(List<String> recommendations) {
            this.recommendations = recommendations;
        }

        public String getCreditRating() {
            return creditRating;
        }

        public void setCreditRating(String creditRating) {
            this.creditRating = creditRating;
        }

        public String getBusinessStatus() {
            return businessStatus;
        }

        public void setBusinessStatus(String businessStatus) {
            this.businessStatus = businessStatus;
        }
    }

    // 内部类：条款问题DTO
    public static class ClauseIssueDTO implements Serializable {

        private String clauseText;
        private String clauseNumber;
        private String issueType;
        private String description;
        private RiskLevel severity;
        private String legalRisk;
        private List<String> suggestions;
        private List<String> referenceLaws;

        // Getters and Setters
        public String getClauseText() {
            return clauseText;
        }

        public void setClauseText(String clauseText) {
            this.clauseText = clauseText;
        }

        public String getClauseNumber() {
            return clauseNumber;
        }

        public void setClauseNumber(String clauseNumber) {
            this.clauseNumber = clauseNumber;
        }

        public String getIssueType() {
            return issueType;
        }

        public void setIssueType(String issueType) {
            this.issueType = issueType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public RiskLevel getSeverity() {
            return severity;
        }

        public void setSeverity(RiskLevel severity) {
            this.severity = severity;
        }

        public String getLegalRisk() {
            return legalRisk;
        }

        public void setLegalRisk(String legalRisk) {
            this.legalRisk = legalRisk;
        }

        public List<String> getSuggestions() {
            return suggestions;
        }

        public void setSuggestions(List<String> suggestions) {
            this.suggestions = suggestions;
        }

        public List<String> getReferenceLaws() {
            return referenceLaws;
        }

        public void setReferenceLaws(List<String> referenceLaws) {
            this.referenceLaws = referenceLaws;
        }
    }

    // 内部类：合规性检查结果DTO
    public static class ComplianceCheckResultDTO implements Serializable {

        private List<String> violatedRegulations;
        private List<String> internalPolicyViolations;
        private List<String> recommendations;
        private List<String> requiredActions;
        private Boolean overallCompliance;
        private String complianceScore;

        // Getters and Setters
        public List<String> getViolatedRegulations() {
            return violatedRegulations;
        }

        public void setViolatedRegulations(List<String> violatedRegulations) {
            this.violatedRegulations = violatedRegulations;
        }

        public List<String> getInternalPolicyViolations() {
            return internalPolicyViolations;
        }

        public void setInternalPolicyViolations(List<String> internalPolicyViolations) {
            this.internalPolicyViolations = internalPolicyViolations;
        }

        public List<String> getRecommendations() {
            return recommendations;
        }

        public void setRecommendations(List<String> recommendations) {
            this.recommendations = recommendations;
        }

        public List<String> getRequiredActions() {
            return requiredActions;
        }

        public void setRequiredActions(List<String> requiredActions) {
            this.requiredActions = requiredActions;
        }

        public Boolean getOverallCompliance() {
            return overallCompliance;
        }

        public void setOverallCompliance(Boolean overallCompliance) {
            this.overallCompliance = overallCompliance;
        }

        public String getComplianceScore() {
            return complianceScore;
        }

        public void setComplianceScore(String complianceScore) {
            this.complianceScore = complianceScore;
        }
    }

    // Getters and Setters
    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public RiskLevel getOverallRiskLevel() {
        return overallRiskLevel;
    }

    public void setOverallRiskLevel(RiskLevel overallRiskLevel) {
        this.overallRiskLevel = overallRiskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public String getRiskSummary() {
        return riskSummary;
    }

    public void setRiskSummary(String riskSummary) {
        this.riskSummary = riskSummary;
    }

    public List<RiskPointDTO> getRiskPoints() {
        return riskPoints;
    }

    public void setRiskPoints(List<RiskPointDTO> riskPoints) {
        this.riskPoints = riskPoints;
    }

    public List<PartyRiskAnalysisDTO> getPartyAnalysis() {
        return partyAnalysis;
    }

    public void setPartyAnalysis(List<PartyRiskAnalysisDTO> partyAnalysis) {
        this.partyAnalysis = partyAnalysis;
    }

    public List<ClauseIssueDTO> getClauseIssues() {
        return clauseIssues;
    }

    public void setClauseIssues(List<ClauseIssueDTO> clauseIssues) {
        this.clauseIssues = clauseIssues;
    }

    public ComplianceCheckResultDTO getComplianceCheck() {
        return complianceCheck;
    }

    public void setComplianceCheck(ComplianceCheckResultDTO complianceCheck) {
        this.complianceCheck = complianceCheck;
    }

    public Instant getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Instant reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Long getReviewDuration() {
        return reviewDuration;
    }

    public void setReviewDuration(Long reviewDuration) {
        this.reviewDuration = reviewDuration;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getAiModelInfo() {
        return aiModelInfo;
    }

    public void setAiModelInfo(String aiModelInfo) {
        this.aiModelInfo = aiModelInfo;
    }

    public Integer getConfidence() {
        return confidence;
    }

    public void setConfidence(Integer confidence) {
        this.confidence = confidence;
    }

    public List<String> getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(List<String> recommendations) {
        this.recommendations = recommendations;
    }

    public List<String> getNextActions() {
        return nextActions;
    }

    public void setNextActions(List<String> nextActions) {
        this.nextActions = nextActions;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractReviewResponseDTO that = (ContractReviewResponseDTO) o;
        return Objects.equals(reviewId, that.reviewId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(reviewId);
    }

    @Override
    public String toString() {
        return (
            "ContractReviewResponseDTO{" +
            "reviewId=" +
            reviewId +
            ", overallRiskLevel=" +
            overallRiskLevel +
            ", riskScore=" +
            riskScore +
            ", reviewTime=" +
            reviewTime +
            ", reviewDuration=" +
            reviewDuration +
            ", confidence=" +
            confidence +
            '}'
        );
    }

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Long reviewId;
        private String contractId;
        private Long companyId;
        private RiskLevel overallRiskLevel;
        private Integer riskScore;
        private String riskSummary;
        private List<RiskPointDTO> riskPoints;
        private List<PartyRiskAnalysisDTO> partyAnalysis;
        private List<ClauseIssueDTO> clauseIssues;
        private ComplianceCheckResultDTO complianceCheck;
        private Instant reviewTime;
        private Long reviewDuration;
        private String reviewStatus;
        private String aiModelInfo;
        private Integer confidence;
        private List<String> recommendations;
        private List<String> nextActions;

        public Builder reviewId(Long reviewId) {
            this.reviewId = reviewId;
            return this;
        }

        public Builder contractId(String contractId) {
            this.contractId = contractId;
            return this;
        }

        public Builder companyId(Long companyId) {
            this.companyId = companyId;
            return this;
        }

        public Builder overallRiskLevel(RiskLevel overallRiskLevel) {
            this.overallRiskLevel = overallRiskLevel;
            return this;
        }

        public Builder riskScore(Integer riskScore) {
            this.riskScore = riskScore;
            return this;
        }

        public Builder riskSummary(String riskSummary) {
            this.riskSummary = riskSummary;
            return this;
        }

        public Builder riskPoints(List<RiskPointDTO> riskPoints) {
            this.riskPoints = riskPoints;
            return this;
        }

        public Builder partyAnalysis(List<PartyRiskAnalysisDTO> partyAnalysis) {
            this.partyAnalysis = partyAnalysis;
            return this;
        }

        public Builder clauseIssues(List<ClauseIssueDTO> clauseIssues) {
            this.clauseIssues = clauseIssues;
            return this;
        }

        public Builder complianceCheck(ComplianceCheckResultDTO complianceCheck) {
            this.complianceCheck = complianceCheck;
            return this;
        }

        public Builder reviewTime(Instant reviewTime) {
            this.reviewTime = reviewTime;
            return this;
        }

        public Builder reviewDuration(Long reviewDuration) {
            this.reviewDuration = reviewDuration;
            return this;
        }

        public Builder reviewStatus(String reviewStatus) {
            this.reviewStatus = reviewStatus;
            return this;
        }

        public Builder aiModelInfo(String aiModelInfo) {
            this.aiModelInfo = aiModelInfo;
            return this;
        }

        public Builder confidence(Integer confidence) {
            this.confidence = confidence;
            return this;
        }

        public Builder recommendations(List<String> recommendations) {
            this.recommendations = recommendations;
            return this;
        }

        public Builder nextActions(List<String> nextActions) {
            this.nextActions = nextActions;
            return this;
        }

        public ContractReviewResponseDTO build() {
            ContractReviewResponseDTO dto = new ContractReviewResponseDTO();
            dto.setReviewId(reviewId);
            dto.setOverallRiskLevel(overallRiskLevel);
            dto.setRiskScore(riskScore);
            dto.setRiskSummary(riskSummary);
            dto.setRiskPoints(riskPoints);
            dto.setPartyAnalysis(partyAnalysis);
            dto.setClauseIssues(clauseIssues);
            dto.setComplianceCheck(complianceCheck);
            dto.setReviewTime(reviewTime);
            dto.setReviewDuration(reviewDuration);
            dto.setReviewStatus(reviewStatus);
            dto.setAiModelInfo(aiModelInfo);
            dto.setConfidence(confidence);
            dto.setRecommendations(recommendations);
            dto.setNextActions(nextActions);
            return dto;
        }
    }
}
