/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyReviewRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：内部制度智能审查请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

/**
 * 内部制度智能审查请求DTO
 * <p>
 * 用于接收法规微服务发送的内部制度审查请求，
 * 包含制度内容、企业信息、审查配置等参数。
 *
 * 主要功能：
 * 1. 制度内容分析和结构化处理
 * 2. 法律法规合规性检查
 * 3. 关联方风险评估
 * 4. 条款问题识别和改进建议
 */
@Schema(description = "内部制度智能审查请求DTO")
public class InternalPolicyReviewRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 制度内容 - 必填 */
    @NotBlank(message = "制度内容不能为空")
    @Size(min = 10, max = 1000000, message = "制度内容长度必须在10到1000000字符之间")
    @Schema(description = "制度内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String policyContent;

    /** 制度类型 - 必填 */
    @NotBlank(message = "制度类型不能为空")
    @Size(max = 64, message = "制度类型长度不能超过64字符")
    @Schema(description = "制度类型（如：人事制度、财务制度、采购制度等）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String policyType;

    /** 制度标题 - 可选 */
    @Size(max = 256, message = "制度标题长度不能超过256字符")
    @Schema(description = "制度标题")
    private String policyTitle;

    /** 制定部门 - 可选 */
    @Size(max = 128, message = "制定部门长度不能超过128字符")
    @Schema(description = "制定部门")
    private String department;

    /** 租户ID - 必填，用于多租户数据隔离 */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    /** 员工ID - 必填，用于审计和权限控制 */
    @NotNull(message = "员工ID不能为空")
    @Schema(description = "员工ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long employeeId;

    /** 公司名称 - 可选 */
    @Size(max = 256, message = "公司名称长度不能超过256字符")
    @Schema(description = "公司名称")
    private String companyName;

    /** 行业类型 - 可选 */
    @Size(max = 64, message = "行业类型长度不能超过64字符")
    @Schema(description = "行业类型")
    private String industry;

    /** 扩展元数据 - 可选 */
    @Schema(description = "扩展元数据")
    private Map<String, Object> metadata;

    /** 审查优先级 - 可选 */
    @Schema(description = "审查优先级")
    private ReviewPriority priority;

    /** 是否启用深度分析 - 可选，默认true */
    @Schema(description = "是否启用深度分析")
    private Boolean enableDeepAnalysis;

    /** 是否包含关联方背景调查 - 可选，默认true */
    @Schema(description = "是否包含关联方背景调查")
    private Boolean includeRelatedPartyBackground;

    /** 是否检查法律法规合规性 - 可选，默认true */
    @Schema(description = "是否检查法律法规合规性")
    private Boolean checkLegalCompliance;

    /** 指定的审查重点 - 可选 */
    @Schema(description = "审查重点")
    private ReviewFocus reviewFocus;

    /** 风险容忍度 - 可选 */
    @Schema(description = "风险容忍度")
    private RiskTolerance riskTolerance;

    /** 特殊要求说明 - 可选 */
    @Size(max = 1000, message = "特殊要求说明长度不能超过1000字符")
    @Schema(description = "特殊要求说明")
    private String specialRequirements;

    // 枚举：审查优先级
    public enum ReviewPriority {
        LOW, // 低优先级
        NORMAL, // 普通优先级
        HIGH, // 高优先级
        URGENT, // 紧急优先级
    }

    // 枚举：审查重点
    public enum ReviewFocus {
        COMPREHENSIVE, // 全面审查
        LEGAL_ONLY, // 仅法律合规
        RISK_ONLY, // 仅风险评估
        OPERATIONAL_ONLY, // 仅操作流程
        FINANCIAL_ONLY, // 仅财务相关
    }

    // 枚举：风险容忍度
    public enum RiskTolerance {
        LOW, // 低风险容忍度 - 严格审查
        MEDIUM, // 中等风险容忍度 - 标准审查
        HIGH, // 高风险容忍度 - 宽松审查
    }

    public InternalPolicyReviewRequestDTO() {
        this.priority = ReviewPriority.NORMAL;
        this.enableDeepAnalysis = true;
        this.includeRelatedPartyBackground = true;
        this.checkLegalCompliance = true;
        this.reviewFocus = ReviewFocus.COMPREHENSIVE;
        this.riskTolerance = RiskTolerance.MEDIUM;
    }

    public InternalPolicyReviewRequestDTO(String policyContent, String policyType, Long tenantId, Long employeeId) {
        this();
        this.policyContent = policyContent;
        this.policyType = policyType;
        this.tenantId = tenantId;
        this.employeeId = employeeId;
    }

    // Getters and Setters
    public String getPolicyContent() {
        return policyContent;
    }

    public void setPolicyContent(String policyContent) {
        this.policyContent = policyContent;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyTitle() {
        return policyTitle;
    }

    public void setPolicyTitle(String policyTitle) {
        this.policyTitle = policyTitle;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public ReviewPriority getPriority() {
        return priority;
    }

    public void setPriority(ReviewPriority priority) {
        this.priority = priority;
    }

    public Boolean getEnableDeepAnalysis() {
        return enableDeepAnalysis;
    }

    public void setEnableDeepAnalysis(Boolean enableDeepAnalysis) {
        this.enableDeepAnalysis = enableDeepAnalysis;
    }

    public Boolean getIncludeRelatedPartyBackground() {
        return includeRelatedPartyBackground;
    }

    public void setIncludeRelatedPartyBackground(Boolean includeRelatedPartyBackground) {
        this.includeRelatedPartyBackground = includeRelatedPartyBackground;
    }

    public Boolean getCheckLegalCompliance() {
        return checkLegalCompliance;
    }

    public void setCheckLegalCompliance(Boolean checkLegalCompliance) {
        this.checkLegalCompliance = checkLegalCompliance;
    }

    public ReviewFocus getReviewFocus() {
        return reviewFocus;
    }

    public void setReviewFocus(ReviewFocus reviewFocus) {
        this.reviewFocus = reviewFocus;
    }

    public RiskTolerance getRiskTolerance() {
        return riskTolerance;
    }

    public void setRiskTolerance(RiskTolerance riskTolerance) {
        this.riskTolerance = riskTolerance;
    }

    public String getSpecialRequirements() {
        return specialRequirements;
    }

    public void setSpecialRequirements(String specialRequirements) {
        this.specialRequirements = specialRequirements;
    }

    @Override
    public String toString() {
        return (
            "InternalPolicyReviewRequestDTO{" +
            "policyType='" +
            policyType +
            '\'' +
            ", policyTitle='" +
            policyTitle +
            '\'' +
            ", department='" +
            department +
            '\'' +
            ", tenantId=" +
            tenantId +
            ", employeeId=" +
            employeeId +
            ", companyName='" +
            companyName +
            '\'' +
            ", industry='" +
            industry +
            '\'' +
            ", priority=" +
            priority +
            ", enableDeepAnalysis=" +
            enableDeepAnalysis +
            ", includeRelatedPartyBackground=" +
            includeRelatedPartyBackground +
            ", checkLegalCompliance=" +
            checkLegalCompliance +
            ", reviewFocus=" +
            reviewFocus +
            ", riskTolerance=" +
            riskTolerance +
            '}'
        );
    }
}
