package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.PartyType;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.ContractParty} entity.
 */
@Schema(description = "合同关联方实体\n存储从合同中提取的关联方信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractPartyDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "关联的审查记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long reviewId;

    @NotNull
    @Size(max = 256)
    @Schema(description = "关联方名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String partyName;

    @NotNull
    @Schema(description = "关联方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private PartyType partyType;

    @Size(max = 64)
    @Schema(description = "在合同中的角色")
    private String partyRole;

    @Size(max = 32)
    @Schema(description = "统一社会信用代码（企业）")
    private String creditCode;

    @Size(max = 512)
    @Schema(description = "注册地址")
    private String registeredAddress;

    @Size(max = 64)
    @Schema(description = "法定代表人（企业）")
    private String legalRepresentative;

    @Size(max = 256)
    @Schema(description = "联系方式")
    private String contactInfo;

    @Schema(description = "风险等级")
    private RiskLevel riskLevel;

    @Schema(description = "风险因素（JSON数组）")
    @Lob
    private String riskFactors;

    @Schema(description = "合规问题（JSON数组）")
    @Lob
    private String complianceIssues;

    @Schema(description = "天眼查信息（JSON格式）")
    @Lob
    private String tianyanchaInfo;

    @Schema(description = "扩展信息")
    @Lob
    private String additionalInfo;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private ContractReviewDTO review;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public String getPartyName() {
        return partyName;
    }

    public void setPartyName(String partyName) {
        this.partyName = partyName;
    }

    public PartyType getPartyType() {
        return partyType;
    }

    public void setPartyType(PartyType partyType) {
        this.partyType = partyType;
    }

    public String getPartyRole() {
        return partyRole;
    }

    public void setPartyRole(String partyRole) {
        this.partyRole = partyRole;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskFactors() {
        return riskFactors;
    }

    public void setRiskFactors(String riskFactors) {
        this.riskFactors = riskFactors;
    }

    public String getComplianceIssues() {
        return complianceIssues;
    }

    public void setComplianceIssues(String complianceIssues) {
        this.complianceIssues = complianceIssues;
    }

    public String getTianyanchaInfo() {
        return tianyanchaInfo;
    }

    public void setTianyanchaInfo(String tianyanchaInfo) {
        this.tianyanchaInfo = tianyanchaInfo;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReviewDTO getReview() {
        return review;
    }

    public void setReview(ContractReviewDTO review) {
        this.review = review;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractPartyDTO)) {
            return false;
        }

        ContractPartyDTO contractPartyDTO = (ContractPartyDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, contractPartyDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractPartyDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", reviewId=" + getReviewId() +
            ", partyName='" + getPartyName() + "'" +
            ", partyType='" + getPartyType() + "'" +
            ", partyRole='" + getPartyRole() + "'" +
            ", creditCode='" + getCreditCode() + "'" +
            ", registeredAddress='" + getRegisteredAddress() + "'" +
            ", legalRepresentative='" + getLegalRepresentative() + "'" +
            ", contactInfo='" + getContactInfo() + "'" +
            ", riskLevel='" + getRiskLevel() + "'" +
            ", riskFactors='" + getRiskFactors() + "'" +
            ", complianceIssues='" + getComplianceIssues() + "'" +
            ", tianyanchaInfo='" + getTianyanchaInfo() + "'" +
            ", additionalInfo='" + getAdditionalInfo() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", review=" + getReview() +
            "}";
    }
}
