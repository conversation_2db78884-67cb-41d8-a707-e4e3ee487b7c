package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.TaskStep} entity.
 */
@Schema(description = "任务步骤实体\n记录Agent任务的执行步骤")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TaskStepDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Size(max = 100)
    @Schema(description = "步骤名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String stepName;

    @Size(max = 500)
    @Schema(description = "步骤描述")
    private String stepDescription;

    @NotNull
    @Schema(description = "步骤状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private TaskStepStatus status;

    @NotNull
    @Schema(description = "步骤顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stepOrder;

    @Schema(description = "输入数据")
    @Lob
    private String inputData;

    @Schema(description = "输出数据")
    @Lob
    private String outputData;

    @Size(max = 1000)
    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "执行时长(毫秒)")
    private Long executionTime;

    @Min(value = 0)
    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "扩展元数据")
    @Lob
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @NotNull
    private AgentTaskDTO agentTask;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    public String getStepDescription() {
        return stepDescription;
    }

    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }

    public TaskStepStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStepStatus status) {
        this.status = status;
    }

    public Integer getStepOrder() {
        return stepOrder;
    }

    public void setStepOrder(Integer stepOrder) {
        this.stepOrder = stepOrder;
    }

    public String getInputData() {
        return inputData;
    }

    public void setInputData(String inputData) {
        this.inputData = inputData;
    }

    public String getOutputData() {
        return outputData;
    }

    public void setOutputData(String outputData) {
        this.outputData = outputData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return endTime;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AgentTaskDTO getAgentTask() {
        return agentTask;
    }

    public void setAgentTask(AgentTaskDTO agentTask) {
        this.agentTask = agentTask;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TaskStepDTO)) {
            return false;
        }

        TaskStepDTO taskStepDTO = (TaskStepDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, taskStepDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TaskStepDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", stepName='" + getStepName() + "'" +
            ", stepDescription='" + getStepDescription() + "'" +
            ", status='" + getStatus() + "'" +
            ", stepOrder=" + getStepOrder() +
            ", inputData='" + getInputData() + "'" +
            ", outputData='" + getOutputData() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", startTime='" + getStartTime() + "'" +
            ", endTime='" + getEndTime() + "'" +
            ", executionTime=" + getExecutionTime() +
            ", retryCount=" + getRetryCount() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", agentTask=" + getAgentTask() +
            "}";
    }
}
