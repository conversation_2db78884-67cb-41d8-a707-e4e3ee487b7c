package com.whiskerguard.ai.service.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 法规条款详情DTO
 * <p>
 * 用于前端展示完整的法规条款内容，
 * 包含条款的详细信息和相关联的条款。
 */
public class LawArticleDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条款ID
     */
    private Long id;

    /**
     * 法规ID
     */
    private Long regulationId;

    /**
     * 法规名称
     */
    private String regulationTitle;

    /**
     * 条款号
     */
    private String articleNumber;

    /**
     * 条款标题
     */
    private String articleTitle;

    /**
     * 条款完整内容
     */
    private String fullContent;

    /**
     * 条款摘要
     */
    private String summary;

    /**
     * 法规类型
     */
    private String regulationType;

    /**
     * 发布机构
     */
    private String issuer;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 废止日期（如果已废止）
     */
    private String abolishedDate;

    /**
     * 相关条款列表
     */
    private List<RelatedArticleDTO> relatedArticles;

    /**
     * 解释说明
     */
    private String interpretation;

    /**
     * 适用案例
     */
    private List<String> applicableCases;

    /**
     * 关键词标签
     */
    private List<String> tags;

    /**
     * 是否为内部制度
     */
    private Boolean isInternal;

    /**
     * 来源链接
     */
    private String sourceUrl;

    // 内部类：相关条款
    public static class RelatedArticleDTO implements Serializable {

        private Long id;
        private String articleNumber;
        private String title;
        private String relationship; // REFERENCE, SUPPLEMENT, CONFLICT等

        // 构造函数
        public RelatedArticleDTO() {}

        public RelatedArticleDTO(Long id, String articleNumber, String title, String relationship) {
            this.id = id;
            this.articleNumber = articleNumber;
            this.title = title;
            this.relationship = relationship;
        }

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getArticleNumber() {
            return articleNumber;
        }

        public void setArticleNumber(String articleNumber) {
            this.articleNumber = articleNumber;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getRelationship() {
            return relationship;
        }

        public void setRelationship(String relationship) {
            this.relationship = relationship;
        }
    }

    // 构造函数
    public LawArticleDetailDTO() {}

    public LawArticleDetailDTO(Long id, String articleNumber, String articleTitle, String fullContent) {
        this.id = id;
        this.articleNumber = articleNumber;
        this.articleTitle = articleTitle;
        this.fullContent = fullContent;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRegulationId() {
        return regulationId;
    }

    public void setRegulationId(Long regulationId) {
        this.regulationId = regulationId;
    }

    public String getRegulationTitle() {
        return regulationTitle;
    }

    public void setRegulationTitle(String regulationTitle) {
        this.regulationTitle = regulationTitle;
    }

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public String getArticleTitle() {
        return articleTitle;
    }

    public void setArticleTitle(String articleTitle) {
        this.articleTitle = articleTitle;
    }

    public String getFullContent() {
        return fullContent;
    }

    public void setFullContent(String fullContent) {
        this.fullContent = fullContent;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getRegulationType() {
        return regulationType;
    }

    public void setRegulationType(String regulationType) {
        this.regulationType = regulationType;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getAbolishedDate() {
        return abolishedDate;
    }

    public void setAbolishedDate(String abolishedDate) {
        this.abolishedDate = abolishedDate;
    }

    public List<RelatedArticleDTO> getRelatedArticles() {
        return relatedArticles;
    }

    public void setRelatedArticles(List<RelatedArticleDTO> relatedArticles) {
        this.relatedArticles = relatedArticles;
    }

    public String getInterpretation() {
        return interpretation;
    }

    public void setInterpretation(String interpretation) {
        this.interpretation = interpretation;
    }

    public List<String> getApplicableCases() {
        return applicableCases;
    }

    public void setApplicableCases(List<String> applicableCases) {
        this.applicableCases = applicableCases;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    @Override
    public String toString() {
        return (
            "LawArticleDetailDTO{" +
            "id=" +
            id +
            ", regulationId=" +
            regulationId +
            ", regulationTitle='" +
            regulationTitle +
            '\'' +
            ", articleNumber='" +
            articleNumber +
            '\'' +
            ", articleTitle='" +
            articleTitle +
            '\'' +
            ", regulationType='" +
            regulationType +
            '\'' +
            ", isInternal=" +
            isInternal +
            '}'
        );
    }
}
