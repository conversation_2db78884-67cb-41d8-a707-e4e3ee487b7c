package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.AiRequest} entity.
 */
@Schema(description = "AI请求（AiRequest）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiRequestDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "职工（用户）ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long employeeId;

    @NotNull
    @Size(max = 64)
    @Schema(description = "工具类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String toolType;

    @NotNull
    @Schema(description = "提示词", requiredMode = Schema.RequiredMode.REQUIRED)
    private String prompt;

    @NotNull
    @Schema(description = "响应内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String response;

    @NotNull
    @Schema(description = "请求时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant requestTime;

    @Schema(description = "响应时间")
    private Instant responseTime;

    @NotNull
    @Schema(description = "请求状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private RequestStatus status;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "扩展元数据")
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private AiToolDTO tool;

    /**
     * 法规引用列表
     * <p>
     * 包含AI响应中引用的法律法规和内部制度信息，
     * 用于前端展示引用详情和提供法规链接。
     */
    @Schema(description = "法规引用列表")
    private List<LawReferenceDTO> lawReferences;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getToolType() {
        return toolType;
    }

    public void setToolType(String toolType) {
        this.toolType = toolType;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Instant getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Instant requestTime) {
        this.requestTime = requestTime;
    }

    public Instant getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Instant responseTime) {
        this.responseTime = responseTime;
    }

    public RequestStatus getStatus() {
        return status;
    }

    public void setStatus(RequestStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AiToolDTO getTool() {
        return tool;
    }

    public void setTool(AiToolDTO tool) {
        this.tool = tool;
    }

    public List<LawReferenceDTO> getLawReferences() {
        return lawReferences;
    }

    public void setLawReferences(List<LawReferenceDTO> lawReferences) {
        this.lawReferences = lawReferences;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiRequestDTO)) {
            return false;
        }

        AiRequestDTO aiRequestDTO = (AiRequestDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, aiRequestDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiRequestDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", toolType='" + getToolType() + "'" +
            ", prompt='" + getPrompt() + "'" +
            ", response='" + getResponse() + "'" +
            ", requestTime='" + getRequestTime() + "'" +
            ", responseTime='" + getResponseTime() + "'" +
            ", status='" + getStatus() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", tool=" + getTool() +
            ", lawReferencesCount=" + (getLawReferences() != null ? getLawReferences().size() : 0) +
            "}";
    }
}
