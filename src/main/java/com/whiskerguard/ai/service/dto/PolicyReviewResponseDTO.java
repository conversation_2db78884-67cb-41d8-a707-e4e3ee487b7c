package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Schema(description = "制度审查响应DTO")
public class PolicyReviewResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "制度ID")
    private String policyId;

    @Schema(description = "公司ID")
    private Long companyId;

    @Schema(description = "审查结果")
    private String reviewResult;

    @Schema(description = "合规性评分")
    private Integer complianceScore;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "发现的问题")
    private List<String> issues;

    @Schema(description = "改进建议")
    private List<String> recommendations;

    @Schema(description = "合规要点")
    private String compliancePoints;

    @Schema(description = "审查状态")
    private String status;

    @Schema(description = "审查时间")
    private Instant reviewedAt;

    public PolicyReviewResponseDTO() {}

    // Getter和Setter方法
    public String getPolicyId() { return policyId; }
    public void setPolicyId(String policyId) { this.policyId = policyId; }

    public Long getCompanyId() { return companyId; }
    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getReviewResult() { return reviewResult; }
    public void setReviewResult(String reviewResult) { this.reviewResult = reviewResult; }

    public Integer getComplianceScore() { return complianceScore; }
    public void setComplianceScore(Integer complianceScore) { this.complianceScore = complianceScore; }

    public String getRiskLevel() { return riskLevel; }
    public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }

    public List<String> getIssues() { return issues; }
    public void setIssues(List<String> issues) { this.issues = issues; }

    public List<String> getRecommendations() { return recommendations; }
    public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }

    public String getCompliancePoints() { return compliancePoints; }
    public void setCompliancePoints(String compliancePoints) { this.compliancePoints = compliancePoints; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Instant getReviewedAt() { return reviewedAt; }
    public void setReviewedAt(Instant reviewedAt) { this.reviewedAt = reviewedAt; }

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String policyId;
        private Long companyId;
        private String reviewResult;
        private Integer complianceScore;
        private String riskLevel;
        private List<String> issues;
        private List<String> recommendations;
        private String compliancePoints;
        private String status;
        private Instant reviewedAt;

        public Builder policyId(String policyId) {
            this.policyId = policyId;
            return this;
        }

        public Builder companyId(Long companyId) {
            this.companyId = companyId;
            return this;
        }

        public Builder reviewResult(String reviewResult) {
            this.reviewResult = reviewResult;
            return this;
        }

        public Builder complianceScore(Integer complianceScore) {
            this.complianceScore = complianceScore;
            return this;
        }

        public Builder riskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
            return this;
        }

        public Builder issues(List<String> issues) {
            this.issues = issues;
            return this;
        }

        public Builder recommendations(List<String> recommendations) {
            this.recommendations = recommendations;
            return this;
        }

        public Builder compliancePoints(String compliancePoints) {
            this.compliancePoints = compliancePoints;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder reviewedAt(Instant reviewedAt) {
            this.reviewedAt = reviewedAt;
            return this;
        }

        public PolicyReviewResponseDTO build() {
            PolicyReviewResponseDTO dto = new PolicyReviewResponseDTO();
            dto.setPolicyId(policyId);
            dto.setCompanyId(companyId);
            dto.setReviewResult(reviewResult);
            dto.setComplianceScore(complianceScore);
            dto.setRiskLevel(riskLevel);
            dto.setIssues(issues);
            dto.setRecommendations(recommendations);
            dto.setCompliancePoints(compliancePoints);
            dto.setStatus(status);
            dto.setReviewedAt(reviewedAt);
            return dto;
        }
    }
}
