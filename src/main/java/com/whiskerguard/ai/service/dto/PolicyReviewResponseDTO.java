package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "制度审查响应DTO")
public class PolicyReviewResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "审查结果")
    private String reviewResult;
    
    public PolicyReviewResponseDTO() {}
    
    public String getReviewResult() { return reviewResult; }
    public void setReviewResult(String reviewResult) { this.reviewResult = reviewResult; }
}
