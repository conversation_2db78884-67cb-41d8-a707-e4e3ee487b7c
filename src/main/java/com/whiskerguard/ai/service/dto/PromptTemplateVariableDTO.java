package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.VariableType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.PromptTemplateVariable} entity.
 */
@Schema(description = "提示词模板变量（PromptTemplateVariable）实体\n定义模板中使用的变量信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateVariableDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Size(max = 100)
    @Schema(description = "变量名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String variableName;

    @NotNull
    @Size(max = 200)
    @Schema(description = "变量显示名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String displayName;

    @Size(max = 500)
    @Schema(description = "变量描述")
    private String description;

    @NotNull
    @Schema(description = "变量类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private VariableType variableType;

    @Schema(description = "默认值")
    @Lob
    private String defaultValue;

    @NotNull
    @Schema(description = "是否必填", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isRequired;

    @Size(max = 500)
    @Schema(description = "变量验证规则")
    private String validationRule;

    @Schema(description = "变量示例值")
    @Lob
    private String exampleValue;

    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @NotNull
    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isEnabled;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 50)
    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 50)
    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private PromptTemplateDTO promptTemplate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public VariableType getVariableType() {
        return variableType;
    }

    public void setVariableType(VariableType variableType) {
        this.variableType = variableType;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getValidationRule() {
        return validationRule;
    }

    public void setValidationRule(String validationRule) {
        this.validationRule = validationRule;
    }

    public String getExampleValue() {
        return exampleValue;
    }

    public void setExampleValue(String exampleValue) {
        this.exampleValue = exampleValue;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public PromptTemplateDTO getPromptTemplate() {
        return promptTemplate;
    }

    public void setPromptTemplate(PromptTemplateDTO promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateVariableDTO)) {
            return false;
        }

        PromptTemplateVariableDTO promptTemplateVariableDTO = (PromptTemplateVariableDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, promptTemplateVariableDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateVariableDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", variableName='" + getVariableName() + "'" +
            ", displayName='" + getDisplayName() + "'" +
            ", description='" + getDescription() + "'" +
            ", variableType='" + getVariableType() + "'" +
            ", defaultValue='" + getDefaultValue() + "'" +
            ", isRequired='" + getIsRequired() + "'" +
            ", validationRule='" + getValidationRule() + "'" +
            ", exampleValue='" + getExampleValue() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", isEnabled='" + getIsEnabled() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", promptTemplate=" + getPromptTemplate() +
            "}";
    }
}
