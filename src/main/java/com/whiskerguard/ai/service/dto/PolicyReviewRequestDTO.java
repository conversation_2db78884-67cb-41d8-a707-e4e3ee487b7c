package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

@Schema(description = "制度审查请求DTO")
public class PolicyReviewRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    @Schema(description = "制度ID", example = "POL001")
    private String policyId;

    @NotBlank(message = "制度内容不能为空")
    @Size(max = 10000, message = "制度内容长度不能超过10000个字符")
    @Schema(description = "制度内容", required = true)
    private String policyContent;

    @NotBlank(message = "制度类型不能为空")
    @Schema(description = "制度类型", required = true, example = "安全管理制度")
    private String policyType;

    @Schema(description = "行业类型", example = "电力")
    private String industryType;

    @Schema(description = "公司ID", example = "1")
    private Long companyId;

    @Schema(description = "审查重点")
    private String reviewFocus;

    @Schema(description = "特殊要求")
    private String specialRequirements;

    public PolicyReviewRequestDTO() {}

    // Getter和Setter方法
    public Long getTenantId() { return tenantId; }
    public void setTenantId(Long tenantId) { this.tenantId = tenantId; }

    public String getPolicyId() { return policyId; }
    public void setPolicyId(String policyId) { this.policyId = policyId; }

    public String getPolicyContent() { return policyContent; }
    public void setPolicyContent(String policyContent) { this.policyContent = policyContent; }

    public String getPolicyType() { return policyType; }
    public void setPolicyType(String policyType) { this.policyType = policyType; }

    public String getIndustryType() { return industryType; }
    public void setIndustryType(String industryType) { this.industryType = industryType; }

    public Long getCompanyId() { return companyId; }
    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getReviewFocus() { return reviewFocus; }
    public void setReviewFocus(String reviewFocus) { this.reviewFocus = reviewFocus; }

    public String getSpecialRequirements() { return specialRequirements; }
    public void setSpecialRequirements(String specialRequirements) { this.specialRequirements = specialRequirements; }
}
