package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "制度审查请求DTO")
public class PolicyReviewRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "租户ID")
    private Long tenantId;
    
    @Schema(description = "制度内容")
    private String policyContent;
    
    @Schema(description = "制度类型")
    private String policyType;
    
    public PolicyReviewRequestDTO() {}
    
    public Long getTenantId() { return tenantId; }
    public void setTenantId(Long tenantId) { this.tenantId = tenantId; }
    
    public String getPolicyContent() { return policyContent; }
    public void setPolicyContent(String policyContent) { this.policyContent = policyContent; }
    
    public String getPolicyType() { return policyType; }
    public void setPolicyType(String policyType) { this.policyType = policyType; }
}
