package com.whiskerguard.ai.service.dto;

import java.io.Serializable;

/**
 * 法规引用DTO
 * <p>
 * 用于AI响应中的法规引用信息传输，
 * 包含法规的基本信息和引用相关的元数据。
 */
public class LawReferenceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 引用ID
     */
    private Long id;

    /**
     * 法规类型
     * LAW - 法律
     * REGULATION - 法规
     * INTERNAL_POLICY - 内部制度
     * JUDICIAL_INTERPRETATION - 司法解释
     * INDUSTRY_STANDARD - 行业标准
     */
    private String regulationType;

    /**
     * 法规名称
     */
    private String title;

    /**
     * 条款号
     */
    private String articleNumber;

    /**
     * 条款标题
     */
    private String articleTitle;

    /**
     * 条款摘要
     */
    private String summary;

    /**
     * 完整内容（可选）
     */
    private String fullContent;

    /**
     * 来源链接
     */
    private String sourceUrl;

    /**
     * 相关性评分（0-100）
     */
    private Double relevanceScore;

    /**
     * 分类
     * 如：合同法、公司法、劳动法等
     */
    private String category;

    /**
     * 发布机构
     */
    private String issuer;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 是否为内部制度
     */
    private Boolean isInternal;

    /**
     * 引用标记ID
     * 用于在AI响应中标记引用位置
     */
    private String referenceMarkerId;

    // 构造函数
    public LawReferenceDTO() {}

    public LawReferenceDTO(Long id, String regulationType, String title, String articleNumber, String summary) {
        this.id = id;
        this.regulationType = regulationType;
        this.title = title;
        this.articleNumber = articleNumber;
        this.summary = summary;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRegulationType() {
        return regulationType;
    }

    public void setRegulationType(String regulationType) {
        this.regulationType = regulationType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public String getArticleTitle() {
        return articleTitle;
    }

    public void setArticleTitle(String articleTitle) {
        this.articleTitle = articleTitle;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getFullContent() {
        return fullContent;
    }

    public void setFullContent(String fullContent) {
        this.fullContent = fullContent;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public Double getRelevanceScore() {
        return relevanceScore;
    }

    public void setRelevanceScore(Double relevanceScore) {
        this.relevanceScore = relevanceScore;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    public String getReferenceMarkerId() {
        return referenceMarkerId;
    }

    public void setReferenceMarkerId(String referenceMarkerId) {
        this.referenceMarkerId = referenceMarkerId;
    }

    @Override
    public String toString() {
        return (
            "LawReferenceDTO{" +
            "id=" +
            id +
            ", regulationType='" +
            regulationType +
            '\'' +
            ", title='" +
            title +
            '\'' +
            ", articleNumber='" +
            articleNumber +
            '\'' +
            ", articleTitle='" +
            articleTitle +
            '\'' +
            ", summary='" +
            summary +
            '\'' +
            ", relevanceScore=" +
            relevanceScore +
            ", category='" +
            category +
            '\'' +
            ", isInternal=" +
            isInternal +
            ", referenceMarkerId='" +
            referenceMarkerId +
            '\'' +
            '}'
        );
    }
}
