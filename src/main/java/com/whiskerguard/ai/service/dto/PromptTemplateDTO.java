package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.PromptTemplate} entity.
 */
@Schema(description = "提示词模板（PromptTemplate）实体\n存储提示词模板的基本信息和内容")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "租户 ID")
    private Long tenantId;

    @NotNull
    @Size(max = 100)
    @Schema(description = "模板唯一标识键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateKey;

    @NotNull
    @Size(max = 200)
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Size(max = 1000)
    @Schema(description = "模板描述")
    private String description;

    @NotNull
    @Schema(description = "模板类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private PromptTemplateType templateType;

    @Schema(description = "模板内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String content;

    @NotNull
    @Schema(description = "模板状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private PromptTemplateStatus status;

    @NotNull
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer templateVersion;

    @NotNull
    @Schema(description = "是否为系统默认模板", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isSystemDefault;

    @Schema(description = "创建者ID")
    private Long createdById;

    @Schema(description = "最后修改者ID")
    private Long lastModifiedById;

    @Schema(description = "使用次数统计")
    private Long usageCount;

    @Schema(description = "最后使用时间")
    private Instant lastUsedAt;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 50)
    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 50)
    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PromptTemplateType getTemplateType() {
        return templateType;
    }

    public void setTemplateType(PromptTemplateType templateType) {
        this.templateType = templateType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PromptTemplateStatus getStatus() {
        return status;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Integer getTemplateVersion() {
        return templateVersion;
    }

    public void setTemplateVersion(Integer templateVersion) {
        this.templateVersion = templateVersion;
    }

    public Boolean getIsSystemDefault() {
        return isSystemDefault;
    }

    public void setIsSystemDefault(Boolean isSystemDefault) {
        this.isSystemDefault = isSystemDefault;
    }

    public Long getCreatedById() {
        return createdById;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public Long getLastModifiedById() {
        return lastModifiedById;
    }

    public void setLastModifiedById(Long lastModifiedById) {
        this.lastModifiedById = lastModifiedById;
    }

    public Long getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return lastUsedAt;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateDTO)) {
            return false;
        }

        PromptTemplateDTO promptTemplateDTO = (PromptTemplateDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, promptTemplateDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", templateKey='" + getTemplateKey() + "'" +
            ", name='" + getName() + "'" +
            ", description='" + getDescription() + "'" +
            ", templateType='" + getTemplateType() + "'" +
            ", content='" + getContent() + "'" +
            ", status='" + getStatus() + "'" +
            ", templateVersion=" + getTemplateVersion() +
            ", isSystemDefault='" + getIsSystemDefault() + "'" +
            ", createdById=" + getCreatedById() +
            ", lastModifiedById=" + getLastModifiedById() +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
