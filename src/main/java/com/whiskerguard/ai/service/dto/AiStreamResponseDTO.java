/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiStreamResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.dto
 * 描    述：AI流式响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto;

import java.io.Serializable;
import java.util.Map;

/**
 * DTO for streaming responses from AI tools.
 * 用于AI工具流式响应的数据传输对象。
 */
public class AiStreamResponseDTO implements Serializable {

    // 序列化版本ID
    private static final long serialVersionUID = 1L;

    /**
     * The content chunk from the AI response.
     * AI响应的内容片段。
     */
    private String content;

    /**
     * Indicates if this is the final chunk in the stream.
     * 表示这是否是流中的最后一个片段。
     */
    private boolean done;

    /**
     * Optional usage statistics (only included in the final chunk).
     * 可选的使用统计信息（仅包含在最后一个片段中）。
     */
    private Map<String, Object> usage;

    /**
     * Optional error message if an error occurred.
     * 如果发生错误，则包含可选的错误消息。
     */
    private String error;

    public AiStreamResponseDTO() {
        // Default constructor 默认构造函数
    }

    /**
     * Constructor for content chunks.
     * 内容片段的构造函数。
     *
     * @param content The content chunk 内容片段
     * @param done    Whether this is the final chunk 是否为最后一个片段
     */
    public AiStreamResponseDTO(String content, boolean done) {
        this.content = content;
        this.done = done;
    }

    /**
     * Constructor for the final chunk with usage statistics.
     * 带有使用统计信息的最终片段的构造函数。
     *
     * @param content The content chunk 内容片段
     * @param usage   Usage statistics 使用统计信息
     */
    public AiStreamResponseDTO(String content, Map<String, Object> usage) {
        this.content = content;
        this.usage = usage;
        this.done = true;
    }

    /**
     * Constructor for error responses.
     * 错误响应的构造函数。
     *
     * @param error The error message 错误消息
     */
    public AiStreamResponseDTO(String error) {
        this.error = error;
        this.done = true;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isDone() {
        return done;
    }

    public void setDone(boolean done) {
        this.done = done;
    }

    public Map<String, Object> getUsage() {
        return usage;
    }

    public void setUsage(Map<String, Object> usage) {
        this.usage = usage;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return (
            "AiStreamResponseDTO{" + "content='" + content + '\'' + ", done=" + done + ", usage=" + usage + ", error='" + error + '\'' + '}'
        );
    }
}
