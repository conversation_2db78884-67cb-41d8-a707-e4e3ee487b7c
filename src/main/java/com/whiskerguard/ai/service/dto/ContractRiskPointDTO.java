package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.RiskCategory;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.ContractRiskPoint} entity.
 */
@Schema(description = "风险点实体\n存储合同审查中识别的具体风险点")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractRiskPointDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "关联的审查记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long reviewId;

    @NotNull
    @Schema(description = "风险类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private RiskCategory riskCategory;

    @Schema(description = "风险描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String riskDescription;

    @NotNull
    @Schema(description = "风险等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private RiskLevel severity;

    @Schema(description = "涉及的条款")
    @Lob
    private String affectedClauses;

    @Schema(description = "法律依据")
    @Lob
    private String legalBasis;

    @Schema(description = "修改建议")
    @Lob
    private String suggestions;

    @Min(value = 0)
    @Max(value = 100)
    @Schema(description = "风险分数 (0-100)")
    private Integer riskScore;

    @Schema(description = "是否为关键风险")
    private Boolean isCritical;

    @Size(max = 128)
    @Schema(description = "风险来源")
    private String riskSource;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private ContractReviewDTO review;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public RiskCategory getRiskCategory() {
        return riskCategory;
    }

    public void setRiskCategory(RiskCategory riskCategory) {
        this.riskCategory = riskCategory;
    }

    public String getRiskDescription() {
        return riskDescription;
    }

    public void setRiskDescription(String riskDescription) {
        this.riskDescription = riskDescription;
    }

    public RiskLevel getSeverity() {
        return severity;
    }

    public void setSeverity(RiskLevel severity) {
        this.severity = severity;
    }

    public String getAffectedClauses() {
        return affectedClauses;
    }

    public void setAffectedClauses(String affectedClauses) {
        this.affectedClauses = affectedClauses;
    }

    public String getLegalBasis() {
        return legalBasis;
    }

    public void setLegalBasis(String legalBasis) {
        this.legalBasis = legalBasis;
    }

    public String getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(String suggestions) {
        this.suggestions = suggestions;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Boolean getIsCritical() {
        return isCritical;
    }

    public void setIsCritical(Boolean isCritical) {
        this.isCritical = isCritical;
    }

    public String getRiskSource() {
        return riskSource;
    }

    public void setRiskSource(String riskSource) {
        this.riskSource = riskSource;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReviewDTO getReview() {
        return review;
    }

    public void setReview(ContractReviewDTO review) {
        this.review = review;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractRiskPointDTO)) {
            return false;
        }

        ContractRiskPointDTO contractRiskPointDTO = (ContractRiskPointDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, contractRiskPointDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractRiskPointDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", reviewId=" + getReviewId() +
            ", riskCategory='" + getRiskCategory() + "'" +
            ", riskDescription='" + getRiskDescription() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", affectedClauses='" + getAffectedClauses() + "'" +
            ", legalBasis='" + getLegalBasis() + "'" +
            ", suggestions='" + getSuggestions() + "'" +
            ", riskScore=" + getRiskScore() +
            ", isCritical='" + getIsCritical() + "'" +
            ", riskSource='" + getRiskSource() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", review=" + getReview() +
            "}";
    }
}
