/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiStreamRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.dto
 * 描    述：AI流式调用请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * DTO for streaming invocation of AI tools via the whiskerguard-ai-service.
 * 用于通过whiskerguard-ai-service流式调用AI工具的数据传输对象。
 */
public class AiStreamRequestDTO implements Serializable {

    // 序列化版本ID
    private static final long serialVersionUID = 1L;

    /**
     * Identifier of the AI tool to invoke (e.g., "chatgpt", "deepseek").
     * AI工具的标识符（例如："chatgpt", "deepseek"）。
     */
    @NotNull
    private String toolKey;

    /**
     * Prompt or input text for the AI tool.
     * 提供给AI工具的提示或输入文本。
     */
    @NotNull
    private String prompt;

    /**
     * Optional metadata or parameters for the invocation.
     * 调用时的可选元数据或参数。
     */
    private Map<String, Object> metadata;

    /**
     * Tenant identifier for multi-tenant scenarios.
     * 多租户场景下的租户标识符。
     */
    private Long tenantId;

    /**
     * Employee identifier for tracking user requests.
     * 员工标识符，用于跟踪用户请求。
     */
    @NotNull
    private Long employeeId;

    /**
     * Flag to enable streaming mode.
     * 是否启用流式模式的标志。
     */
    private boolean streaming = true;

    /**
     * Template key for prompt template (optional).
     * 提示词模板键（可选）。
     * 如果指定，将使用对应的模板来构建最终的提示词。
     */
    private String templateKey;

    /**
     * Template type for prompt template (optional).
     * 提示词模板类型（可选）。
     * 当templateKey为空时，可以通过模板类型自动选择合适的模板。
     */
    private PromptTemplateType templateType;

    /**
     * Variables for template substitution (optional).
     * 模板变量替换映射（可选）。
     * 用于在模板中替换占位符变量。
     */
    private Map<String, Object> templateVariables;

    /**
     * Flag to enable template usage (optional, default: false).
     * 是否启用模板功能的标志（可选，默认：false）。
     * 为了向下兼容，默认不使用模板功能。
     */
    private boolean useTemplate = false;

    public AiStreamRequestDTO() {
        // Default constructor 默认构造函数
    }

    public AiStreamRequestDTO(String toolKey, String prompt, Map<String, Object> metadata, Long tenantId) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.tenantId = tenantId;
    }

    public AiStreamRequestDTO(String toolKey, String prompt, Map<String, Object> metadata, Long tenantId, Long employeeId) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.tenantId = tenantId;
        this.employeeId = employeeId;
    }

    /**
     * 构造函数 - 支持模板功能
     * Constructor with template support
     */
    public AiStreamRequestDTO(
        String toolKey,
        String prompt,
        Map<String, Object> metadata,
        Long tenantId,
        Long employeeId,
        String templateKey,
        PromptTemplateType templateType,
        Map<String, Object> templateVariables,
        boolean useTemplate
    ) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.tenantId = tenantId;
        this.employeeId = employeeId;
        this.templateKey = templateKey;
        this.templateType = templateType;
        this.templateVariables = templateVariables;
        this.useTemplate = useTemplate;
    }

    public String getToolKey() {
        return toolKey;
    }

    public void setToolKey(String toolKey) {
        this.toolKey = toolKey;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public boolean isStreaming() {
        return streaming;
    }

    public void setStreaming(boolean streaming) {
        this.streaming = streaming;
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public PromptTemplateType getTemplateType() {
        return templateType;
    }

    public void setTemplateType(PromptTemplateType templateType) {
        this.templateType = templateType;
    }

    public Map<String, Object> getTemplateVariables() {
        return templateVariables;
    }

    public void setTemplateVariables(Map<String, Object> templateVariables) {
        this.templateVariables = templateVariables;
    }

    public boolean isUseTemplate() {
        return useTemplate;
    }

    public void setUseTemplate(boolean useTemplate) {
        this.useTemplate = useTemplate;
    }

    @Override
    public String toString() {
        return (
            "AiStreamRequestDTO{" +
            "toolKey='" +
            toolKey +
            '\'' +
            ", prompt='" +
            prompt +
            '\'' +
            ", metadata=" +
            metadata +
            ", tenantId=" +
            tenantId +
            ", employeeId=" +
            employeeId +
            ", streaming=" +
            streaming +
            ", templateKey='" +
            templateKey +
            '\'' +
            ", templateType=" +
            templateType +
            ", templateVariables=" +
            templateVariables +
            ", useTemplate=" +
            useTemplate +
            '}'
        );
    }
}
