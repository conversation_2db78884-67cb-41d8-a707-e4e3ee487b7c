package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.MetricsPeriod;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.AiToolMetrics} entity.
 */
@Schema(description = "AI工具指标（AiToolMetrics）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiToolMetricsDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "统计周期", requiredMode = Schema.RequiredMode.REQUIRED)
    private MetricsPeriod period;

    @Schema(description = "响应时间")
    private Integer responseTime;

    @Schema(description = "成功次数")
    private Long successCount;

    @Schema(description = "失败次数")
    private Long failureCount;

    @Schema(description = "总请求数")
    private Long totalRequests;

    @Schema(description = "错误率")
    private Float errorRate;

    @NotNull
    @Schema(description = "采集日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant collectDate;

    @Schema(description = "扩展元数据")
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private AiToolDTO tool;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public MetricsPeriod getPeriod() {
        return period;
    }

    public void setPeriod(MetricsPeriod period) {
        this.period = period;
    }

    public Integer getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Integer responseTime) {
        this.responseTime = responseTime;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }

    public Long getTotalRequests() {
        return totalRequests;
    }

    public void setTotalRequests(Long totalRequests) {
        this.totalRequests = totalRequests;
    }

    public Float getErrorRate() {
        return errorRate;
    }

    public void setErrorRate(Float errorRate) {
        this.errorRate = errorRate;
    }

    public Instant getCollectDate() {
        return collectDate;
    }

    public void setCollectDate(Instant collectDate) {
        this.collectDate = collectDate;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AiToolDTO getTool() {
        return tool;
    }

    public void setTool(AiToolDTO tool) {
        this.tool = tool;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiToolMetricsDTO)) {
            return false;
        }

        AiToolMetricsDTO aiToolMetricsDTO = (AiToolMetricsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, aiToolMetricsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiToolMetricsDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", period='" + getPeriod() + "'" +
            ", responseTime=" + getResponseTime() +
            ", successCount=" + getSuccessCount() +
            ", failureCount=" + getFailureCount() +
            ", totalRequests=" + getTotalRequests() +
            ", errorRate=" + getErrorRate() +
            ", collectDate='" + getCollectDate() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", tool=" + getTool() +
            "}";
    }
}
