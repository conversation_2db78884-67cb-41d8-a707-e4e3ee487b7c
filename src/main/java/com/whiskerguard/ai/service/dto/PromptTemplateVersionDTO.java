package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.PromptTemplateVersion} entity.
 */
@Schema(description = "提示词模板版本（PromptTemplateVersion）实体\n管理模板的版本历史")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateVersionDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer versionNumber;

    @Size(max = 200)
    @Schema(description = "版本名称")
    private String versionName;

    @Size(max = 1000)
    @Schema(description = "版本描述")
    private String description;

    @Schema(description = "模板内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String content;

    @Schema(description = "变量定义JSON")
    @Lob
    private String variablesDefinition;

    @NotNull
    @Schema(description = "版本状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private PromptTemplateStatus status;

    @NotNull
    @Schema(description = "是否为当前活跃版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isActive;

    @Schema(description = "创建者ID")
    private Long createdById;

    @Size(max = 1000)
    @Schema(description = "版本变更说明")
    private String changeLog;

    @Schema(description = "使用次数统计")
    private Long usageCount;

    @Schema(description = "最后使用时间")
    private Instant lastUsedAt;

    @Schema(description = "性能评分")
    private Double performanceScore;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 50)
    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 50)
    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private PromptTemplateDTO promptTemplate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVariablesDefinition() {
        return variablesDefinition;
    }

    public void setVariablesDefinition(String variablesDefinition) {
        this.variablesDefinition = variablesDefinition;
    }

    public PromptTemplateStatus getStatus() {
        return status;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Long getCreatedById() {
        return createdById;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public String getChangeLog() {
        return changeLog;
    }

    public void setChangeLog(String changeLog) {
        this.changeLog = changeLog;
    }

    public Long getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return lastUsedAt;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Double getPerformanceScore() {
        return performanceScore;
    }

    public void setPerformanceScore(Double performanceScore) {
        this.performanceScore = performanceScore;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public PromptTemplateDTO getPromptTemplate() {
        return promptTemplate;
    }

    public void setPromptTemplate(PromptTemplateDTO promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateVersionDTO)) {
            return false;
        }

        PromptTemplateVersionDTO promptTemplateVersionDTO = (PromptTemplateVersionDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, promptTemplateVersionDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateVersionDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", versionNumber=" + getVersionNumber() +
            ", versionName='" + getVersionName() + "'" +
            ", description='" + getDescription() + "'" +
            ", content='" + getContent() + "'" +
            ", variablesDefinition='" + getVariablesDefinition() + "'" +
            ", status='" + getStatus() + "'" +
            ", isActive='" + getIsActive() + "'" +
            ", createdById=" + getCreatedById() +
            ", changeLog='" + getChangeLog() + "'" +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            ", performanceScore=" + getPerformanceScore() +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", promptTemplate=" + getPromptTemplate() +
            "}";
    }
}
