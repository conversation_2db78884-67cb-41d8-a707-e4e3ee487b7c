package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.ReviewStatus;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.ContractReview} entity.
 */
@Schema(description = "合同审查记录实体\n存储合同审查的基本信息和结果")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractReviewDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "租户ID - 多租户数据隔离", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "员工ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long employeeId;

    @Size(max = 64)
    @Schema(description = "合同类型")
    private String contractType;

    @Size(max = 256)
    @Schema(description = "合同标题")
    private String contractTitle;

    @Schema(description = "合同内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String contractContent;

    @Schema(description = "审查结果（JSON格式）")
    @Lob
    private String reviewResult;

    @NotNull
    @Schema(description = "审查状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private ReviewStatus status;

    @Schema(description = "整体风险等级")
    private RiskLevel overallRiskLevel;

    @Min(value = 0)
    @Max(value = 100)
    @Schema(description = "风险分数 (0-100)")
    private Integer riskScore;

    @Schema(description = "风险总结")
    @Lob
    private String riskSummary;

    @Schema(description = "AI调用ID - 关联到ai_request表")
    private Long aiRequestId;

    @Schema(description = "审查开始时间")
    private Instant reviewStartTime;

    @Schema(description = "审查完成时间")
    private Instant reviewEndTime;

    @Schema(description = "审查耗时（毫秒）")
    private Long reviewDuration;

    @Schema(description = "扩展元数据")
    @Lob
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 50)
    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 50)
    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractTitle() {
        return contractTitle;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }

    public String getContractContent() {
        return contractContent;
    }

    public void setContractContent(String contractContent) {
        this.contractContent = contractContent;
    }

    public String getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    public ReviewStatus getStatus() {
        return status;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public RiskLevel getOverallRiskLevel() {
        return overallRiskLevel;
    }

    public void setOverallRiskLevel(RiskLevel overallRiskLevel) {
        this.overallRiskLevel = overallRiskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public String getRiskSummary() {
        return riskSummary;
    }

    public void setRiskSummary(String riskSummary) {
        this.riskSummary = riskSummary;
    }

    public Long getAiRequestId() {
        return aiRequestId;
    }

    public void setAiRequestId(Long aiRequestId) {
        this.aiRequestId = aiRequestId;
    }

    public Instant getReviewStartTime() {
        return reviewStartTime;
    }

    public void setReviewStartTime(Instant reviewStartTime) {
        this.reviewStartTime = reviewStartTime;
    }

    public Instant getReviewEndTime() {
        return reviewEndTime;
    }

    public void setReviewEndTime(Instant reviewEndTime) {
        this.reviewEndTime = reviewEndTime;
    }

    public Long getReviewDuration() {
        return reviewDuration;
    }

    public void setReviewDuration(Long reviewDuration) {
        this.reviewDuration = reviewDuration;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractReviewDTO)) {
            return false;
        }

        ContractReviewDTO contractReviewDTO = (ContractReviewDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, contractReviewDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractReviewDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", contractType='" + getContractType() + "'" +
            ", contractTitle='" + getContractTitle() + "'" +
            ", contractContent='" + getContractContent() + "'" +
            ", reviewResult='" + getReviewResult() + "'" +
            ", status='" + getStatus() + "'" +
            ", overallRiskLevel='" + getOverallRiskLevel() + "'" +
            ", riskScore=" + getRiskScore() +
            ", riskSummary='" + getRiskSummary() + "'" +
            ", aiRequestId=" + getAiRequestId() +
            ", reviewStartTime='" + getReviewStartTime() + "'" +
            ", reviewEndTime='" + getReviewEndTime() + "'" +
            ", reviewDuration=" + getReviewDuration() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
