/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyComplianceAssessmentDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：制度合规性评估DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 制度合规性评估DTO
 * <p>
 * 用于描述内部制度的合规性评估结果，
 * 包含法律法规符合性、行业标准符合性、内部制度一致性等评估信息。
 *
 * 主要功能：
 * 1. 法律法规合规性评估
 * 2. 行业标准符合性评估
 * 3. 内部制度一致性检查
 * 4. 合规风险识别
 */
@Schema(description = "制度合规性评估DTO")
public class PolicyComplianceAssessmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 整体合规等级 */
    @Schema(description = "整体合规等级")
    private ComplianceLevel overallComplianceLevel;

    /** 合规分数 (0-100) */
    @Schema(description = "合规分数 (0-100)")
    private Integer complianceScore;

    /** 法律法规合规性 */
    @Schema(description = "法律法规合规性")
    private LegalComplianceDTO legalCompliance;

    /** 行业标准符合性 */
    @Schema(description = "行业标准符合性")
    private IndustryStandardComplianceDTO industryStandardCompliance;

    /** 内部制度一致性 */
    @Schema(description = "内部制度一致性")
    private InternalPolicyConsistencyDTO internalPolicyConsistency;

    /** 合规风险点 */
    @Schema(description = "合规风险点")
    private List<ComplianceRiskDTO> complianceRisks;

    /** 不合规项目 */
    @Schema(description = "不合规项目")
    private List<NonComplianceItemDTO> nonComplianceItems;

    /** 改进建议 */
    @Schema(description = "改进建议")
    private List<String> improvementSuggestions;

    /** 监管要求 */
    @Schema(description = "监管要求")
    private List<String> regulatoryRequirements;

    /** 合规检查清单 */
    @Schema(description = "合规检查清单")
    private Map<String, Boolean> complianceChecklist;

    // 枚举：合规等级
    public enum ComplianceLevel {
        FULLY_COMPLIANT, // 完全合规
        MOSTLY_COMPLIANT, // 基本合规
        PARTIALLY_COMPLIANT, // 部分合规
        NON_COMPLIANT, // 不合规
    }

    // 内部类：法律法规合规性
    @Schema(description = "法律法规合规性")
    public static class LegalComplianceDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 合规状态 */
        @Schema(description = "合规状态")
        private ComplianceLevel status;

        /** 适用法律法规 */
        @Schema(description = "适用法律法规")
        private List<String> applicableLaws;

        /** 违反的法律法规 */
        @Schema(description = "违反的法律法规")
        private List<String> violatedLaws;

        /** 合规要求 */
        @Schema(description = "合规要求")
        private List<String> complianceRequirements;

        /** 法律风险评估 */
        @Schema(description = "法律风险评估")
        private String legalRiskAssessment;

        // Getters and Setters
        public ComplianceLevel getStatus() {
            return status;
        }

        public void setStatus(ComplianceLevel status) {
            this.status = status;
        }

        public List<String> getApplicableLaws() {
            return applicableLaws;
        }

        public void setApplicableLaws(List<String> applicableLaws) {
            this.applicableLaws = applicableLaws;
        }

        public List<String> getViolatedLaws() {
            return violatedLaws;
        }

        public void setViolatedLaws(List<String> violatedLaws) {
            this.violatedLaws = violatedLaws;
        }

        public List<String> getComplianceRequirements() {
            return complianceRequirements;
        }

        public void setComplianceRequirements(List<String> complianceRequirements) {
            this.complianceRequirements = complianceRequirements;
        }

        public String getLegalRiskAssessment() {
            return legalRiskAssessment;
        }

        public void setLegalRiskAssessment(String legalRiskAssessment) {
            this.legalRiskAssessment = legalRiskAssessment;
        }
    }

    // 内部类：行业标准符合性
    @Schema(description = "行业标准符合性")
    public static class IndustryStandardComplianceDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 合规状态 */
        @Schema(description = "合规状态")
        private ComplianceLevel status;

        /** 适用行业标准 */
        @Schema(description = "适用行业标准")
        private List<String> applicableStandards;

        /** 不符合的标准 */
        @Schema(description = "不符合的标准")
        private List<String> nonCompliantStandards;

        /** 最佳实践建议 */
        @Schema(description = "最佳实践建议")
        private List<String> bestPractices;

        // Getters and Setters
        public ComplianceLevel getStatus() {
            return status;
        }

        public void setStatus(ComplianceLevel status) {
            this.status = status;
        }

        public List<String> getApplicableStandards() {
            return applicableStandards;
        }

        public void setApplicableStandards(List<String> applicableStandards) {
            this.applicableStandards = applicableStandards;
        }

        public List<String> getNonCompliantStandards() {
            return nonCompliantStandards;
        }

        public void setNonCompliantStandards(List<String> nonCompliantStandards) {
            this.nonCompliantStandards = nonCompliantStandards;
        }

        public List<String> getBestPractices() {
            return bestPractices;
        }

        public void setBestPractices(List<String> bestPractices) {
            this.bestPractices = bestPractices;
        }
    }

    // 内部类：内部制度一致性
    @Schema(description = "内部制度一致性")
    public static class InternalPolicyConsistencyDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 一致性状态 */
        @Schema(description = "一致性状态")
        private ComplianceLevel status;

        /** 冲突的制度 */
        @Schema(description = "冲突的制度")
        private List<String> conflictingPolicies;

        /** 重复的条款 */
        @Schema(description = "重复的条款")
        private List<String> duplicatedClauses;

        /** 缺失的条款 */
        @Schema(description = "缺失的条款")
        private List<String> missingClauses;

        // Getters and Setters
        public ComplianceLevel getStatus() {
            return status;
        }

        public void setStatus(ComplianceLevel status) {
            this.status = status;
        }

        public List<String> getConflictingPolicies() {
            return conflictingPolicies;
        }

        public void setConflictingPolicies(List<String> conflictingPolicies) {
            this.conflictingPolicies = conflictingPolicies;
        }

        public List<String> getDuplicatedClauses() {
            return duplicatedClauses;
        }

        public void setDuplicatedClauses(List<String> duplicatedClauses) {
            this.duplicatedClauses = duplicatedClauses;
        }

        public List<String> getMissingClauses() {
            return missingClauses;
        }

        public void setMissingClauses(List<String> missingClauses) {
            this.missingClauses = missingClauses;
        }
    }

    // 内部类：合规风险
    @Schema(description = "合规风险")
    public static class ComplianceRiskDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 风险ID */
        @Schema(description = "风险ID")
        private String riskId;

        /** 风险描述 */
        @Schema(description = "风险描述")
        private String description;

        /** 风险等级 */
        @Schema(description = "风险等级")
        private String riskLevel;

        /** 影响范围 */
        @Schema(description = "影响范围")
        private String impact;

        // Getters and Setters
        public String getRiskId() {
            return riskId;
        }

        public void setRiskId(String riskId) {
            this.riskId = riskId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
        }

        public String getImpact() {
            return impact;
        }

        public void setImpact(String impact) {
            this.impact = impact;
        }
    }

    // 内部类：不合规项目
    @Schema(description = "不合规项目")
    public static class NonComplianceItemDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 项目ID */
        @Schema(description = "项目ID")
        private String itemId;

        /** 项目描述 */
        @Schema(description = "项目描述")
        private String description;

        /** 严重程度 */
        @Schema(description = "严重程度")
        private String severity;

        /** 整改建议 */
        @Schema(description = "整改建议")
        private String remediation;

        // Getters and Setters
        public String getItemId() {
            return itemId;
        }

        public void setItemId(String itemId) {
            this.itemId = itemId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getSeverity() {
            return severity;
        }

        public void setSeverity(String severity) {
            this.severity = severity;
        }

        public String getRemediation() {
            return remediation;
        }

        public void setRemediation(String remediation) {
            this.remediation = remediation;
        }
    }

    public PolicyComplianceAssessmentDTO() {}

    // Getters and Setters
    public ComplianceLevel getOverallComplianceLevel() {
        return overallComplianceLevel;
    }

    public void setOverallComplianceLevel(ComplianceLevel overallComplianceLevel) {
        this.overallComplianceLevel = overallComplianceLevel;
    }

    public Integer getComplianceScore() {
        return complianceScore;
    }

    public void setComplianceScore(Integer complianceScore) {
        this.complianceScore = complianceScore;
    }

    public LegalComplianceDTO getLegalCompliance() {
        return legalCompliance;
    }

    public void setLegalCompliance(LegalComplianceDTO legalCompliance) {
        this.legalCompliance = legalCompliance;
    }

    public IndustryStandardComplianceDTO getIndustryStandardCompliance() {
        return industryStandardCompliance;
    }

    public void setIndustryStandardCompliance(IndustryStandardComplianceDTO industryStandardCompliance) {
        this.industryStandardCompliance = industryStandardCompliance;
    }

    public InternalPolicyConsistencyDTO getInternalPolicyConsistency() {
        return internalPolicyConsistency;
    }

    public void setInternalPolicyConsistency(InternalPolicyConsistencyDTO internalPolicyConsistency) {
        this.internalPolicyConsistency = internalPolicyConsistency;
    }

    public List<ComplianceRiskDTO> getComplianceRisks() {
        return complianceRisks;
    }

    public void setComplianceRisks(List<ComplianceRiskDTO> complianceRisks) {
        this.complianceRisks = complianceRisks;
    }

    public List<NonComplianceItemDTO> getNonComplianceItems() {
        return nonComplianceItems;
    }

    public void setNonComplianceItems(List<NonComplianceItemDTO> nonComplianceItems) {
        this.nonComplianceItems = nonComplianceItems;
    }

    public List<String> getImprovementSuggestions() {
        return improvementSuggestions;
    }

    public void setImprovementSuggestions(List<String> improvementSuggestions) {
        this.improvementSuggestions = improvementSuggestions;
    }

    public List<String> getRegulatoryRequirements() {
        return regulatoryRequirements;
    }

    public void setRegulatoryRequirements(List<String> regulatoryRequirements) {
        this.regulatoryRequirements = regulatoryRequirements;
    }

    public Map<String, Boolean> getComplianceChecklist() {
        return complianceChecklist;
    }

    public void setComplianceChecklist(Map<String, Boolean> complianceChecklist) {
        this.complianceChecklist = complianceChecklist;
    }

    @Override
    public String toString() {
        return (
            "PolicyComplianceAssessmentDTO{" +
            "overallComplianceLevel=" +
            overallComplianceLevel +
            ", complianceScore=" +
            complianceScore +
            '}'
        );
    }
}
