package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "外规内化响应DTO")
public class RegulationInternalizationResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "内化结果")
    private String internalizationResult;
    
    public RegulationInternalizationResponseDTO() {}
    
    public String getInternalizationResult() { return internalizationResult; }
    public void setInternalizationResult(String internalizationResult) { this.internalizationResult = internalizationResult; }
}
