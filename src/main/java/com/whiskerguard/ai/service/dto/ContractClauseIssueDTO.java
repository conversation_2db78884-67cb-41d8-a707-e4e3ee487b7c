package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.ContractClauseIssue} entity.
 */
@Schema(description = "条款问题实体\n存储合同条款中发现的具体问题")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractClauseIssueDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "关联的审查记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long reviewId;

    @Schema(description = "条款内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String clauseText;

    @Size(max = 32)
    @Schema(description = "条款编号/位置")
    private String clauseNumber;

    @Size(max = 64)
    @Schema(description = "问题类型")
    private String issueType;

    @Schema(description = "问题描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @Lob
    private String issueDescription;

    @NotNull
    @Schema(description = "严重程度", requiredMode = Schema.RequiredMode.REQUIRED)
    private RiskLevel severity;

    @Schema(description = "法律风险说明")
    @Lob
    private String legalRisk;

    @Schema(description = "修改建议")
    @Lob
    private String suggestions;

    @Schema(description = "参考法规")
    @Lob
    private String referenceLaws;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private ContractReviewDTO review;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public String getClauseText() {
        return clauseText;
    }

    public void setClauseText(String clauseText) {
        this.clauseText = clauseText;
    }

    public String getClauseNumber() {
        return clauseNumber;
    }

    public void setClauseNumber(String clauseNumber) {
        this.clauseNumber = clauseNumber;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public String getIssueDescription() {
        return issueDescription;
    }

    public void setIssueDescription(String issueDescription) {
        this.issueDescription = issueDescription;
    }

    public RiskLevel getSeverity() {
        return severity;
    }

    public void setSeverity(RiskLevel severity) {
        this.severity = severity;
    }

    public String getLegalRisk() {
        return legalRisk;
    }

    public void setLegalRisk(String legalRisk) {
        this.legalRisk = legalRisk;
    }

    public String getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(String suggestions) {
        this.suggestions = suggestions;
    }

    public String getReferenceLaws() {
        return referenceLaws;
    }

    public void setReferenceLaws(String referenceLaws) {
        this.referenceLaws = referenceLaws;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReviewDTO getReview() {
        return review;
    }

    public void setReview(ContractReviewDTO review) {
        this.review = review;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractClauseIssueDTO)) {
            return false;
        }

        ContractClauseIssueDTO contractClauseIssueDTO = (ContractClauseIssueDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, contractClauseIssueDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractClauseIssueDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", reviewId=" + getReviewId() +
            ", clauseText='" + getClauseText() + "'" +
            ", clauseNumber='" + getClauseNumber() + "'" +
            ", issueType='" + getIssueType() + "'" +
            ", issueDescription='" + getIssueDescription() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", legalRisk='" + getLegalRisk() + "'" +
            ", suggestions='" + getSuggestions() + "'" +
            ", referenceLaws='" + getReferenceLaws() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", review=" + getReview() +
            "}";
    }
}
