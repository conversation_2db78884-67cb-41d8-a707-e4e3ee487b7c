package com.whiskerguard.ai.service.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 增强的AI响应DTO
 * <p>
 * 包含原始AI响应和法规引用增强信息，
 * 用于提供带有法规引用的智能AI响应。
 */
public class EnhancedAiResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原始AI响应内容
     */
    private String originalContent;

    /**
     * 带引用标记的增强内容
     */
    private String enhancedContent;

    /**
     * 法规引用列表
     */
    private List<LawReferenceDTO> references;

    /**
     * 引用ID到显示文本的映射
     * key: 引用标记ID (如: ref_1, ref_2)
     * value: 显示文本 (如: 《合同法》第52条)
     */
    private Map<String, String> referenceMap;

    /**
     * 引用统计信息
     */
    private ReferenceStatistics statistics;

    /**
     * 处理状态
     */
    private ProcessingStatus status;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;

    // 内部类：引用统计信息
    public static class ReferenceStatistics implements Serializable {

        private Integer totalReferences; // 总引用数
        private Integer lawReferences; // 法律引用数
        private Integer regulationReferences; // 法规引用数
        private Integer internalReferences; // 内部制度引用数
        private Double averageRelevanceScore; // 平均相关性得分

        // 构造函数
        public ReferenceStatistics() {}

        public ReferenceStatistics(
            Integer totalReferences,
            Integer lawReferences,
            Integer regulationReferences,
            Integer internalReferences
        ) {
            this.totalReferences = totalReferences;
            this.lawReferences = lawReferences;
            this.regulationReferences = regulationReferences;
            this.internalReferences = internalReferences;
        }

        // Getters and Setters
        public Integer getTotalReferences() {
            return totalReferences;
        }

        public void setTotalReferences(Integer totalReferences) {
            this.totalReferences = totalReferences;
        }

        public Integer getLawReferences() {
            return lawReferences;
        }

        public void setLawReferences(Integer lawReferences) {
            this.lawReferences = lawReferences;
        }

        public Integer getRegulationReferences() {
            return regulationReferences;
        }

        public void setRegulationReferences(Integer regulationReferences) {
            this.regulationReferences = regulationReferences;
        }

        public Integer getInternalReferences() {
            return internalReferences;
        }

        public void setInternalReferences(Integer internalReferences) {
            this.internalReferences = internalReferences;
        }

        public Double getAverageRelevanceScore() {
            return averageRelevanceScore;
        }

        public void setAverageRelevanceScore(Double averageRelevanceScore) {
            this.averageRelevanceScore = averageRelevanceScore;
        }
    }

    // 处理状态枚举
    public enum ProcessingStatus {
        SUCCESS, // 处理成功
        PARTIAL_SUCCESS, // 部分成功
        FAILED, // 处理失败
        NO_REFERENCES, // 未找到引用
    }

    // 构造函数
    public EnhancedAiResponse() {}

    public EnhancedAiResponse(String originalContent, String enhancedContent, List<LawReferenceDTO> references) {
        this.originalContent = originalContent;
        this.enhancedContent = enhancedContent;
        this.references = references;
        this.status = ProcessingStatus.SUCCESS;
    }

    // Getters and Setters
    public String getOriginalContent() {
        return originalContent;
    }

    public void setOriginalContent(String originalContent) {
        this.originalContent = originalContent;
    }

    public String getEnhancedContent() {
        return enhancedContent;
    }

    public void setEnhancedContent(String enhancedContent) {
        this.enhancedContent = enhancedContent;
    }

    public List<LawReferenceDTO> getReferences() {
        return references;
    }

    public void setReferences(List<LawReferenceDTO> references) {
        this.references = references;
    }

    public Map<String, String> getReferenceMap() {
        return referenceMap;
    }

    public void setReferenceMap(Map<String, String> referenceMap) {
        this.referenceMap = referenceMap;
    }

    public ReferenceStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(ReferenceStatistics statistics) {
        this.statistics = statistics;
    }

    public ProcessingStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessingStatus status) {
        this.status = status;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return (
            "EnhancedAiResponse{" +
            "originalContentLength=" +
            (originalContent != null ? originalContent.length() : 0) +
            ", enhancedContentLength=" +
            (enhancedContent != null ? enhancedContent.length() : 0) +
            ", referencesCount=" +
            (references != null ? references.size() : 0) +
            ", status=" +
            status +
            ", processingTimeMs=" +
            processingTimeMs +
            '}'
        );
    }
}
