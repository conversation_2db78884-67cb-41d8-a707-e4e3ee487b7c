package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.TaskStepDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.TaskStep}.
 */
public interface TaskStepService {
    /**
     * Save a taskStep.
     *
     * @param taskStepDTO the entity to save.
     * @return the persisted entity.
     */
    TaskStepDTO save(TaskStepDTO taskStepDTO);

    /**
     * Updates a taskStep.
     *
     * @param taskStepDTO the entity to update.
     * @return the persisted entity.
     */
    TaskStepDTO update(TaskStepDTO taskStepDTO);

    /**
     * Partially updates a taskStep.
     *
     * @param taskStepDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<TaskStepDTO> partialUpdate(TaskStepDTO taskStepDTO);

    /**
     * Get all the taskSteps.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TaskStepDTO> findAll(Pageable pageable);

    /**
     * Get all the taskSteps with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TaskStepDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" taskStep.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<TaskStepDTO> findOne(Long id);

    /**
     * Delete the "id" taskStep.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
