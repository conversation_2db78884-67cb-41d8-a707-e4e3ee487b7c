package com.whiskerguard.ai.service;

import com.whiskerguard.ai.client.SensitiveWordClient;
import com.whiskerguard.ai.client.dto.SensitiveWordFilterRequestDTO;
import com.whiskerguard.ai.client.dto.SensitiveWordFilterResponseDTO;
import java.util.Collections;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 敏感词过滤服务
 * <p>
 * 该服务封装了敏感词过滤微服务的调用逻辑，提供了简单的接口以供系统内各模块使用。
 * 通过该服务可以对内容进行敏感词过滤，确保输出内容符合合规要求。
 */
@Service
public class SensitiveWordFilterService {

    private static final Logger log = LoggerFactory.getLogger(SensitiveWordFilterService.class);

    private final SensitiveWordClient sensitiveWordClient;

    public SensitiveWordFilterService(SensitiveWordClient sensitiveWordClient) {
        this.sensitiveWordClient = sensitiveWordClient;
    }

    /**
     * 过滤内容中的敏感词
     * <p>
     * 将传入文本中的敏感词替换为 *，返回过滤后的内容
     *
     * @param content 需要过滤的内容
     * @return 过滤后的内容，如遇错误则返回原内容
     */
    public String filterContent(String content) {
        // 空内容直接返回
        if (content == null || content.isEmpty()) {
            return content;
        }

        try {
            // 调用敏感词过滤服务
            log.debug("开始对内容进行敏感词过滤, 内容长度: {}", content.length());
            String filteredContent = sensitiveWordClient.filterContent(content);
            log.debug("敏感词过滤完成, 过滤后内容长度: {}", filteredContent.length());
            return filteredContent;
        } catch (Exception e) {
            // 发生异常时记录错误但返回原内容，确保服务不中断
            log.error("敏感词过滤失败: {}", e.getMessage(), e);
            return content;
        }
    }

    /**
     * 过滤内容并获取详细的敏感词信息
     * <p>
     * 适用于需要了解具体敏感词信息的场景
     *
     * @param content 需要过滤的内容
     * @return 敏感词过滤响应DTO，包含原文、过滤后内容和敏感词信息
     */
    public SensitiveWordFilterResponseDTO filterContentDetailed(String content) {
        // 空内容直接返回空结果
        if (content == null || content.isEmpty()) {
            SensitiveWordFilterResponseDTO emptyResponse = new SensitiveWordFilterResponseDTO();
            emptyResponse.setOriginalContent("");
            emptyResponse.setFilteredContent("");
            emptyResponse.setContainsSensitiveWords(false);
            return emptyResponse;
        }

        try {
            // 调用敏感词过滤服务获取详细信息
            return sensitiveWordClient.filterContentDetailed(content);
        } catch (Exception e) {
            // 发生异常时构建一个基本响应
            log.error("获取敏感词详情失败: {}", e.getMessage(), e);
            SensitiveWordFilterResponseDTO fallbackResponse = new SensitiveWordFilterResponseDTO();
            fallbackResponse.setOriginalContent(content);
            fallbackResponse.setFilteredContent(content);
            fallbackResponse.setContainsSensitiveWords(false);
            return fallbackResponse;
        }
    }

    /**
     * 批量过滤多条内容
     *
     * @param contents 需要过滤的内容列表
     * @return 过滤结果列表
     */
    public List<SensitiveWordFilterResponseDTO> batchFilterContent(List<String> contents) {
        if (contents == null || contents.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 构建批量请求DTO
            SensitiveWordFilterRequestDTO request = new SensitiveWordFilterRequestDTO();
            request.setContents(contents);

            // 调用批量过滤API
            return sensitiveWordClient.batchFilterContent(request);
        } catch (Exception e) {
            log.error("批量敏感词过滤失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查内容是否包含敏感词
     *
     * @param content 需要检查的内容
     * @return true表示包含敏感词，false表示不包含
     */
    public boolean containsSensitiveWords(String content) {
        //测试阶段 先直接返回 false 不然太耗时间了
        return false; //返回 false 表示不包含敏感词
        // if (content == null || content.isEmpty()) {
        //     return false;
        // }

        // try {
        //     return sensitiveWordClient.containsSensitiveWords(content);
        // } catch (Exception e) {
        //     log.error("敏感词检测失败: {}", e.getMessage(), e);
        //     return false;
        // }
    }
}
