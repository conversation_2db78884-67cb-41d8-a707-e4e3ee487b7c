package com.whiskerguard.ai.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.invocation.AiResult;
import jakarta.annotation.PostConstruct;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * AI响应智能缓存服务
 * <p>
 * 提供多层缓存策略：
 * 1. 内存缓存 - 快速响应热点请求
 * 2. 请求去重 - 避免相同请求的并发执行
 * 3. 智能失效 - 基于内容和时间的失效策略
 */
@Service
public class AiResponseCacheService {

    private static final Logger log = LoggerFactory.getLogger(AiResponseCacheService.class);

    // 配置参数
    @Value("${whiskerguard.ai.cache.enabled:true}")
    private boolean cacheEnabled;

    @Value("${whiskerguard.ai.cache.max-size:1000}")
    private long maxSize;

    @Value("${whiskerguard.ai.cache.expire-after-write:30m}")
    private Duration expireAfterWrite;

    @Value("${whiskerguard.ai.cache.expire-after-access:10m}")
    private Duration expireAfterAccess;

    // 缓存实例
    private Cache<String, AiResult> responseCache;
    private Cache<String, String> ragCache;

    // 请求去重映射 - 防止相同请求并发执行
    private final ConcurrentHashMap<String, CompletableFuture<AiResult>> ongoingRequests = new ConcurrentHashMap<>();

    @PostConstruct
    public void initCache() {
        this.responseCache = Caffeine.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(expireAfterWrite)
            .expireAfterAccess(expireAfterAccess)
            .recordStats() // 启用统计
            .build();

        this.ragCache = Caffeine.newBuilder()
            .maximumSize(maxSize / 2) // RAG缓存设置为响应缓存的一半
            .expireAfterWrite(Duration.ofHours(2)) // RAG结果可以缓存更久
            .recordStats()
            .build();

        log.info(
            "AI缓存服务初始化完成 - 启用状态: {}, 最大缓存大小: {}, 写后过期: {}, 访问后过期: {}",
            cacheEnabled,
            maxSize,
            expireAfterWrite,
            expireAfterAccess
        );
    }

    /**
     * 获取缓存的AI响应或执行新的调用
     *
     * @param request AI调用请求
     * @param aiCallSupplier AI调用的执行函数
     * @return AI调用结果
     */
    public AiResult getCachedOrCompute(AiInvocationRequestDTO request, Supplier<AiResult> aiCallSupplier) {
        if (!cacheEnabled) {
            log.debug("缓存已禁用，直接执行AI调用");
            return aiCallSupplier.get();
        }

        String cacheKey = generateCacheKey(request);

        // 1. 尝试从缓存获取
        AiResult cachedResult = responseCache.getIfPresent(cacheKey);
        if (cachedResult != null) {
            log.info("缓存命中 - Key: {}, 跳过AI调用", maskCacheKey(cacheKey));
            return cachedResult;
        }

        // 2. 检查是否有正在进行的相同请求（请求去重）
        CompletableFuture<AiResult> ongoingRequest = ongoingRequests.get(cacheKey);
        if (ongoingRequest != null) {
            try {
                log.info("检测到相同请求正在执行，等待结果 - Key: {}", maskCacheKey(cacheKey));
                return ongoingRequest.get(); // 等待正在进行的请求完成
            } catch (Exception e) {
                log.warn("等待正在进行的请求失败，将重新执行 - Key: {}, Error: {}", maskCacheKey(cacheKey), e.getMessage());
                ongoingRequests.remove(cacheKey);
            }
        }

        // 3. 创建新的异步请求
        CompletableFuture<AiResult> futureResult = CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("缓存未命中，执行AI调用 - Key: {}", maskCacheKey(cacheKey));
                AiResult result = aiCallSupplier.get();

                // 缓存结果（只缓存成功的结果）
                if (result != null && result.getContent() != null && !result.getContent().trim().isEmpty()) {
                    responseCache.put(cacheKey, result);
                    log.debug("AI调用结果已缓存 - Key: {}, 内容长度: {}", maskCacheKey(cacheKey), result.getContent().length());
                }

                return result;
            } finally {
                // 清理正在进行的请求记录
                ongoingRequests.remove(cacheKey);
            }
        });

        // 4. 记录正在进行的请求
        ongoingRequests.put(cacheKey, futureResult);

        // 5. 同步等待结果
        try {
            return futureResult.get();
        } catch (Exception e) {
            log.error("AI调用执行失败 - Key: {}, Error: {}", maskCacheKey(cacheKey), e.getMessage(), e);
            ongoingRequests.remove(cacheKey);
            throw new RuntimeException("AI调用失败", e);
        }
    }

    /**
     * 缓存RAG增强结果
     *
     * @param prompt 原始提示词
     * @param enhancedPrompt 增强后的提示词
     */
    public void cacheRagResult(String prompt, String enhancedPrompt) {
        if (!cacheEnabled) return;

        String ragKey = generateRagCacheKey(prompt);
        ragCache.put(ragKey, enhancedPrompt);
        log.debug("RAG结果已缓存 - 原始长度: {}, 增强后长度: {}", prompt.length(), enhancedPrompt.length());
    }

    /**
     * 获取缓存的RAG结果
     *
     * @param prompt 原始提示词
     * @return 缓存的增强提示词，如果没有则返回null
     */
    public String getCachedRagResult(String prompt) {
        if (!cacheEnabled) return null;

        String ragKey = generateRagCacheKey(prompt);
        String cachedResult = ragCache.getIfPresent(ragKey);

        if (cachedResult != null) {
            log.debug("RAG缓存命中 - 原始长度: {}, 缓存长度: {}", prompt.length(), cachedResult.length());
        }

        return cachedResult;
    }

    /**
     * 手动清除缓存
     */
    public void clearCache() {
        responseCache.invalidateAll();
        ragCache.invalidateAll();
        ongoingRequests.clear();
        log.info("AI缓存已清空");
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        CacheStats responseStats = responseCache.stats();
        CacheStats ragStats = ragCache.stats();

        return new CacheStatistics(
            responseStats.hitCount(),
            responseStats.missCount(),
            responseStats.hitRate(),
            responseCache.estimatedSize(),
            ragStats.hitCount(),
            ragStats.missCount(),
            ragStats.hitRate(),
            ragCache.estimatedSize(),
            ongoingRequests.size()
        );
    }

    /**
     * 生成缓存键
     * 基于请求内容生成唯一的缓存键
     */
    private String generateCacheKey(AiInvocationRequestDTO request) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("ai:").append(request.getToolKey()).append(":");

        // 添加提示词的哈希
        keyBuilder.append(hashString(request.getPrompt())).append(":");

        // 添加元数据的哈希（如果存在）
        if (request.getMetadata() != null && !request.getMetadata().isEmpty()) {
            keyBuilder.append(hashString(request.getMetadata().toString()));
        } else {
            keyBuilder.append("no-metadata");
        }

        return keyBuilder.toString();
    }

    /**
     * 生成RAG缓存键
     */
    private String generateRagCacheKey(String prompt) {
        return "rag:" + hashString(prompt);
    }

    /**
     * 生成字符串哈希值
     */
    private String hashString(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString().substring(0, 16); // 取前16位
        } catch (NoSuchAlgorithmException e) {
            // 降级为简单哈希
            return String.valueOf(input.hashCode());
        }
    }

    /**
     * 屏蔽缓存键的敏感信息（用于日志）
     */
    private String maskCacheKey(String cacheKey) {
        if (cacheKey.length() > 20) {
            return cacheKey.substring(0, 10) + "***" + cacheKey.substring(cacheKey.length() - 7);
        }
        return cacheKey;
    }

    /**
     * 缓存统计信息DTO
     */
    public static class CacheStatistics {

        private final long responseHitCount;
        private final long responseMissCount;
        private final double responseHitRate;
        private final long responseCacheSize;
        private final long ragHitCount;
        private final long ragMissCount;
        private final double ragHitRate;
        private final long ragCacheSize;
        private final int ongoingRequestsCount;

        public CacheStatistics(
            long responseHitCount,
            long responseMissCount,
            double responseHitRate,
            long responseCacheSize,
            long ragHitCount,
            long ragMissCount,
            double ragHitRate,
            long ragCacheSize,
            int ongoingRequestsCount
        ) {
            this.responseHitCount = responseHitCount;
            this.responseMissCount = responseMissCount;
            this.responseHitRate = responseHitRate;
            this.responseCacheSize = responseCacheSize;
            this.ragHitCount = ragHitCount;
            this.ragMissCount = ragMissCount;
            this.ragHitRate = ragHitRate;
            this.ragCacheSize = ragCacheSize;
            this.ongoingRequestsCount = ongoingRequestsCount;
        }

        // Getters
        public long getResponseHitCount() {
            return responseHitCount;
        }

        public long getResponseMissCount() {
            return responseMissCount;
        }

        public double getResponseHitRate() {
            return responseHitRate;
        }

        public long getResponseCacheSize() {
            return responseCacheSize;
        }

        public long getRagHitCount() {
            return ragHitCount;
        }

        public long getRagMissCount() {
            return ragMissCount;
        }

        public double getRagHitRate() {
            return ragHitRate;
        }

        public long getRagCacheSize() {
            return ragCacheSize;
        }

        public int getOngoingRequestsCount() {
            return ongoingRequestsCount;
        }

        @Override
        public String toString() {
            return String.format(
                "CacheStats{response: %.2f%% hit rate (%d hits, %d misses, %d cached), " +
                "rag: %.2f%% hit rate (%d hits, %d misses, %d cached), ongoing: %d}",
                responseHitRate * 100,
                responseHitCount,
                responseMissCount,
                responseCacheSize,
                ragHitRate * 100,
                ragHitCount,
                ragMissCount,
                ragCacheSize,
                ongoingRequestsCount
            );
        }
    }
}
