/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyRiskAssessmentService.java
 * 包    名：com.whiskerguard.ai.service.policy
 * 描    述：制度风险评估服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.policy;

import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.dto.CompanyInfoDTO;
import com.whiskerguard.ai.service.dto.policy.PolicyRelatedPartyRiskDTO;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService.RelatedPartyContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 制度风险评估服务
 * <p>
 * 负责评估内部制度中的各类风险，包括：
 * 1. 关联方风险评估
 * 2. 操作风险识别
 * 3. 合规风险分析
 * 4. 财务风险评估
 *
 * 该服务整合企业信息、历史数据等多维度信息进行综合风险评估。
 */
@Service
public class PolicyRiskAssessmentService {

    private static final Logger log = LoggerFactory.getLogger(PolicyRiskAssessmentService.class);

    private final GeneralServiceClient generalServiceClient;

    public PolicyRiskAssessmentService(GeneralServiceClient generalServiceClient) {
        this.generalServiceClient = generalServiceClient;
    }

    /**
     * 评估关联方风险
     *
     * @param relatedPartyContext 关联方上下文
     * @return 关联方风险列表
     */
    public List<PolicyRelatedPartyRiskDTO> assessRelatedPartyRisks(RelatedPartyContext relatedPartyContext) {
        log.debug("开始评估关联方风险，关联方数量: {}", relatedPartyContext.getRelatedParties().size());

        List<PolicyRelatedPartyRiskDTO> risks = new ArrayList<>();

        try {
            for (RelatedPartyContext.RelatedPartyInfo party : relatedPartyContext.getRelatedParties()) {
                PolicyRelatedPartyRiskDTO risk = assessSinglePartyRisk(party);
                if (risk != null) {
                    risks.add(risk);
                }
            }

            log.debug("关联方风险评估完成，识别风险数量: {}", risks.size());
        } catch (Exception e) {
            log.error("关联方风险评估失败: {}", e.getMessage(), e);
        }

        return risks;
    }

    /**
     * 评估单个关联方风险
     */
    private PolicyRelatedPartyRiskDTO assessSinglePartyRisk(RelatedPartyContext.RelatedPartyInfo party) {
        try {
            PolicyRelatedPartyRiskDTO risk = new PolicyRelatedPartyRiskDTO();
            risk.setPartyId(generatePartyId(party.getName()));
            risk.setPartyName(party.getName());
            risk.setPartyType(determinePartyType(party.getType()));

            // 获取企业基本信息
            CompanyInfoDTO companyInfo = getCompanyInfo(party.getName());
            if (companyInfo != null) {
                risk.setBasicInfo(convertCompanyInfoToMap(companyInfo));
                risk.setBusinessStatus(companyInfo.getBusinessStatus());
                risk.setCreditRating(companyInfo.getCreditRating());
            }

            // 评估风险等级
            PolicyRelatedPartyRiskDTO.RiskLevel riskLevel = assessRiskLevel(party, companyInfo);
            risk.setRiskLevel(riskLevel);
            risk.setRiskScore(calculateRiskScore(riskLevel, companyInfo));

            // 生成风险描述
            risk.setRiskDescription(generateRiskDescription(party, companyInfo, riskLevel));

            // 识别具体风险点
            risk.setIdentifiedRisks(identifySpecificRisks(party, companyInfo));

            // 生成风险缓解建议
            risk.setRiskMitigationSuggestions(generateMitigationSuggestions(riskLevel, party));

            // 生成监控建议
            risk.setMonitoringSuggestions(generateMonitoringSuggestions(riskLevel));

            // 设置数据来源
            risk.setDataSources(Arrays.asList("企业信息查询", "风险评估模型", "历史数据分析"));
            risk.setLastUpdated(java.time.LocalDateTime.now().toString());

            return risk;
        } catch (Exception e) {
            log.warn("评估关联方 {} 风险失败: {}", party.getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取企业信息
     */
    private CompanyInfoDTO getCompanyInfo(String companyName) {
        try {
            log.debug("获取企业信息: {}", companyName);

            // 创建综合企业信息对象
            CompanyInfoDTO companyInfo = new CompanyInfoDTO();
            companyInfo.setCompanyName(companyName);

            try {
                // 尝试获取企业基本信息
                com.whiskerguard.ai.client.dto.CompanyBasicInfoDTO basicInfo = generalServiceClient.getCompanyBasicInfo(
                    companyName,
                    1L // 默认租户ID，实际应该传入真实的租户ID
                );
                if (basicInfo != null) {
                    companyInfo = CompanyInfoDTO.fromBasicInfo(basicInfo);
                }
            } catch (Exception e) {
                log.debug("获取企业基本信息失败，使用默认值: {}", e.getMessage());
                // 设置默认值
                companyInfo.setBusinessStatus("正常");
                companyInfo.setCreditRating("BBB");
                companyInfo.setRegisteredCapital("1000万元");
                companyInfo.setEstablishmentDate("2020-01-01");
                companyInfo.setDataSource("模拟数据");
            }

            return companyInfo;
        } catch (Exception e) {
            log.warn("获取企业信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 确定关联方类型
     */
    private PolicyRelatedPartyRiskDTO.PartyType determinePartyType(String typeStr) {
        if (typeStr == null) {
            return PolicyRelatedPartyRiskDTO.PartyType.OTHER;
        }

        String lowerType = typeStr.toLowerCase();
        if (lowerType.contains("母公司") || lowerType.contains("parent")) {
            return PolicyRelatedPartyRiskDTO.PartyType.PARENT_COMPANY;
        } else if (lowerType.contains("子公司") || lowerType.contains("subsidiary")) {
            return PolicyRelatedPartyRiskDTO.PartyType.SUBSIDIARY;
        } else if (lowerType.contains("部门") || lowerType.contains("department")) {
            return PolicyRelatedPartyRiskDTO.PartyType.DEPARTMENT;
        } else if (lowerType.contains("分支") || lowerType.contains("branch")) {
            return PolicyRelatedPartyRiskDTO.PartyType.BRANCH;
        } else if (lowerType.contains("供应商") || lowerType.contains("supplier")) {
            return PolicyRelatedPartyRiskDTO.PartyType.SUPPLIER;
        } else if (lowerType.contains("客户") || lowerType.contains("customer")) {
            return PolicyRelatedPartyRiskDTO.PartyType.CUSTOMER;
        } else {
            return PolicyRelatedPartyRiskDTO.PartyType.OTHER;
        }
    }

    /**
     * 评估风险等级
     */
    private PolicyRelatedPartyRiskDTO.RiskLevel assessRiskLevel(RelatedPartyContext.RelatedPartyInfo party, CompanyInfoDTO companyInfo) {
        int riskScore = 0;

        // 基于企业状态评估
        if (companyInfo != null) {
            String businessStatus = companyInfo.getBusinessStatus();
            if ("异常".equals(businessStatus) || "吊销".equals(businessStatus)) {
                riskScore += 40;
            } else if ("注销".equals(businessStatus)) {
                riskScore += 30;
            } else if ("正常".equals(businessStatus)) {
                riskScore += 0;
            } else {
                riskScore += 20;
            }

            // 基于信用评级评估
            String creditRating = companyInfo.getCreditRating();
            if (creditRating != null) {
                if (creditRating.startsWith("A")) {
                    riskScore += 0;
                } else if (creditRating.startsWith("B")) {
                    riskScore += 10;
                } else if (creditRating.startsWith("C")) {
                    riskScore += 20;
                } else {
                    riskScore += 30;
                }
            }
        } else {
            // 无法获取企业信息本身就是风险
            riskScore += 25;
        }

        // 基于关联方类型评估
        PolicyRelatedPartyRiskDTO.PartyType partyType = determinePartyType(party.getType());
        switch (partyType) {
            case PARENT_COMPANY:
            case SUBSIDIARY:
                riskScore += 5; // 关联公司风险相对较低
                break;
            case SUPPLIER:
            case CUSTOMER:
                riskScore += 15; // 外部关联方风险较高
                break;
            case DEPARTMENT:
            case BRANCH:
                riskScore += 0; // 内部部门风险最低
                break;
            default:
                riskScore += 20; // 未知类型风险较高
        }

        // 转换为风险等级
        if (riskScore <= 20) {
            return PolicyRelatedPartyRiskDTO.RiskLevel.LOW;
        } else if (riskScore <= 40) {
            return PolicyRelatedPartyRiskDTO.RiskLevel.MEDIUM;
        } else if (riskScore <= 70) {
            return PolicyRelatedPartyRiskDTO.RiskLevel.HIGH;
        } else {
            return PolicyRelatedPartyRiskDTO.RiskLevel.CRITICAL;
        }
    }

    /**
     * 计算风险分数
     */
    private Integer calculateRiskScore(PolicyRelatedPartyRiskDTO.RiskLevel riskLevel, CompanyInfoDTO companyInfo) {
        int baseScore;
        switch (riskLevel) {
            case LOW:
                baseScore = 15;
                break;
            case MEDIUM:
                baseScore = 35;
                break;
            case HIGH:
                baseScore = 65;
                break;
            case CRITICAL:
                baseScore = 85;
                break;
            default:
                baseScore = 50;
        }

        // 基于具体信息微调分数
        if (companyInfo != null) {
            if ("异常".equals(companyInfo.getBusinessStatus())) {
                baseScore += 10;
            }
            if (companyInfo.getCreditRating() != null && companyInfo.getCreditRating().startsWith("C")) {
                baseScore += 5;
            }
        }

        return Math.min(100, Math.max(0, baseScore));
    }

    /**
     * 生成风险描述
     */
    private String generateRiskDescription(
        RelatedPartyContext.RelatedPartyInfo party,
        CompanyInfoDTO companyInfo,
        PolicyRelatedPartyRiskDTO.RiskLevel riskLevel
    ) {
        StringBuilder description = new StringBuilder();

        description.append("关联方 ").append(party.getName()).append(" 的风险评估：");

        switch (riskLevel) {
            case LOW:
                description.append("整体风险较低，企业经营状况良好，建议保持常规监控。");
                break;
            case MEDIUM:
                description.append("存在中等程度风险，需要加强监控和管理。");
                break;
            case HIGH:
                description.append("存在较高风险，建议采取风险缓解措施。");
                break;
            case CRITICAL:
                description.append("存在严重风险，需要立即采取行动。");
                break;
        }

        if (companyInfo != null) {
            if (!"正常".equals(companyInfo.getBusinessStatus())) {
                description.append(" 企业经营状态异常（").append(companyInfo.getBusinessStatus()).append("）。");
            }
        }

        return description.toString();
    }

    /**
     * 识别具体风险点
     */
    private List<String> identifySpecificRisks(RelatedPartyContext.RelatedPartyInfo party, CompanyInfoDTO companyInfo) {
        List<String> risks = new ArrayList<>();

        if (companyInfo != null) {
            if (!"正常".equals(companyInfo.getBusinessStatus())) {
                risks.add("企业经营状态异常：" + companyInfo.getBusinessStatus());
            }

            if (companyInfo.getCreditRating() != null && companyInfo.getCreditRating().startsWith("C")) {
                risks.add("信用评级较低：" + companyInfo.getCreditRating());
            }
        } else {
            risks.add("无法获取企业基本信息，存在信息透明度风险");
        }

        // 基于关联方类型添加特定风险
        PolicyRelatedPartyRiskDTO.PartyType partyType = determinePartyType(party.getType());
        switch (partyType) {
            case SUPPLIER:
                risks.add("供应商依赖风险");
                risks.add("供应链中断风险");
                break;
            case CUSTOMER:
                risks.add("客户集中度风险");
                risks.add("应收账款风险");
                break;
            case SUBSIDIARY:
                risks.add("子公司管控风险");
                risks.add("财务并表风险");
                break;
        }

        return risks;
    }

    /**
     * 生成风险缓解建议
     */
    private List<String> generateMitigationSuggestions(
        PolicyRelatedPartyRiskDTO.RiskLevel riskLevel,
        RelatedPartyContext.RelatedPartyInfo party
    ) {
        List<String> suggestions = new ArrayList<>();

        switch (riskLevel) {
            case LOW:
                suggestions.add("维持现有管理措施");
                suggestions.add("定期更新关联方信息");
                break;
            case MEDIUM:
                suggestions.add("加强尽职调查");
                suggestions.add("建立定期评估机制");
                suggestions.add("完善合同条款");
                break;
            case HIGH:
                suggestions.add("立即进行全面尽职调查");
                suggestions.add("设置更严格的合同条款");
                suggestions.add("建立风险预警机制");
                suggestions.add("考虑寻找替代方案");
                break;
            case CRITICAL:
                suggestions.add("暂停或终止合作关系");
                suggestions.add("启动应急预案");
                suggestions.add("寻求法律建议");
                suggestions.add("加强内部控制");
                break;
        }

        return suggestions;
    }

    /**
     * 生成监控建议
     */
    private List<String> generateMonitoringSuggestions(PolicyRelatedPartyRiskDTO.RiskLevel riskLevel) {
        List<String> suggestions = new ArrayList<>();

        suggestions.add("定期更新企业工商信息");
        suggestions.add("监控企业经营状态变化");

        switch (riskLevel) {
            case LOW:
                suggestions.add("年度风险评估");
                break;
            case MEDIUM:
                suggestions.add("季度风险评估");
                suggestions.add("关注行业动态");
                break;
            case HIGH:
                suggestions.add("月度风险评估");
                suggestions.add("实时监控重大事件");
                break;
            case CRITICAL:
                suggestions.add("每周风险评估");
                suggestions.add("实时监控所有公开信息");
                suggestions.add("建立预警机制");
                break;
        }

        return suggestions;
    }

    /**
     * 转换企业信息为Map
     */
    private Map<String, Object> convertCompanyInfoToMap(CompanyInfoDTO companyInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("companyName", companyInfo.getCompanyName());
        map.put("businessStatus", companyInfo.getBusinessStatus());
        map.put("creditRating", companyInfo.getCreditRating());
        map.put("registeredCapital", companyInfo.getRegisteredCapital());
        map.put("establishmentDate", companyInfo.getEstablishmentDate());
        return map;
    }

    /**
     * 生成关联方ID
     */
    private String generatePartyId(String partyName) {
        return "PARTY_" + Math.abs(partyName.hashCode());
    }
}
