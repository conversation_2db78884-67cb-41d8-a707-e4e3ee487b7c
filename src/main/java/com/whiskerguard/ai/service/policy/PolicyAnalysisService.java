/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyAnalysisService.java
 * 包    名：com.whiskerguard.ai.service.policy
 * 描    述：制度分析服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.policy;

import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService.PolicyAnalysisContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 制度分析服务
 * <p>
 * 负责对内部制度内容进行结构化分析，包括：
 * 1. 条款提取和分类
 * 2. 部门和职责识别
 * 3. 业务流程分析
 * 4. 关键信息提取
 *
 * 该服务为后续的风险评估和合规检查提供基础数据。
 */
@Service
public class PolicyAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(PolicyAnalysisService.class);

    // 常见的条款标识符模式
    private static final Pattern CLAUSE_PATTERN = Pattern.compile(
        "第[一二三四五六七八九十\\d]+[条章节]|[\\d]+\\.|[\\d]+、|\\([\\d]+\\)|（[\\d]+）"
    );

    // 部门识别模式
    private static final Pattern DEPARTMENT_PATTERN = Pattern.compile(
        "(人事|财务|法务|采购|销售|市场|技术|研发|生产|质量|安全|行政|审计|风控|合规|IT|信息|客服|物流|仓储)部门?|" +
        "(总经理|副总|经理|主管|专员|助理)办公室|" +
        "(董事会|监事会|股东大会|管理层|高管)"
    );

    // 流程识别模式
    private static final Pattern PROCESS_PATTERN = Pattern.compile(
        "(申请|审批|审核|批准|签署|执行|监督|检查|评估|报告|备案|归档|流程|程序|步骤|环节)"
    );

    /**
     * 分析制度内容
     *
     * @param request 审查请求
     * @return 分析上下文
     */
    public PolicyAnalysisContext analyzePolicyContent(InternalPolicyReviewRequestDTO request) {
        log.debug("开始分析制度内容，制度类型: {}", request.getPolicyType());

        PolicyAnalysisContext context = new PolicyAnalysisContext();

        try {
            String content = request.getPolicyContent();

            // 1. 提取关键条款
            List<String> keyClauses = extractKeyClauses(content);
            context.setKeyClauses(keyClauses);
            log.debug("提取到 {} 个关键条款", keyClauses.size());

            // 2. 识别相关部门
            List<String> departments = identifyDepartments(content);
            context.setDepartments(departments);
            log.debug("识别到 {} 个相关部门", departments.size());

            // 3. 分析业务流程
            List<String> processes = analyzeBusinessProcesses(content);
            context.setProcesses(processes);
            log.debug("识别到 {} 个业务流程", processes.size());

            // 4. 提取元数据
            Map<String, Object> metadata = extractMetadata(request, content);
            context.setMetadata(metadata);

            log.debug("制度内容分析完成");
            return context;
        } catch (Exception e) {
            log.error("制度内容分析失败: {}", e.getMessage(), e);
            // 返回空的上下文，不影响主流程
            return new PolicyAnalysisContext();
        }
    }

    /**
     * 提取关键条款
     */
    private List<String> extractKeyClauses(String content) {
        List<String> clauses = new ArrayList<>();

        try {
            // 按行分割内容
            String[] lines = content.split("\n");
            StringBuilder currentClause = new StringBuilder();
            boolean inClause = false;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                // 检查是否是条款开始
                Matcher matcher = CLAUSE_PATTERN.matcher(line);
                if (matcher.find()) {
                    // 保存前一个条款
                    if (inClause && currentClause.length() > 0) {
                        String clauseText = currentClause.toString().trim();
                        if (clauseText.length() > 10) { // 过滤太短的条款
                            clauses.add(clauseText);
                        }
                    }

                    // 开始新条款
                    currentClause = new StringBuilder();
                    currentClause.append(line);
                    inClause = true;
                } else if (inClause) {
                    // 继续当前条款
                    currentClause.append(" ").append(line);
                }

                // 限制条款长度，避免过长
                if (currentClause.length() > 1000) {
                    String clauseText = currentClause.toString().trim();
                    clauses.add(clauseText);
                    currentClause = new StringBuilder();
                    inClause = false;
                }
            }

            // 保存最后一个条款
            if (inClause && currentClause.length() > 10) {
                clauses.add(currentClause.toString().trim());
            }

            // 限制条款数量，避免过多
            if (clauses.size() > 50) {
                clauses = clauses.subList(0, 50);
            }
        } catch (Exception e) {
            log.warn("提取关键条款失败: {}", e.getMessage());
        }

        return clauses;
    }

    /**
     * 识别相关部门
     */
    private List<String> identifyDepartments(String content) {
        List<String> departments = new ArrayList<>();

        try {
            Matcher matcher = DEPARTMENT_PATTERN.matcher(content);
            while (matcher.find()) {
                String department = matcher.group().trim();
                if (!departments.contains(department)) {
                    departments.add(department);
                }

                // 限制部门数量
                if (departments.size() >= 20) {
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("识别相关部门失败: {}", e.getMessage());
        }

        return departments;
    }

    /**
     * 分析业务流程
     */
    private List<String> analyzeBusinessProcesses(String content) {
        List<String> processes = new ArrayList<>();

        try {
            // 查找包含流程关键词的句子
            String[] sentences = content.split("[。！？；]");

            for (String sentence : sentences) {
                sentence = sentence.trim();
                if (sentence.length() < 10 || sentence.length() > 200) {
                    continue;
                }

                Matcher matcher = PROCESS_PATTERN.matcher(sentence);
                if (matcher.find()) {
                    processes.add(sentence);
                }

                // 限制流程数量
                if (processes.size() >= 30) {
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("分析业务流程失败: {}", e.getMessage());
        }

        return processes;
    }

    /**
     * 提取元数据
     */
    private Map<String, Object> extractMetadata(InternalPolicyReviewRequestDTO request, String content) {
        Map<String, Object> metadata = new HashMap<>();

        try {
            // 基本信息
            metadata.put("policyType", request.getPolicyType());
            metadata.put("department", request.getDepartment());
            metadata.put("contentLength", content.length());
            metadata.put("wordCount", content.split("\\s+").length);

            // 内容特征
            metadata.put("hasNumberedClauses", CLAUSE_PATTERN.matcher(content).find());
            metadata.put("hasDepartmentReferences", DEPARTMENT_PATTERN.matcher(content).find());
            metadata.put("hasProcessDescriptions", PROCESS_PATTERN.matcher(content).find());

            // 复杂度评估
            int complexity = calculateComplexity(content);
            metadata.put("complexityScore", complexity);

            // 关键词密度
            Map<String, Integer> keywordDensity = calculateKeywordDensity(content);
            metadata.put("keywordDensity", keywordDensity);
        } catch (Exception e) {
            log.warn("提取元数据失败: {}", e.getMessage());
        }

        return metadata;
    }

    /**
     * 计算内容复杂度
     */
    private int calculateComplexity(String content) {
        int complexity = 0;

        try {
            // 基于多个因素计算复杂度
            int length = content.length();
            int sentences = content.split("[。！？；]").length;
            int clauses = CLAUSE_PATTERN.matcher(content).groupCount();
            int departments = DEPARTMENT_PATTERN.matcher(content).groupCount();

            // 长度因子 (0-30分)
            complexity += Math.min(30, length / 1000);

            // 句子数量因子 (0-25分)
            complexity += Math.min(25, sentences / 10);

            // 条款数量因子 (0-25分)
            complexity += Math.min(25, clauses * 2);

            // 部门涉及因子 (0-20分)
            complexity += Math.min(20, departments * 3);
        } catch (Exception e) {
            log.warn("计算复杂度失败: {}", e.getMessage());
            complexity = 50; // 默认中等复杂度
        }

        return Math.min(100, complexity);
    }

    /**
     * 计算关键词密度
     */
    private Map<String, Integer> calculateKeywordDensity(String content) {
        Map<String, Integer> density = new HashMap<>();

        try {
            // 定义关键词列表
            List<String> keywords = Arrays.asList(
                "审批",
                "权限",
                "责任",
                "流程",
                "制度",
                "规定",
                "要求",
                "标准",
                "程序",
                "管理",
                "监督",
                "检查",
                "评估",
                "风险",
                "合规"
            );

            String lowerContent = content.toLowerCase();

            for (String keyword : keywords) {
                int count = 0;
                int index = 0;
                while ((index = lowerContent.indexOf(keyword, index)) != -1) {
                    count++;
                    index += keyword.length();
                }
                if (count > 0) {
                    density.put(keyword, count);
                }
            }
        } catch (Exception e) {
            log.warn("计算关键词密度失败: {}", e.getMessage());
        }

        return density;
    }
}
