package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 提示词模板管理服务接口
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplate}.
 */
public interface PromptTemplateService {
    /**
     * 保存提示词模板
     * Save a promptTemplate.
     *
     * @param promptTemplateDTO 要保存的实体 the entity to save.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateDTO save(PromptTemplateDTO promptTemplateDTO);

    /**
     * 更新提示词模板
     * Updates a promptTemplate.
     *
     * @param promptTemplateDTO 要更新的实体 the entity to update.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateDTO update(PromptTemplateDTO promptTemplateDTO);

    /**
     * 部分更新提示词模板
     * Partially updates a promptTemplate.
     *
     * @param promptTemplateDTO 要部分更新的实体 the entity to update partially.
     * @return 持久化后的实体 the persisted entity.
     */
    Optional<PromptTemplateDTO> partialUpdate(PromptTemplateDTO promptTemplateDTO);

    /**
     * 获取所有提示词模板
     * Get all the promptTemplates.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 实体列表 the list of entities.
     */
    Page<PromptTemplateDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取提示词模板
     * Get the "id" promptTemplate.
     *
     * @param id 实体ID the id of the entity.
     * @return 实体 the entity.
     */
    Optional<PromptTemplateDTO> findOne(Long id);

    /**
     * 根据ID删除提示词模板
     * Delete the "id" promptTemplate.
     *
     * @param id 实体ID the id of the entity.
     */
    void delete(Long id);
}
