package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 提示词模板变量管理服务接口
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplateVariable}.
 */
public interface PromptTemplateVariableService {
    /**
     * 保存提示词模板变量
     * Save a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO 要保存的实体 the entity to save.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateVariableDTO save(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * 更新提示词模板变量
     * Updates a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO 要更新的实体 the entity to update.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateVariableDTO update(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * 部分更新提示词模板变量
     * Partially updates a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO 要部分更新的实体 the entity to update partially.
     * @return 持久化后的实体 the persisted entity.
     */
    Optional<PromptTemplateVariableDTO> partialUpdate(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * 获取所有提示词模板变量
     * Get all the promptTemplateVariables.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 实体列表 the list of entities.
     */
    Page<PromptTemplateVariableDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取提示词模板变量
     * Get the "id" promptTemplateVariable.
     *
     * @param id 实体ID the id of the entity.
     * @return 实体 the entity.
     */
    Optional<PromptTemplateVariableDTO> findOne(Long id);

    /**
     * 根据ID删除提示词模板变量
     * Delete the "id" promptTemplateVariable.
     *
     * @param id 实体ID the id of the entity.
     */
    void delete(Long id);
}
