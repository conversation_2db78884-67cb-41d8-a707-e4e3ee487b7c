package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.enumeration.VariableType;
import java.util.List;

/**
 * 提示词变量信息
 * <p>
 * 包含模板所需的变量详细信息，用于前端展示和验证。
 */
public class PromptVariableInfo {

    /**
     * 模板键
     */
    private String templateKey;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 变量列表
     */
    private List<VariableInfo> variables;

    public PromptVariableInfo() {}

    public PromptVariableInfo(String templateKey, String templateName, List<VariableInfo> variables) {
        this.templateKey = templateKey;
        this.templateName = templateName;
        this.variables = variables;
    }

    // Getters and Setters
    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public List<VariableInfo> getVariables() {
        return variables;
    }

    public void setVariables(List<VariableInfo> variables) {
        this.variables = variables;
    }

    /**
     * 变量信息内部类
     */
    public static class VariableInfo {

        private String variableName;
        private String displayName;
        private String description;
        private VariableType variableType;
        private boolean isRequired;
        private String defaultValue;
        private String validationRule;
        private String exampleValue;
        private Integer sortOrder;

        public VariableInfo() {}

        public VariableInfo(String variableName, String displayName, VariableType variableType, boolean isRequired) {
            this.variableName = variableName;
            this.displayName = displayName;
            this.variableType = variableType;
            this.isRequired = isRequired;
        }

        // Getters and Setters
        public String getVariableName() {
            return variableName;
        }

        public void setVariableName(String variableName) {
            this.variableName = variableName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public VariableType getVariableType() {
            return variableType;
        }

        public void setVariableType(VariableType variableType) {
            this.variableType = variableType;
        }

        public boolean isRequired() {
            return isRequired;
        }

        public void setRequired(boolean required) {
            isRequired = required;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public String getValidationRule() {
            return validationRule;
        }

        public void setValidationRule(String validationRule) {
            this.validationRule = validationRule;
        }

        public String getExampleValue() {
            return exampleValue;
        }

        public void setExampleValue(String exampleValue) {
            this.exampleValue = exampleValue;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        @Override
        public String toString() {
            return (
                "VariableInfo{" +
                "variableName='" +
                variableName +
                '\'' +
                ", displayName='" +
                displayName +
                '\'' +
                ", variableType=" +
                variableType +
                ", isRequired=" +
                isRequired +
                '}'
            );
        }
    }

    @Override
    public String toString() {
        return (
            "PromptVariableInfo{" +
            "templateKey='" +
            templateKey +
            '\'' +
            ", templateName='" +
            templateName +
            '\'' +
            ", variables=" +
            variables +
            '}'
        );
    }
}
