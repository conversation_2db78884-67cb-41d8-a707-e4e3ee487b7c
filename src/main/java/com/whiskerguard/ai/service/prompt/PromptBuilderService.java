package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import java.util.Map;

/**
 * 提示词构建服务接口
 * <p>
 * 提供动态提示词构建功能，支持模板化管理、变量替换、
 * 租户个性化配置等功能。是整个提示词管理系统的核心接口。
 */
public interface PromptBuilderService {
    /**
     * 根据模板键构建提示词
     *
     * @param templateKey 模板键
     * @param tenantId 租户ID
     * @param variables 变量值映射
     * @return 构建后的提示词
     */
    String buildPrompt(String templateKey, Long tenantId, Map<String, Object> variables);

    /**
     * 根据模板类型构建提示词
     * 自动选择最优模板（租户自定义 > 系统默认）
     *
     * @param templateType 模板类型
     * @param tenantId 租户ID
     * @param variables 变量值映射
     * @return 构建后的提示词
     */
    String buildPrompt(PromptTemplateType templateType, Long tenantId, Map<String, Object> variables);

    /**
     * 构建增强提示词（包含RAG增强）
     *
     * @param templateKey 模板键
     * @param tenantId 租户ID
     * @param variables 变量值映射
     * @param enableRagEnhancement 是否启用RAG增强
     * @return 构建后的提示词
     */
    String buildEnhancedPrompt(String templateKey, Long tenantId, Map<String, Object> variables, boolean enableRagEnhancement);

    /**
     * 构建提示词请求对象
     *
     * @param request 提示词构建请求
     * @return 构建后的提示词
     */
    String buildPrompt(PromptBuildRequest request);

    /**
     * 验证模板变量
     *
     * @param templateKey 模板键
     * @param tenantId 租户ID
     * @param variables 变量值映射
     * @return 验证结果
     */
    PromptValidationResult validateVariables(String templateKey, Long tenantId, Map<String, Object> variables);

    /**
     * 获取模板所需的变量列表
     *
     * @param templateKey 模板键
     * @param tenantId 租户ID
     * @return 变量列表
     */
    PromptVariableInfo getRequiredVariables(String templateKey, Long tenantId);

    /**
     * 预览提示词（用于测试）
     *
     * @param templateKey 模板键
     * @param tenantId 租户ID
     * @param variables 变量值映射
     * @return 预览结果
     */
    PromptPreviewResult previewPrompt(String templateKey, Long tenantId, Map<String, Object> variables);
}
