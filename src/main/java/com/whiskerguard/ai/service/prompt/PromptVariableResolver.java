package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.enumeration.VariableType;
import java.util.Map;

/**
 * 提示词变量解析服务接口
 * <p>
 * 负责解析和处理提示词模板中的各种类型变量，包括系统变量、
 * 业务变量、RAG变量、自定义变量等。
 */
public interface PromptVariableResolver {
    /**
     * 解析模板中的所有变量
     *
     * @param templateContent 模板内容
     * @param tenantId 租户ID
     * @param providedVariables 用户提供的变量值
     * @return 解析后的变量值映射
     */
    Map<String, Object> resolveVariables(String templateContent, Long tenantId, Map<String, Object> providedVariables);

    /**
     * 解析系统变量
     *
     * @param tenantId 租户ID
     * @return 系统变量映射
     */
    Map<String, Object> resolveSystemVariables(Long tenantId);

    /**
     * 解析业务变量
     *
     * @param tenantId 租户ID
     * @param providedVariables 用户提供的变量值
     * @return 业务变量映射
     */
    Map<String, Object> resolveBusinessVariables(Long tenantId, Map<String, Object> providedVariables);

    /**
     * 解析RAG变量
     *
     * @param tenantId 租户ID
     * @param context 上下文信息
     * @return RAG变量映射
     */
    Map<String, Object> resolveRagVariables(Long tenantId, String context);

    /**
     * 解析自定义变量
     *
     * @param tenantId 租户ID
     * @param providedVariables 用户提供的变量值
     * @return 自定义变量映射
     */
    Map<String, Object> resolveCustomVariables(Long tenantId, Map<String, Object> providedVariables);

    /**
     * 解析计算变量
     *
     * @param tenantId 租户ID
     * @param baseVariables 基础变量
     * @return 计算变量映射
     */
    Map<String, Object> resolveComputedVariables(Long tenantId, Map<String, Object> baseVariables);

    /**
     * 解析外部变量
     *
     * @param tenantId 租户ID
     * @param externalContext 外部上下文
     * @return 外部变量映射
     */
    Map<String, Object> resolveExternalVariables(Long tenantId, Map<String, Object> externalContext);

    /**
     * 替换模板中的变量占位符
     *
     * @param templateContent 模板内容
     * @param variables 变量值映射
     * @return 替换后的内容
     */
    String replaceVariables(String templateContent, Map<String, Object> variables);

    /**
     * 提取模板中的变量名列表
     *
     * @param templateContent 模板内容
     * @return 变量名列表
     */
    java.util.Set<String> extractVariableNames(String templateContent);

    /**
     * 验证变量值
     *
     * @param variableName 变量名
     * @param variableType 变量类型
     * @param value 变量值
     * @param validationRule 验证规则
     * @return 验证是否通过
     */
    boolean validateVariableValue(String variableName, VariableType variableType, Object value, String validationRule);

    /**
     * 获取变量的默认值
     *
     * @param variableName 变量名
     * @param variableType 变量类型
     * @param tenantId 租户ID
     * @return 默认值
     */
    Object getDefaultValue(String variableName, VariableType variableType, Long tenantId);
}
