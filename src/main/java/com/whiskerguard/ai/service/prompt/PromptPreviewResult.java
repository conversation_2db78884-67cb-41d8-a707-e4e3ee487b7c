package com.whiskerguard.ai.service.prompt;

import java.time.Instant;
import java.util.Map;

/**
 * 提示词预览结果
 * <p>
 * 用于模板测试和预览功能，包含构建后的提示词和相关信息。
 */
public class PromptPreviewResult {

    /**
     * 预览是否成功
     */
    private boolean success;

    /**
     * 构建后的提示词内容
     */
    private String promptContent;

    /**
     * 使用的模板键
     */
    private String templateKey;

    /**
     * 使用的模板名称
     */
    private String templateName;

    /**
     * 模板版本
     */
    private Integer templateVersion;

    /**
     * 实际使用的变量值
     */
    private Map<String, Object> resolvedVariables;

    /**
     * 构建耗时（毫秒）
     */
    private Long buildDuration;

    /**
     * 构建时间
     */
    private Instant buildTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 警告信息
     */
    private String warningMessage;

    /**
     * 是否使用了RAG增强
     */
    private boolean ragEnhanced;

    /**
     * 提示词字符数
     */
    private Integer characterCount;

    /**
     * 提示词Token数（估算）
     */
    private Integer estimatedTokenCount;

    public PromptPreviewResult() {
        this.buildTime = Instant.now();
    }

    /**
     * 创建成功的预览结果
     */
    public static PromptPreviewResult success(String promptContent, String templateKey) {
        PromptPreviewResult result = new PromptPreviewResult();
        result.setSuccess(true);
        result.setPromptContent(promptContent);
        result.setTemplateKey(templateKey);
        result.setCharacterCount(promptContent != null ? promptContent.length() : 0);
        // 简单估算Token数（1个Token约等于4个字符）
        result.setEstimatedTokenCount(promptContent != null ? promptContent.length() / 4 : 0);
        return result;
    }

    /**
     * 创建失败的预览结果
     */
    public static PromptPreviewResult failure(String errorMessage) {
        PromptPreviewResult result = new PromptPreviewResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getPromptContent() {
        return promptContent;
    }

    public void setPromptContent(String promptContent) {
        this.promptContent = promptContent;
        if (promptContent != null) {
            this.characterCount = promptContent.length();
            this.estimatedTokenCount = promptContent.length() / 4;
        }
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getTemplateVersion() {
        return templateVersion;
    }

    public void setTemplateVersion(Integer templateVersion) {
        this.templateVersion = templateVersion;
    }

    public Map<String, Object> getResolvedVariables() {
        return resolvedVariables;
    }

    public void setResolvedVariables(Map<String, Object> resolvedVariables) {
        this.resolvedVariables = resolvedVariables;
    }

    public Long getBuildDuration() {
        return buildDuration;
    }

    public void setBuildDuration(Long buildDuration) {
        this.buildDuration = buildDuration;
    }

    public Instant getBuildTime() {
        return buildTime;
    }

    public void setBuildTime(Instant buildTime) {
        this.buildTime = buildTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getWarningMessage() {
        return warningMessage;
    }

    public void setWarningMessage(String warningMessage) {
        this.warningMessage = warningMessage;
    }

    public boolean isRagEnhanced() {
        return ragEnhanced;
    }

    public void setRagEnhanced(boolean ragEnhanced) {
        this.ragEnhanced = ragEnhanced;
    }

    public Integer getCharacterCount() {
        return characterCount;
    }

    public void setCharacterCount(Integer characterCount) {
        this.characterCount = characterCount;
    }

    public Integer getEstimatedTokenCount() {
        return estimatedTokenCount;
    }

    public void setEstimatedTokenCount(Integer estimatedTokenCount) {
        this.estimatedTokenCount = estimatedTokenCount;
    }

    @Override
    public String toString() {
        return (
            "PromptPreviewResult{" +
            "success=" +
            success +
            ", templateKey='" +
            templateKey +
            '\'' +
            ", templateName='" +
            templateName +
            '\'' +
            ", characterCount=" +
            characterCount +
            ", estimatedTokenCount=" +
            estimatedTokenCount +
            ", ragEnhanced=" +
            ragEnhanced +
            ", buildDuration=" +
            buildDuration +
            '}'
        );
    }
}
