package com.whiskerguard.ai.service.prompt;

import java.util.ArrayList;
import java.util.List;

/**
 * 提示词验证结果
 * <p>
 * 包含变量验证的详细结果信息，用于检查模板变量是否正确提供。
 */
public class PromptValidationResult {

    /**
     * 验证是否通过
     */
    private boolean valid;

    /**
     * 错误信息列表
     */
    private List<String> errors;

    /**
     * 警告信息列表
     */
    private List<String> warnings;

    /**
     * 缺失的必填变量
     */
    private List<String> missingRequiredVariables;

    /**
     * 验证失败的变量
     */
    private List<VariableValidationError> variableErrors;

    public PromptValidationResult() {
        this.valid = true;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.missingRequiredVariables = new ArrayList<>();
        this.variableErrors = new ArrayList<>();
    }

    /**
     * 添加错误信息
     */
    public void addError(String error) {
        this.valid = false;
        this.errors.add(error);
    }

    /**
     * 添加警告信息
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }

    /**
     * 添加缺失的必填变量
     */
    public void addMissingRequiredVariable(String variableName) {
        this.valid = false;
        this.missingRequiredVariables.add(variableName);
    }

    /**
     * 添加变量验证错误
     */
    public void addVariableError(String variableName, String error) {
        this.valid = false;
        this.variableErrors.add(new VariableValidationError(variableName, error));
    }

    /**
     * 创建成功的验证结果
     */
    public static PromptValidationResult success() {
        return new PromptValidationResult();
    }

    /**
     * 创建失败的验证结果
     */
    public static PromptValidationResult failure(String error) {
        PromptValidationResult result = new PromptValidationResult();
        result.addError(error);
        return result;
    }

    // Getters and Setters
    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public List<String> getWarnings() {
        return warnings;
    }

    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
    }

    public List<String> getMissingRequiredVariables() {
        return missingRequiredVariables;
    }

    public void setMissingRequiredVariables(List<String> missingRequiredVariables) {
        this.missingRequiredVariables = missingRequiredVariables;
    }

    public List<VariableValidationError> getVariableErrors() {
        return variableErrors;
    }

    public void setVariableErrors(List<VariableValidationError> variableErrors) {
        this.variableErrors = variableErrors;
    }

    /**
     * 变量验证错误内部类
     */
    public static class VariableValidationError {

        private String variableName;
        private String errorMessage;

        public VariableValidationError(String variableName, String errorMessage) {
            this.variableName = variableName;
            this.errorMessage = errorMessage;
        }

        public String getVariableName() {
            return variableName;
        }

        public void setVariableName(String variableName) {
            this.variableName = variableName;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "VariableValidationError{" + "variableName='" + variableName + '\'' + ", errorMessage='" + errorMessage + '\'' + '}';
        }
    }

    @Override
    public String toString() {
        return (
            "PromptValidationResult{" +
            "valid=" +
            valid +
            ", errors=" +
            errors +
            ", warnings=" +
            warnings +
            ", missingRequiredVariables=" +
            missingRequiredVariables +
            ", variableErrors=" +
            variableErrors +
            '}'
        );
    }
}
