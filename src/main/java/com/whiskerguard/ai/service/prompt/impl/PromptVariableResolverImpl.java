package com.whiskerguard.ai.service.prompt.impl;

import com.whiskerguard.ai.domain.enumeration.VariableType;
import com.whiskerguard.ai.service.TenantPromptConfigService;
import com.whiskerguard.ai.service.prompt.PromptVariableResolver;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 提示词变量解析服务实现
 * <p>
 * 负责解析和处理提示词模板中的各种类型变量，支持多层级变量替换。
 */
@Service
public class PromptVariableResolverImpl implements PromptVariableResolver {

    private final Logger log = LoggerFactory.getLogger(PromptVariableResolverImpl.class);

    /**
     * 变量占位符正则表达式：{{VARIABLE_NAME}}
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{\\{([A-Z_][A-Z0-9_]*)\\}\\}");

    /**
     * 系统变量前缀
     */
    private static final String SYSTEM_PREFIX = "SYSTEM.";

    /**
     * RAG变量前缀
     */
    private static final String RAG_PREFIX = "RAG.";

    private final TenantPromptConfigService tenantPromptConfigService;

    public PromptVariableResolverImpl(TenantPromptConfigService tenantPromptConfigService) {
        this.tenantPromptConfigService = tenantPromptConfigService;
    }

    @Override
    public Map<String, Object> resolveVariables(String templateContent, Long tenantId, Map<String, Object> providedVariables) {
        log.debug("解析模板变量 - 租户ID: {}", tenantId);

        Map<String, Object> resolvedVariables = new HashMap<>();

        try {
            // 1. 解析系统变量
            resolvedVariables.putAll(resolveSystemVariables(tenantId));

            // 2. 解析业务变量
            resolvedVariables.putAll(resolveBusinessVariables(tenantId, providedVariables));

            // 3. 解析自定义变量
            resolvedVariables.putAll(resolveCustomVariables(tenantId, providedVariables));

            // 4. 解析计算变量
            resolvedVariables.putAll(resolveComputedVariables(tenantId, resolvedVariables));

            // 5. 用户提供的变量覆盖默认值
            if (providedVariables != null) {
                resolvedVariables.putAll(providedVariables);
            }

            log.debug("变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, resolvedVariables.size());
            return resolvedVariables;
        } catch (Exception e) {
            log.error("解析模板变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
            return providedVariables != null ? new HashMap<>(providedVariables) : new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> resolveSystemVariables(Long tenantId) {
        Map<String, Object> systemVars = new HashMap<>();

        try {
            // 当前时间相关变量
            Instant now = Instant.now();
            systemVars.put("SYSTEM.CURRENT_DATE", DateTimeFormatter.ISO_LOCAL_DATE.format(now.atZone(java.time.ZoneId.systemDefault())));
            systemVars.put("SYSTEM.CURRENT_TIME", DateTimeFormatter.ISO_LOCAL_TIME.format(now.atZone(java.time.ZoneId.systemDefault())));
            systemVars.put(
                "SYSTEM.CURRENT_DATETIME",
                DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(now.atZone(java.time.ZoneId.systemDefault()))
            );
            systemVars.put("SYSTEM.TIMESTAMP", now.toEpochMilli());

            // 租户相关变量
            if (tenantId != null) {
                systemVars.put("SYSTEM.TENANT_ID", tenantId);
            }

            // 系统信息变量
            systemVars.put("SYSTEM.SERVICE_NAME", "WhiskerGuard AI Service");
            systemVars.put("SYSTEM.VERSION", "1.0.0");

            log.debug("系统变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, systemVars.size());
        } catch (Exception e) {
            log.error("解析系统变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return systemVars;
    }

    @Override
    public Map<String, Object> resolveBusinessVariables(Long tenantId, Map<String, Object> providedVariables) {
        Map<String, Object> businessVars = new HashMap<>();

        try {
            // 从用户提供的变量中提取业务变量
            if (providedVariables != null) {
                for (Map.Entry<String, Object> entry : providedVariables.entrySet()) {
                    String key = entry.getKey();
                    // 业务变量通常是大写字母和下划线组成
                    if (key.matches("^[A-Z][A-Z0-9_]*$") && !key.startsWith(SYSTEM_PREFIX) && !key.startsWith(RAG_PREFIX)) {
                        businessVars.put(key, entry.getValue());
                    }
                }
            }

            log.debug("业务变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, businessVars.size());
        } catch (Exception e) {
            log.error("解析业务变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return businessVars;
    }

    @Override
    public Map<String, Object> resolveRagVariables(Long tenantId, String context) {
        Map<String, Object> ragVars = new HashMap<>();

        try {
            // RAG变量通常由RAG服务提供，这里提供基础实现
            if (context != null && !context.trim().isEmpty()) {
                ragVars.put("RAG.ENHANCED_CONTEXT", context);
                ragVars.put("RAG.CONTEXT_LENGTH", context.length());
                ragVars.put("RAG.HAS_CONTEXT", true);
            } else {
                ragVars.put("RAG.HAS_CONTEXT", false);
            }

            log.debug("RAG变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, ragVars.size());
        } catch (Exception e) {
            log.error("解析RAG变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return ragVars;
    }

    @Override
    public Map<String, Object> resolveCustomVariables(Long tenantId, Map<String, Object> providedVariables) {
        Map<String, Object> customVars = new HashMap<>();

        try {
            if (tenantId != null) {
                // 从租户配置中获取自定义变量
                // 这里可以调用 tenantPromptConfigService 获取租户的自定义配置
                // 暂时提供基础实现
                log.debug("解析租户自定义变量 - 租户ID: {}", tenantId);
            }

            log.debug("自定义变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, customVars.size());
        } catch (Exception e) {
            log.error("解析自定义变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return customVars;
    }

    @Override
    public Map<String, Object> resolveComputedVariables(Long tenantId, Map<String, Object> baseVariables) {
        Map<String, Object> computedVars = new HashMap<>();

        try {
            // 计算变量示例
            if (baseVariables.containsKey("CONTRACT_CONTENT")) {
                Object content = baseVariables.get("CONTRACT_CONTENT");
                if (content != null) {
                    String contentStr = content.toString();
                    computedVars.put("COMPUTED.CONTENT_LENGTH", contentStr.length());
                    computedVars.put("COMPUTED.WORD_COUNT", contentStr.split("\\s+").length);
                    computedVars.put("COMPUTED.HAS_CONTENT", !contentStr.trim().isEmpty());
                }
            }

            if (baseVariables.containsKey("COMPANY_NAME")) {
                Object companyName = baseVariables.get("COMPANY_NAME");
                if (companyName != null) {
                    computedVars.put("COMPUTED.HAS_COMPANY_INFO", true);
                    computedVars.put("COMPUTED.COMPANY_NAME_LENGTH", companyName.toString().length());
                }
            }

            log.debug("计算变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, computedVars.size());
        } catch (Exception e) {
            log.error("解析计算变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return computedVars;
    }

    @Override
    public Map<String, Object> resolveExternalVariables(Long tenantId, Map<String, Object> externalContext) {
        Map<String, Object> externalVars = new HashMap<>();

        try {
            // 外部变量通常来自第三方服务或API
            if (externalContext != null) {
                for (Map.Entry<String, Object> entry : externalContext.entrySet()) {
                    String key = entry.getKey();
                    if (key.startsWith("EXTERNAL.")) {
                        externalVars.put(key, entry.getValue());
                    }
                }
            }

            log.debug("外部变量解析完成 - 租户ID: {}, 变量数量: {}", tenantId, externalVars.size());
        } catch (Exception e) {
            log.error("解析外部变量失败 - 租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
        }

        return externalVars;
    }

    @Override
    public String replaceVariables(String templateContent, Map<String, Object> variables) {
        if (templateContent == null || templateContent.isEmpty()) {
            return templateContent;
        }

        if (variables == null || variables.isEmpty()) {
            return templateContent;
        }

        try {
            String result = templateContent;
            Matcher matcher = VARIABLE_PATTERN.matcher(templateContent);

            while (matcher.find()) {
                String variableName = matcher.group(1);
                String placeholder = matcher.group(0); // 完整的 {{VARIABLE_NAME}}

                Object value = variables.get(variableName);
                String replacement = value != null ? value.toString() : "";

                result = result.replace(placeholder, replacement);
            }

            log.debug("变量替换完成 - 原长度: {}, 替换后长度: {}", templateContent.length(), result.length());
            return result;
        } catch (Exception e) {
            log.error("变量替换失败 - 错误: {}", e.getMessage(), e);
            return templateContent;
        }
    }

    @Override
    public Set<String> extractVariableNames(String templateContent) {
        Set<String> variableNames = new HashSet<>();

        if (templateContent == null || templateContent.isEmpty()) {
            return variableNames;
        }

        try {
            Matcher matcher = VARIABLE_PATTERN.matcher(templateContent);
            while (matcher.find()) {
                variableNames.add(matcher.group(1));
            }

            log.debug("提取变量名完成 - 模板长度: {}, 变量数量: {}", templateContent.length(), variableNames.size());
        } catch (Exception e) {
            log.error("提取变量名失败 - 错误: {}", e.getMessage(), e);
        }

        return variableNames;
    }

    @Override
    public boolean validateVariableValue(String variableName, VariableType variableType, Object value, String validationRule) {
        try {
            // 基础验证：非空检查
            if (value == null) {
                return false;
            }

            // 根据变量类型进行验证
            switch (variableType) {
                case SYSTEM:
                    return validateSystemVariable(variableName, value);
                case BUSINESS:
                    return validateBusinessVariable(variableName, value, validationRule);
                case RAG:
                    return validateRagVariable(variableName, value);
                case CUSTOM:
                    return validateCustomVariable(variableName, value, validationRule);
                case COMPUTED:
                    return true; // 计算变量通常不需要验证
                case EXTERNAL:
                    return validateExternalVariable(variableName, value);
                default:
                    return true;
            }
        } catch (Exception e) {
            log.error("验证变量值失败 - 变量: {}, 错误: {}", variableName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Object getDefaultValue(String variableName, VariableType variableType, Long tenantId) {
        try {
            switch (variableType) {
                case SYSTEM:
                    return getSystemDefaultValue(variableName);
                case BUSINESS:
                    return getBusinessDefaultValue(variableName, tenantId);
                case RAG:
                    return getRagDefaultValue(variableName);
                case CUSTOM:
                    return getCustomDefaultValue(variableName, tenantId);
                case COMPUTED:
                    return null; // 计算变量没有默认值
                case EXTERNAL:
                    return getExternalDefaultValue(variableName);
                default:
                    return null;
            }
        } catch (Exception e) {
            log.error("获取默认值失败 - 变量: {}, 错误: {}", variableName, e.getMessage(), e);
            return null;
        }
    }

    // 私有辅助方法

    private boolean validateSystemVariable(String variableName, Object value) {
        // 系统变量验证逻辑
        return value != null;
    }

    private boolean validateBusinessVariable(String variableName, Object value, String validationRule) {
        // 业务变量验证逻辑
        if (validationRule != null && !validationRule.trim().isEmpty()) {
            try {
                Pattern pattern = Pattern.compile(validationRule);
                return pattern.matcher(value.toString()).matches();
            } catch (Exception e) {
                log.warn("验证规则执行失败 - 变量: {}, 规则: {}, 错误: {}", variableName, validationRule, e.getMessage());
                return true; // 验证规则错误时，默认通过
            }
        }
        return true;
    }

    private boolean validateRagVariable(String variableName, Object value) {
        // RAG变量验证逻辑
        return value != null;
    }

    private boolean validateCustomVariable(String variableName, Object value, String validationRule) {
        // 自定义变量验证逻辑
        return validateBusinessVariable(variableName, value, validationRule);
    }

    private boolean validateExternalVariable(String variableName, Object value) {
        // 外部变量验证逻辑
        return value != null;
    }

    private Object getSystemDefaultValue(String variableName) {
        // 系统变量默认值
        if (variableName.startsWith("SYSTEM.CURRENT_")) {
            return Instant.now().toString();
        }
        return null;
    }

    private Object getBusinessDefaultValue(String variableName, Long tenantId) {
        // 业务变量默认值
        return null;
    }

    private Object getRagDefaultValue(String variableName) {
        // RAG变量默认值
        return null;
    }

    private Object getCustomDefaultValue(String variableName, Long tenantId) {
        // 自定义变量默认值
        return null;
    }

    private Object getExternalDefaultValue(String variableName) {
        // 外部变量默认值
        return null;
    }
}
