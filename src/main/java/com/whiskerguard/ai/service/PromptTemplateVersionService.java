package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 提示词模板版本管理服务接口
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplateVersion}.
 */
public interface PromptTemplateVersionService {
    /**
     * 保存提示词模板版本
     * Save a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO 要保存的实体 the entity to save.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateVersionDTO save(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * 更新提示词模板版本
     * Updates a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO 要更新的实体 the entity to update.
     * @return 持久化后的实体 the persisted entity.
     */
    PromptTemplateVersionDTO update(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * 部分更新提示词模板版本
     * Partially updates a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO 要部分更新的实体 the entity to update partially.
     * @return 持久化后的实体 the persisted entity.
     */
    Optional<PromptTemplateVersionDTO> partialUpdate(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * 获取所有提示词模板版本
     * Get all the promptTemplateVersions.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 实体列表 the list of entities.
     */
    Page<PromptTemplateVersionDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取提示词模板版本
     * Get the "id" promptTemplateVersion.
     *
     * @param id 实体ID the id of the entity.
     * @return 实体 the entity.
     */
    Optional<PromptTemplateVersionDTO> findOne(Long id);

    /**
     * 根据ID删除提示词模板版本
     * Delete the "id" promptTemplateVersion.
     *
     * @param id 实体ID the id of the entity.
     */
    void delete(Long id);
}
