package com.whiskerguard.ai.service;

import com.whiskerguard.ai.client.dto.SensitiveWordFilterResponseDTO;
import java.util.Collections;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * 敏感词过滤服务 - 响应式版本
 * <p>
 * 该服务提供响应式的敏感词过滤功能，适用于响应式编程上下文。
 * 通过在弹性线程池中执行阻塞操作，避免在reactor线程中产生阻塞。
 */
@Service
public class ReactiveSensitiveWordFilterService {

    private static final Logger log = LoggerFactory.getLogger(ReactiveSensitiveWordFilterService.class);

    private final SensitiveWordFilterService sensitiveWordFilterService;

    public ReactiveSensitiveWordFilterService(SensitiveWordFilterService sensitiveWordFilterService) {
        this.sensitiveWordFilterService = sensitiveWordFilterService;
    }

    /**
     * 响应式过滤内容中的敏感词
     * <p>
     * 将传入文本中的敏感词替换为 *，返回过滤后的内容
     *
     * @param content 需要过滤的内容
     * @return 响应式的过滤结果，如遇错误则返回原内容
     */
    public Mono<String> filterContent(String content) {
        // 空内容直接返回
        if (content == null || content.isEmpty()) {
            return Mono.just(content);
        }

        return Mono.fromCallable(() -> {
            try {
                log.debug("开始对内容进行敏感词过滤, 内容长度: {}", content.length());
                String filteredContent = sensitiveWordFilterService.filterContent(content);
                log.debug("敏感词过滤完成, 过滤后内容长度: {}", filteredContent.length());
                return filteredContent;
            } catch (Exception e) {
                // 发生异常时记录错误但返回原内容，确保服务不中断
                log.error("敏感词过滤失败: {}", e.getMessage(), e);
                return content;
            }
        }).subscribeOn(Schedulers.boundedElastic()); // 在弹性线程池中执行阻塞操作
    }

    /**
     * 响应式过滤内容并获取详细的敏感词信息
     * <p>
     * 适用于需要了解具体敏感词信息的场景
     *
     * @param content 需要过滤的内容
     * @return 响应式的敏感词过滤响应DTO，包含原文、过滤后内容和敏感词信息
     */
    public Mono<SensitiveWordFilterResponseDTO> filterContentDetailed(String content) {
        // 空内容直接返回空结果
        if (content == null || content.isEmpty()) {
            SensitiveWordFilterResponseDTO emptyResponse = new SensitiveWordFilterResponseDTO();
            emptyResponse.setOriginalContent("");
            emptyResponse.setFilteredContent("");
            emptyResponse.setContainsSensitiveWords(false);
            return Mono.just(emptyResponse);
        }

        return Mono.fromCallable(() -> {
            try {
                // 调用敏感词过滤服务获取详细信息
                return sensitiveWordFilterService.filterContentDetailed(content);
            } catch (Exception e) {
                // 发生异常时构建一个基本响应
                log.error("获取敏感词详情失败: {}", e.getMessage(), e);
                SensitiveWordFilterResponseDTO fallbackResponse = new SensitiveWordFilterResponseDTO();
                fallbackResponse.setOriginalContent(content);
                fallbackResponse.setFilteredContent(content);
                fallbackResponse.setContainsSensitiveWords(false);
                return fallbackResponse;
            }
        }).subscribeOn(Schedulers.boundedElastic()); // 在弹性线程池中执行阻塞操作
    }

    /**
     * 响应式批量过滤多条内容
     *
     * @param contents 需要过滤的内容列表
     * @return 响应式的过滤结果列表
     */
    public Mono<List<SensitiveWordFilterResponseDTO>> batchFilterContent(List<String> contents) {
        if (contents == null || contents.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        return Mono.fromCallable(() -> {
            try {
                // 调用批量过滤API
                return sensitiveWordFilterService.batchFilterContent(contents);
            } catch (Exception e) {
                log.error("批量敏感词过滤失败: {}", e.getMessage(), e);
                return Collections.<SensitiveWordFilterResponseDTO>emptyList();
            }
        }).subscribeOn(Schedulers.boundedElastic()); // 在弹性线程池中执行阻塞操作
    }

    /**
     * 响应式检查内容是否包含敏感词
     *
     * @param content 需要检查的内容
     * @return 响应式的布尔值，true表示包含敏感词，false表示不包含
     */
    public Mono<Boolean> containsSensitiveWords(String content) {
        if (content == null || content.isEmpty()) {
            return Mono.just(false);
        }

        return Mono.fromCallable(() -> {
            try {
                return sensitiveWordFilterService.containsSensitiveWords(content);
            } catch (Exception e) {
                log.error("敏感词检测失败: {}", e.getMessage(), e);
                return false;
            }
        }).subscribeOn(Schedulers.boundedElastic()); // 在弹性线程池中执行阻塞操作
    }
}
