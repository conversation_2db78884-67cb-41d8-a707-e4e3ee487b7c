package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link PromptTemplateVariable} and its DTO {@link PromptTemplateVariableDTO}.
 */
@Mapper(componentModel = "spring")
public interface PromptTemplateVariableMapper extends EntityMapper<PromptTemplateVariableDTO, PromptTemplateVariable> {
    @Mapping(target = "promptTemplate", source = "promptTemplate", qualifiedByName = "promptTemplateId")
    PromptTemplateVariableDTO toDto(PromptTemplateVariable s);

    @Named("promptTemplateId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PromptTemplateDTO toDtoPromptTemplateId(PromptTemplate promptTemplate);
}
