package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVersion;
import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link PromptTemplateVersion} and its DTO {@link PromptTemplateVersionDTO}.
 */
@Mapper(componentModel = "spring")
public interface PromptTemplateVersionMapper extends EntityMapper<PromptTemplateVersionDTO, PromptTemplateVersion> {
    @Mapping(target = "promptTemplate", source = "promptTemplate", qualifiedByName = "promptTemplateId")
    PromptTemplateVersionDTO toDto(PromptTemplateVersion s);

    @Named("promptTemplateId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PromptTemplateDTO toDtoPromptTemplateId(PromptTemplate promptTemplate);
}
