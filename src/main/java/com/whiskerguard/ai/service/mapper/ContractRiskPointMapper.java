package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.domain.ContractRiskPoint;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import com.whiskerguard.ai.service.dto.ContractRiskPointDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ContractRiskPoint} and its DTO {@link ContractRiskPointDTO}.
 */
@Mapper(componentModel = "spring")
public interface ContractRiskPointMapper extends EntityMapper<ContractRiskPointDTO, ContractRiskPoint> {
    @Mapping(target = "review", source = "review", qualifiedByName = "contractReviewId")
    ContractRiskPointDTO toDto(ContractRiskPoint s);

    @Named("contractReviewId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ContractReviewDTO toDtoContractReviewId(ContractReview contractReview);
}
