package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ContractReview} and its DTO {@link ContractReviewDTO}.
 */
@Mapper(componentModel = "spring")
public interface ContractReviewMapper extends EntityMapper<ContractReviewDTO, ContractReview> {}
