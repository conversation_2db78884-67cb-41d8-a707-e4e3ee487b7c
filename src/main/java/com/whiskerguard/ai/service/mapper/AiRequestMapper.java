/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiRequestMapper.java
 * 包    名：com.whiskerguard.ai.service.mapper
 * 描    述：AI请求实体与DTO转换映射器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import org.mapstruct.*;

/**
 * AI请求实体映射器
 * <p>
 * Mapper for the entity {@link AiRequest} and its DTO {@link AiRequestDTO}.
 * <p>
 * 用于AI请求实体与其DTO之间的转换，支持双向映射。
 */
@Mapper(componentModel = "spring")
public interface AiRequestMapper extends EntityMapper<AiRequestDTO, AiRequest> {
    /**
     * 将AI请求实体转换为DTO
     * <p>
     * 包含对关联实体AiTool的处理，只转换关联实体的ID
     *
     * @param s AI请求实体对象
     * @return 转换后的AI请求DTO对象
     */
    @Mapping(target = "tool", source = "tool", qualifiedByName = "aiToolId")
    AiRequestDTO toDto(AiRequest s);

    /**
     * 将AiTool实体转换为只包含ID的DTO
     * <p>
     * 用于在获取AI请求时，不返回完整的工具信息，只返回工具ID
     *
     * @param aiTool AI工具实体对象
     * @return 只包含ID的AI工具DTO
     */
    @Named("aiToolId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    AiToolDTO toDtoAiToolId(AiTool aiTool);
}
