package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.ContractParty;
import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.service.dto.ContractPartyDTO;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ContractParty} and its DTO {@link ContractPartyDTO}.
 */
@Mapper(componentModel = "spring")
public interface ContractPartyMapper extends EntityMapper<ContractPartyDTO, ContractParty> {
    @Mapping(target = "review", source = "review", qualifiedByName = "contractReviewId")
    ContractPartyDTO toDto(ContractParty s);

    @Named("contractReviewId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ContractReviewDTO toDtoContractReviewId(ContractReview contractReview);
}
