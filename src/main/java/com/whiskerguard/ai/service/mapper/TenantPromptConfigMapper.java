package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.TenantPromptConfig;
import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TenantPromptConfig} and its DTO {@link TenantPromptConfigDTO}.
 */
@Mapper(componentModel = "spring")
public interface TenantPromptConfigMapper extends EntityMapper<TenantPromptConfigDTO, TenantPromptConfig> {}
