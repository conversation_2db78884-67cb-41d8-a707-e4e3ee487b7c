package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.service.dto.AgentContextDTO;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link AgentContext} and its DTO {@link AgentContextDTO}.
 */
@Mapper(componentModel = "spring")
public interface AgentContextMapper extends EntityMapper<AgentContextDTO, AgentContext> {
    @Mapping(target = "agentTask", source = "agentTask", qualifiedByName = "agentTaskTitle")
    AgentContextDTO toDto(AgentContext s);

    @Named("agentTaskTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    AgentTaskDTO toDtoAgentTaskTitle(AgentTask agentTask);
}
