/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiToolMapper.java
 * 包    名：com.whiskerguard.ai.service.mapper
 * 描    述：AI工具实体与DTO转换映射器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import org.mapstruct.*;

/**
 * AI工具实体映射器
 * <p>
 * Mapper for the entity {@link AiTool} and its DTO {@link AiToolDTO}.
 * <p>
 * 用于AI工具实体与其DTO之间的转换，支持双向映射。
 * 此映射器直接继承EntityMapper接口的所有默认映射方法，无需额外配置。
 */
@Mapper(componentModel = "spring")
public interface AiToolMapper extends EntityMapper<AiToolDTO, AiTool> {}
