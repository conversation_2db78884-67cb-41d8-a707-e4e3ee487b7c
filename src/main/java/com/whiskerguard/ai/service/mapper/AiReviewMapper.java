/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiReviewMapper.java
 * 包    名：com.whiskerguard.ai.service.mapper
 * 描    述：AI评审实体与DTO转换映射器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiReview;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.AiReviewDTO;
import org.mapstruct.*;

/**
 * AI评审实体映射器
 * <p>
 * Mapper for the entity {@link AiReview} and its DTO {@link AiReviewDTO}.
 * <p>
 * 用于AI评审实体与其DTO之间的转换，支持双向映射。
 */
@Mapper(componentModel = "spring")
public interface AiReviewMapper extends EntityMapper<AiReviewDTO, AiReview> {
    /**
     * 将AI评审实体转换为DTO
     * <p>
     * 包含对关联实体AiRequest的处理，只转换关联实体的ID
     *
     * @param s AI评审实体对象
     * @return 转换后的AI评审DTO对象
     */
    @Mapping(target = "request", source = "request", qualifiedByName = "aiRequestId")
    AiReviewDTO toDto(AiReview s);

    /**
     * 将AiRequest实体转换为只包含ID的DTO
     * <p>
     * 用于在获取AI评审时，不返回完整的请求信息，只返回请求ID
     *
     * @param aiRequest AI请求实体对象
     * @return 只包含ID的AI请求DTO
     */
    @Named("aiRequestId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    AiRequestDTO toDtoAiRequestId(AiRequest aiRequest);
}
