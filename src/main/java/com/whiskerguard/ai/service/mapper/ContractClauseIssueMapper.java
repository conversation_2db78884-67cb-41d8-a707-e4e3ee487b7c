package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.ContractClauseIssue;
import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.service.dto.ContractClauseIssueDTO;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ContractClauseIssue} and its DTO {@link ContractClauseIssueDTO}.
 */
@Mapper(componentModel = "spring")
public interface ContractClauseIssueMapper extends EntityMapper<ContractClauseIssueDTO, ContractClauseIssue> {
    @Mapping(target = "review", source = "review", qualifiedByName = "contractReviewId")
    ContractClauseIssueDTO toDto(ContractClauseIssue s);

    @Named("contractReviewId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ContractReviewDTO toDtoContractReviewId(ContractReview contractReview);
}
