package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import com.whiskerguard.ai.service.dto.TaskStepDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TaskStep} and its DTO {@link TaskStepDTO}.
 */
@Mapper(componentModel = "spring")
public interface TaskStepMapper extends EntityMapper<TaskStepDTO, TaskStep> {
    @Mapping(target = "agentTask", source = "agentTask", qualifiedByName = "agentTaskTitle")
    TaskStepDTO toDto(TaskStep s);

    @Named("agentTaskTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    AgentTaskDTO toDtoAgentTaskTitle(AgentTask agentTask);
}
