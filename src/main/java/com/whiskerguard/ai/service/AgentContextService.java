package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.AgentContextDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AgentContext}.
 */
public interface AgentContextService {
    /**
     * Save a agentContext.
     *
     * @param agentContextDTO the entity to save.
     * @return the persisted entity.
     */
    AgentContextDTO save(AgentContextDTO agentContextDTO);

    /**
     * Updates a agentContext.
     *
     * @param agentContextDTO the entity to update.
     * @return the persisted entity.
     */
    AgentContextDTO update(AgentContextDTO agentContextDTO);

    /**
     * Partially updates a agentContext.
     *
     * @param agentContextDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<AgentContextDTO> partialUpdate(AgentContextDTO agentContextDTO);

    /**
     * Get all the agentContexts.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<AgentContextDTO> findAll(Pageable pageable);

    /**
     * Get all the agentContexts with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<AgentContextDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" agentContext.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<AgentContextDTO> findOne(Long id);

    /**
     * Delete the "id" agentContext.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
