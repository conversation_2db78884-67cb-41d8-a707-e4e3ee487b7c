package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.KnowledgeCacheDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.KnowledgeCache}.
 */
public interface KnowledgeCacheService {
    /**
     * Save a knowledgeCache.
     *
     * @param knowledgeCacheDTO the entity to save.
     * @return the persisted entity.
     */
    KnowledgeCacheDTO save(KnowledgeCacheDTO knowledgeCacheDTO);

    /**
     * Updates a knowledgeCache.
     *
     * @param knowledgeCacheDTO the entity to update.
     * @return the persisted entity.
     */
    KnowledgeCacheDTO update(KnowledgeCacheDTO knowledgeCacheDTO);

    /**
     * Partially updates a knowledgeCache.
     *
     * @param knowledgeCacheDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<KnowledgeCacheDTO> partialUpdate(KnowledgeCacheDTO knowledgeCacheDTO);

    /**
     * Get all the knowledgeCaches.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<KnowledgeCacheDTO> findAll(Pageable pageable);

    /**
     * Get the "id" knowledgeCache.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<KnowledgeCacheDTO> findOne(Long id);

    /**
     * Delete the "id" knowledgeCache.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
