package com.whiskerguard.ai.service.exception;

/**
 * AI调用异常
 * 当AI工具调用失败时抛出此异常
 */
public class AiInvocationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 工具键
     */
    private final String toolKey;

    /**
     * 错误代码
     */
    private final String errorCode;

    public AiInvocationException(String message) {
        super(message);
        this.toolKey = null;
        this.errorCode = null;
    }

    public AiInvocationException(String message, Throwable cause) {
        super(message, cause);
        this.toolKey = null;
        this.errorCode = null;
    }

    public AiInvocationException(String message, String toolKey, String errorCode) {
        super(message);
        this.toolKey = toolKey;
        this.errorCode = errorCode;
    }

    public AiInvocationException(String message, String toolKey, String errorCode, Throwable cause) {
        super(message, cause);
        this.toolKey = toolKey;
        this.errorCode = errorCode;
    }

    public String getToolKey() {
        return toolKey;
    }

    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String toString() {
        return (
            "AiInvocationException{" +
            "message='" +
            getMessage() +
            '\'' +
            ", toolKey='" +
            toolKey +
            '\'' +
            ", errorCode='" +
            errorCode +
            '\'' +
            '}'
        );
    }
}
