package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.ContractClauseIssue;
import com.whiskerguard.ai.repository.ContractClauseIssueRepository;
import com.whiskerguard.ai.service.ContractClauseIssueService;
import com.whiskerguard.ai.service.dto.ContractClauseIssueDTO;
import com.whiskerguard.ai.service.mapper.ContractClauseIssueMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.ContractClauseIssue}.
 */
@Service
@Transactional
public class ContractClauseIssueServiceImpl implements ContractClauseIssueService {

    private static final Logger LOG = LoggerFactory.getLogger(ContractClauseIssueServiceImpl.class);

    private final ContractClauseIssueRepository contractClauseIssueRepository;

    private final ContractClauseIssueMapper contractClauseIssueMapper;

    public ContractClauseIssueServiceImpl(
        ContractClauseIssueRepository contractClauseIssueRepository,
        ContractClauseIssueMapper contractClauseIssueMapper
    ) {
        this.contractClauseIssueRepository = contractClauseIssueRepository;
        this.contractClauseIssueMapper = contractClauseIssueMapper;
    }

    @Override
    public ContractClauseIssueDTO save(ContractClauseIssueDTO contractClauseIssueDTO) {
        LOG.debug("Request to save ContractClauseIssue : {}", contractClauseIssueDTO);
        ContractClauseIssue contractClauseIssue = contractClauseIssueMapper.toEntity(contractClauseIssueDTO);
        contractClauseIssue = contractClauseIssueRepository.save(contractClauseIssue);
        return contractClauseIssueMapper.toDto(contractClauseIssue);
    }

    @Override
    public ContractClauseIssueDTO update(ContractClauseIssueDTO contractClauseIssueDTO) {
        LOG.debug("Request to update ContractClauseIssue : {}", contractClauseIssueDTO);
        ContractClauseIssue contractClauseIssue = contractClauseIssueMapper.toEntity(contractClauseIssueDTO);
        contractClauseIssue = contractClauseIssueRepository.save(contractClauseIssue);
        return contractClauseIssueMapper.toDto(contractClauseIssue);
    }

    @Override
    public Optional<ContractClauseIssueDTO> partialUpdate(ContractClauseIssueDTO contractClauseIssueDTO) {
        LOG.debug("Request to partially update ContractClauseIssue : {}", contractClauseIssueDTO);

        return contractClauseIssueRepository
            .findById(contractClauseIssueDTO.getId())
            .map(existingContractClauseIssue -> {
                contractClauseIssueMapper.partialUpdate(existingContractClauseIssue, contractClauseIssueDTO);

                return existingContractClauseIssue;
            })
            .map(contractClauseIssueRepository::save)
            .map(contractClauseIssueMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ContractClauseIssueDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ContractClauseIssues");
        return contractClauseIssueRepository.findAll(pageable).map(contractClauseIssueMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ContractClauseIssueDTO> findOne(Long id) {
        LOG.debug("Request to get ContractClauseIssue : {}", id);
        return contractClauseIssueRepository.findById(id).map(contractClauseIssueMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ContractClauseIssue : {}", id);
        contractClauseIssueRepository.deleteById(id);
    }
}
