package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.ContractParty;
import com.whiskerguard.ai.repository.ContractPartyRepository;
import com.whiskerguard.ai.service.ContractPartyService;
import com.whiskerguard.ai.service.dto.ContractPartyDTO;
import com.whiskerguard.ai.service.mapper.ContractPartyMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.ContractParty}.
 */
@Service
@Transactional
public class ContractPartyServiceImpl implements ContractPartyService {

    private static final Logger LOG = LoggerFactory.getLogger(ContractPartyServiceImpl.class);

    private final ContractPartyRepository contractPartyRepository;

    private final ContractPartyMapper contractPartyMapper;

    public ContractPartyServiceImpl(ContractPartyRepository contractPartyRepository, ContractPartyMapper contractPartyMapper) {
        this.contractPartyRepository = contractPartyRepository;
        this.contractPartyMapper = contractPartyMapper;
    }

    @Override
    public ContractPartyDTO save(ContractPartyDTO contractPartyDTO) {
        LOG.debug("Request to save ContractParty : {}", contractPartyDTO);
        ContractParty contractParty = contractPartyMapper.toEntity(contractPartyDTO);
        contractParty = contractPartyRepository.save(contractParty);
        return contractPartyMapper.toDto(contractParty);
    }

    @Override
    public ContractPartyDTO update(ContractPartyDTO contractPartyDTO) {
        LOG.debug("Request to update ContractParty : {}", contractPartyDTO);
        ContractParty contractParty = contractPartyMapper.toEntity(contractPartyDTO);
        contractParty = contractPartyRepository.save(contractParty);
        return contractPartyMapper.toDto(contractParty);
    }

    @Override
    public Optional<ContractPartyDTO> partialUpdate(ContractPartyDTO contractPartyDTO) {
        LOG.debug("Request to partially update ContractParty : {}", contractPartyDTO);

        return contractPartyRepository
            .findById(contractPartyDTO.getId())
            .map(existingContractParty -> {
                contractPartyMapper.partialUpdate(existingContractParty, contractPartyDTO);

                return existingContractParty;
            })
            .map(contractPartyRepository::save)
            .map(contractPartyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ContractPartyDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ContractParties");
        return contractPartyRepository.findAll(pageable).map(contractPartyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ContractPartyDTO> findOne(Long id) {
        LOG.debug("Request to get ContractParty : {}", id);
        return contractPartyRepository.findById(id).map(contractPartyMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ContractParty : {}", id);
        contractPartyRepository.deleteById(id);
    }
}
