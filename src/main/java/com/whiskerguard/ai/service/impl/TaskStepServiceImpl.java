package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.service.TaskStepService;
import com.whiskerguard.ai.service.dto.TaskStepDTO;
import com.whiskerguard.ai.service.mapper.TaskStepMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.TaskStep}.
 */
@Service
@Transactional
public class TaskStepServiceImpl implements TaskStepService {

    private static final Logger LOG = LoggerFactory.getLogger(TaskStepServiceImpl.class);

    private final TaskStepRepository taskStepRepository;

    private final TaskStepMapper taskStepMapper;

    public TaskStepServiceImpl(TaskStepRepository taskStepRepository, TaskStepMapper taskStepMapper) {
        this.taskStepRepository = taskStepRepository;
        this.taskStepMapper = taskStepMapper;
    }

    @Override
    public TaskStepDTO save(TaskStepDTO taskStepDTO) {
        LOG.debug("Request to save TaskStep : {}", taskStepDTO);
        TaskStep taskStep = taskStepMapper.toEntity(taskStepDTO);
        taskStep = taskStepRepository.save(taskStep);
        return taskStepMapper.toDto(taskStep);
    }

    @Override
    public TaskStepDTO update(TaskStepDTO taskStepDTO) {
        LOG.debug("Request to update TaskStep : {}", taskStepDTO);
        TaskStep taskStep = taskStepMapper.toEntity(taskStepDTO);
        taskStep = taskStepRepository.save(taskStep);
        return taskStepMapper.toDto(taskStep);
    }

    @Override
    public Optional<TaskStepDTO> partialUpdate(TaskStepDTO taskStepDTO) {
        LOG.debug("Request to partially update TaskStep : {}", taskStepDTO);

        return taskStepRepository
            .findById(taskStepDTO.getId())
            .map(existingTaskStep -> {
                taskStepMapper.partialUpdate(existingTaskStep, taskStepDTO);

                return existingTaskStep;
            })
            .map(taskStepRepository::save)
            .map(taskStepMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TaskStepDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all TaskSteps");
        return taskStepRepository.findAll(pageable).map(taskStepMapper::toDto);
    }

    public Page<TaskStepDTO> findAllWithEagerRelationships(Pageable pageable) {
        return taskStepRepository.findAllWithEagerRelationships(pageable).map(taskStepMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaskStepDTO> findOne(Long id) {
        LOG.debug("Request to get TaskStep : {}", id);
        return taskStepRepository.findOneWithEagerRelationships(id).map(taskStepMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TaskStep : {}", id);
        taskStepRepository.deleteById(id);
    }
}
