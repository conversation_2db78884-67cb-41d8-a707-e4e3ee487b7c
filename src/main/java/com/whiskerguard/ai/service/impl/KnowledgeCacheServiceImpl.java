package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.KnowledgeCache;
import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.service.KnowledgeCacheService;
import com.whiskerguard.ai.service.dto.KnowledgeCacheDTO;
import com.whiskerguard.ai.service.mapper.KnowledgeCacheMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.KnowledgeCache}.
 */
@Service
@Transactional
public class KnowledgeCacheServiceImpl implements KnowledgeCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(KnowledgeCacheServiceImpl.class);

    private final KnowledgeCacheRepository knowledgeCacheRepository;

    private final KnowledgeCacheMapper knowledgeCacheMapper;

    public KnowledgeCacheServiceImpl(KnowledgeCacheRepository knowledgeCacheRepository, KnowledgeCacheMapper knowledgeCacheMapper) {
        this.knowledgeCacheRepository = knowledgeCacheRepository;
        this.knowledgeCacheMapper = knowledgeCacheMapper;
    }

    @Override
    public KnowledgeCacheDTO save(KnowledgeCacheDTO knowledgeCacheDTO) {
        LOG.debug("Request to save KnowledgeCache : {}", knowledgeCacheDTO);
        KnowledgeCache knowledgeCache = knowledgeCacheMapper.toEntity(knowledgeCacheDTO);
        knowledgeCache = knowledgeCacheRepository.save(knowledgeCache);
        return knowledgeCacheMapper.toDto(knowledgeCache);
    }

    @Override
    public KnowledgeCacheDTO update(KnowledgeCacheDTO knowledgeCacheDTO) {
        LOG.debug("Request to update KnowledgeCache : {}", knowledgeCacheDTO);
        KnowledgeCache knowledgeCache = knowledgeCacheMapper.toEntity(knowledgeCacheDTO);
        knowledgeCache = knowledgeCacheRepository.save(knowledgeCache);
        return knowledgeCacheMapper.toDto(knowledgeCache);
    }

    @Override
    public Optional<KnowledgeCacheDTO> partialUpdate(KnowledgeCacheDTO knowledgeCacheDTO) {
        LOG.debug("Request to partially update KnowledgeCache : {}", knowledgeCacheDTO);

        return knowledgeCacheRepository
            .findById(knowledgeCacheDTO.getId())
            .map(existingKnowledgeCache -> {
                knowledgeCacheMapper.partialUpdate(existingKnowledgeCache, knowledgeCacheDTO);

                return existingKnowledgeCache;
            })
            .map(knowledgeCacheRepository::save)
            .map(knowledgeCacheMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<KnowledgeCacheDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all KnowledgeCaches");
        return knowledgeCacheRepository.findAll(pageable).map(knowledgeCacheMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<KnowledgeCacheDTO> findOne(Long id) {
        LOG.debug("Request to get KnowledgeCache : {}", id);
        return knowledgeCacheRepository.findById(id).map(knowledgeCacheMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete KnowledgeCache : {}", id);
        knowledgeCacheRepository.deleteById(id);
    }
}
