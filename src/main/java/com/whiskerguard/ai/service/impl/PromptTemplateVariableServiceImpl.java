package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.repository.PromptTemplateVariableRepository;
import com.whiskerguard.ai.service.PromptTemplateVariableService;
import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateVariableMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.PromptTemplateVariable}.
 */
@Service
@Transactional
public class PromptTemplateVariableServiceImpl implements PromptTemplateVariableService {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateVariableServiceImpl.class);

    private final PromptTemplateVariableRepository promptTemplateVariableRepository;

    private final PromptTemplateVariableMapper promptTemplateVariableMapper;

    public PromptTemplateVariableServiceImpl(
        PromptTemplateVariableRepository promptTemplateVariableRepository,
        PromptTemplateVariableMapper promptTemplateVariableMapper
    ) {
        this.promptTemplateVariableRepository = promptTemplateVariableRepository;
        this.promptTemplateVariableMapper = promptTemplateVariableMapper;
    }

    @Override
    public PromptTemplateVariableDTO save(PromptTemplateVariableDTO promptTemplateVariableDTO) {
        LOG.debug("Request to save PromptTemplateVariable : {}", promptTemplateVariableDTO);
        PromptTemplateVariable promptTemplateVariable = promptTemplateVariableMapper.toEntity(promptTemplateVariableDTO);
        promptTemplateVariable = promptTemplateVariableRepository.save(promptTemplateVariable);
        return promptTemplateVariableMapper.toDto(promptTemplateVariable);
    }

    @Override
    public PromptTemplateVariableDTO update(PromptTemplateVariableDTO promptTemplateVariableDTO) {
        LOG.debug("Request to update PromptTemplateVariable : {}", promptTemplateVariableDTO);
        PromptTemplateVariable promptTemplateVariable = promptTemplateVariableMapper.toEntity(promptTemplateVariableDTO);
        promptTemplateVariable = promptTemplateVariableRepository.save(promptTemplateVariable);
        return promptTemplateVariableMapper.toDto(promptTemplateVariable);
    }

    @Override
    public Optional<PromptTemplateVariableDTO> partialUpdate(PromptTemplateVariableDTO promptTemplateVariableDTO) {
        LOG.debug("Request to partially update PromptTemplateVariable : {}", promptTemplateVariableDTO);

        return promptTemplateVariableRepository
            .findById(promptTemplateVariableDTO.getId())
            .map(existingPromptTemplateVariable -> {
                promptTemplateVariableMapper.partialUpdate(existingPromptTemplateVariable, promptTemplateVariableDTO);

                return existingPromptTemplateVariable;
            })
            .map(promptTemplateVariableRepository::save)
            .map(promptTemplateVariableMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PromptTemplateVariableDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PromptTemplateVariables");
        return promptTemplateVariableRepository.findAll(pageable).map(promptTemplateVariableMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateVariableDTO> findOne(Long id) {
        LOG.debug("Request to get PromptTemplateVariable : {}", id);
        return promptTemplateVariableRepository.findById(id).map(promptTemplateVariableMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PromptTemplateVariable : {}", id);
        promptTemplateVariableRepository.deleteById(id);
    }
}
