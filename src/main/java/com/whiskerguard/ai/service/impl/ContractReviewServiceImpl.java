package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.repository.ContractReviewRepository;
import com.whiskerguard.ai.service.ContractReviewService;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import com.whiskerguard.ai.service.mapper.ContractReviewMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.ContractReview}.
 */
@Service
@Transactional
public class ContractReviewServiceImpl implements ContractReviewService {

    private static final Logger LOG = LoggerFactory.getLogger(ContractReviewServiceImpl.class);

    private final ContractReviewRepository contractReviewRepository;

    private final ContractReviewMapper contractReviewMapper;

    public ContractReviewServiceImpl(ContractReviewRepository contractReviewRepository, ContractReviewMapper contractReviewMapper) {
        this.contractReviewRepository = contractReviewRepository;
        this.contractReviewMapper = contractReviewMapper;
    }

    @Override
    public ContractReviewDTO save(ContractReviewDTO contractReviewDTO) {
        LOG.debug("Request to save ContractReview : {}", contractReviewDTO);
        ContractReview contractReview = contractReviewMapper.toEntity(contractReviewDTO);
        contractReview = contractReviewRepository.save(contractReview);
        return contractReviewMapper.toDto(contractReview);
    }

    @Override
    public ContractReviewDTO update(ContractReviewDTO contractReviewDTO) {
        LOG.debug("Request to update ContractReview : {}", contractReviewDTO);
        ContractReview contractReview = contractReviewMapper.toEntity(contractReviewDTO);
        contractReview = contractReviewRepository.save(contractReview);
        return contractReviewMapper.toDto(contractReview);
    }

    @Override
    public Optional<ContractReviewDTO> partialUpdate(ContractReviewDTO contractReviewDTO) {
        LOG.debug("Request to partially update ContractReview : {}", contractReviewDTO);

        return contractReviewRepository
            .findById(contractReviewDTO.getId())
            .map(existingContractReview -> {
                contractReviewMapper.partialUpdate(existingContractReview, contractReviewDTO);

                return existingContractReview;
            })
            .map(contractReviewRepository::save)
            .map(contractReviewMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ContractReviewDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ContractReviews");
        return contractReviewRepository.findAll(pageable).map(contractReviewMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ContractReviewDTO> findOne(Long id) {
        LOG.debug("Request to get ContractReview : {}", id);
        return contractReviewRepository.findById(id).map(contractReviewMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ContractReview : {}", id);
        contractReviewRepository.deleteById(id);
    }
}
