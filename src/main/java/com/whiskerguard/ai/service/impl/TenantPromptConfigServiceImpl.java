package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.TenantPromptConfig;
import com.whiskerguard.ai.repository.TenantPromptConfigRepository;
import com.whiskerguard.ai.service.TenantPromptConfigService;
import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import com.whiskerguard.ai.service.mapper.TenantPromptConfigMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.TenantPromptConfig}.
 */
@Service
@Transactional
public class TenantPromptConfigServiceImpl implements TenantPromptConfigService {

    private static final Logger LOG = LoggerFactory.getLogger(TenantPromptConfigServiceImpl.class);

    private final TenantPromptConfigRepository tenantPromptConfigRepository;

    private final TenantPromptConfigMapper tenantPromptConfigMapper;

    public TenantPromptConfigServiceImpl(
        TenantPromptConfigRepository tenantPromptConfigRepository,
        TenantPromptConfigMapper tenantPromptConfigMapper
    ) {
        this.tenantPromptConfigRepository = tenantPromptConfigRepository;
        this.tenantPromptConfigMapper = tenantPromptConfigMapper;
    }

    @Override
    public TenantPromptConfigDTO save(TenantPromptConfigDTO tenantPromptConfigDTO) {
        LOG.debug("Request to save TenantPromptConfig : {}", tenantPromptConfigDTO);
        TenantPromptConfig tenantPromptConfig = tenantPromptConfigMapper.toEntity(tenantPromptConfigDTO);
        tenantPromptConfig = tenantPromptConfigRepository.save(tenantPromptConfig);
        return tenantPromptConfigMapper.toDto(tenantPromptConfig);
    }

    @Override
    public TenantPromptConfigDTO update(TenantPromptConfigDTO tenantPromptConfigDTO) {
        LOG.debug("Request to update TenantPromptConfig : {}", tenantPromptConfigDTO);
        TenantPromptConfig tenantPromptConfig = tenantPromptConfigMapper.toEntity(tenantPromptConfigDTO);
        tenantPromptConfig = tenantPromptConfigRepository.save(tenantPromptConfig);
        return tenantPromptConfigMapper.toDto(tenantPromptConfig);
    }

    @Override
    public Optional<TenantPromptConfigDTO> partialUpdate(TenantPromptConfigDTO tenantPromptConfigDTO) {
        LOG.debug("Request to partially update TenantPromptConfig : {}", tenantPromptConfigDTO);

        return tenantPromptConfigRepository
            .findById(tenantPromptConfigDTO.getId())
            .map(existingTenantPromptConfig -> {
                tenantPromptConfigMapper.partialUpdate(existingTenantPromptConfig, tenantPromptConfigDTO);

                return existingTenantPromptConfig;
            })
            .map(tenantPromptConfigRepository::save)
            .map(tenantPromptConfigMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantPromptConfigDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all TenantPromptConfigs");
        return tenantPromptConfigRepository.findAll(pageable).map(tenantPromptConfigMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TenantPromptConfigDTO> findOne(Long id) {
        LOG.debug("Request to get TenantPromptConfig : {}", id);
        return tenantPromptConfigRepository.findById(id).map(tenantPromptConfigMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TenantPromptConfig : {}", id);
        tenantPromptConfigRepository.deleteById(id);
    }
}
