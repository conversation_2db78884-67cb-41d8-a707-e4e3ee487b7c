package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.PromptTemplateVersion;
import com.whiskerguard.ai.repository.PromptTemplateVersionRepository;
import com.whiskerguard.ai.service.PromptTemplateVersionService;
import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateVersionMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.PromptTemplateVersion}.
 */
@Service
@Transactional
public class PromptTemplateVersionServiceImpl implements PromptTemplateVersionService {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateVersionServiceImpl.class);

    private final PromptTemplateVersionRepository promptTemplateVersionRepository;

    private final PromptTemplateVersionMapper promptTemplateVersionMapper;

    public PromptTemplateVersionServiceImpl(
        PromptTemplateVersionRepository promptTemplateVersionRepository,
        PromptTemplateVersionMapper promptTemplateVersionMapper
    ) {
        this.promptTemplateVersionRepository = promptTemplateVersionRepository;
        this.promptTemplateVersionMapper = promptTemplateVersionMapper;
    }

    @Override
    public PromptTemplateVersionDTO save(PromptTemplateVersionDTO promptTemplateVersionDTO) {
        LOG.debug("Request to save PromptTemplateVersion : {}", promptTemplateVersionDTO);
        PromptTemplateVersion promptTemplateVersion = promptTemplateVersionMapper.toEntity(promptTemplateVersionDTO);
        promptTemplateVersion = promptTemplateVersionRepository.save(promptTemplateVersion);
        return promptTemplateVersionMapper.toDto(promptTemplateVersion);
    }

    @Override
    public PromptTemplateVersionDTO update(PromptTemplateVersionDTO promptTemplateVersionDTO) {
        LOG.debug("Request to update PromptTemplateVersion : {}", promptTemplateVersionDTO);
        PromptTemplateVersion promptTemplateVersion = promptTemplateVersionMapper.toEntity(promptTemplateVersionDTO);
        promptTemplateVersion = promptTemplateVersionRepository.save(promptTemplateVersion);
        return promptTemplateVersionMapper.toDto(promptTemplateVersion);
    }

    @Override
    public Optional<PromptTemplateVersionDTO> partialUpdate(PromptTemplateVersionDTO promptTemplateVersionDTO) {
        LOG.debug("Request to partially update PromptTemplateVersion : {}", promptTemplateVersionDTO);

        return promptTemplateVersionRepository
            .findById(promptTemplateVersionDTO.getId())
            .map(existingPromptTemplateVersion -> {
                promptTemplateVersionMapper.partialUpdate(existingPromptTemplateVersion, promptTemplateVersionDTO);

                return existingPromptTemplateVersion;
            })
            .map(promptTemplateVersionRepository::save)
            .map(promptTemplateVersionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PromptTemplateVersionDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PromptTemplateVersions");
        return promptTemplateVersionRepository.findAll(pageable).map(promptTemplateVersionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateVersionDTO> findOne(Long id) {
        LOG.debug("Request to get PromptTemplateVersion : {}", id);
        return promptTemplateVersionRepository.findById(id).map(promptTemplateVersionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PromptTemplateVersion : {}", id);
        promptTemplateVersionRepository.deleteById(id);
    }
}
