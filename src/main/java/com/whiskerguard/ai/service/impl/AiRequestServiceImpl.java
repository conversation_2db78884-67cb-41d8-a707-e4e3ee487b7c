/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiRequestServiceImpl.java
 * 包    名：com.whiskerguard.ai.service.impl
 * 描    述：AI请求管理服务实现类，处理AI请求的业务逻辑
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.AiRequestService;
import com.whiskerguard.ai.service.dto.AiRequestChatDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI请求管理服务实现类
 * <p>
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AiRequest}.
 * <p>
 * 实现AI请求的创建、查询、更新和删除等功能，提供分页查询和按条件筛选的能力。
 */
@Service
@Transactional
public class AiRequestServiceImpl implements AiRequestService {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiRequestServiceImpl.class);

    /**
     * AI请求仓库接口，用于数据库操作
     */
    private final AiRequestRepository aiRequestRepository;

    /**
     * AI请求对象映射器，用于DTO与实体的转换
     */
    private final AiRequestMapper aiRequestMapper;

    /**
     * 构造函数，通过依赖注入获取所需的组件
     *
     * @param aiRequestRepository AI请求仓库，用于数据访问
     * @param aiRequestMapper AI请求映射器，用于对象转换
     */
    public AiRequestServiceImpl(AiRequestRepository aiRequestRepository, AiRequestMapper aiRequestMapper) {
        this.aiRequestRepository = aiRequestRepository;
        this.aiRequestMapper = aiRequestMapper;
    }

    /**
     * 保存AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiRequestDTO save(AiRequestDTO aiRequestDTO) {
        LOG.debug("Request to save AiRequest : {}", aiRequestDTO);
        AiRequest aiRequest = aiRequestMapper.toEntity(aiRequestDTO);
        aiRequest = aiRequestRepository.save(aiRequest);
        return aiRequestMapper.toDto(aiRequest);
    }

    /**
     * 更新AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiRequestDTO update(AiRequestDTO aiRequestDTO) {
        LOG.debug("Request to update AiRequest : {}", aiRequestDTO);
        AiRequest aiRequest = aiRequestMapper.toEntity(aiRequestDTO);
        aiRequest = aiRequestRepository.save(aiRequest);
        return aiRequestMapper.toDto(aiRequest);
    }

    /**
     * 部分更新AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    public Optional<AiRequestDTO> partialUpdate(AiRequestDTO aiRequestDTO) {
        LOG.debug("Request to partially update AiRequest : {}", aiRequestDTO);

        return aiRequestRepository
            .findById(aiRequestDTO.getId())
            .map(existingAiRequest -> {
                aiRequestMapper.partialUpdate(existingAiRequest, aiRequestDTO);

                return existingAiRequest;
            })
            .map(aiRequestRepository::save)
            .map(aiRequestMapper::toDto);
    }

    /**
     * 查询所有AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AiRequests");
        return aiRequestRepository.findAll(pageable).map(aiRequestMapper::toDto);
    }

    /**
     * 根据ID查询AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AiRequestDTO> findOne(Long id) {
        LOG.debug("Request to get AiRequest : {}", id);
        return aiRequestRepository.findById(id).map(aiRequestMapper::toDto);
    }

    /**
     * 根据ID删除AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AiRequest : {}", id);
        aiRequestRepository.deleteById(id);
    }

    /**
     * 根据员工ID查询AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestDTO> findByEmployeeId(Long employeeId, Pageable pageable) {
        LOG.debug("Request to get AiRequests by employeeId : {}", employeeId);
        return aiRequestRepository.findByEmployeeId(employeeId, pageable).map(aiRequestMapper::toDto);
    }

    /**
     * 根据员工ID和状态查询AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestDTO> findByEmployeeIdAndStatus(Long employeeId, RequestStatus status, Pageable pageable) {
        LOG.debug("Request to get AiRequests by employeeId : {} and status : {}", employeeId, status);
        return aiRequestRepository.findByEmployeeIdAndStatus(employeeId, status, pageable).map(aiRequestMapper::toDto);
    }

    /**
     * 根据员工ID和工具类型查询AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestDTO> findByEmployeeIdAndToolType(Long employeeId, String toolType, Pageable pageable) {
        LOG.debug("Request to get AiRequests by employeeId : {} and toolType : {}", employeeId, toolType);
        return aiRequestRepository.findByEmployeeIdAndToolType(employeeId, toolType, pageable).map(aiRequestMapper::toDto);
    }

    /**
     * 查询所有未删除的AI请求
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<AiRequestDTO> findAllByEmployeeId(Long employeeId) {
        LOG.debug("Request to get all AiRequests by employeeId : {}", employeeId);
        return aiRequestRepository
            .findByEmployeeIdOrderByCreatedAtDesc(employeeId)
            .stream()
            .map(aiRequestMapper::toDto)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将AI请求DTO分页结果转换为AI请求聊天DTO分页结果
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestChatDTO> convertToAiRequestChatDTOPage(Page<AiRequestDTO> page) {
        LOG.debug("Converting page of AiRequestDTO to page of AiRequestChatDTO");

        List<AiRequestChatDTO> chatDTOs = new ArrayList<>();

        for (AiRequestDTO requestDTO : page.getContent()) {
            // 创建用户提问记录
            AiRequestChatDTO userChatDTO = createUserChatDTO(requestDTO);
            chatDTOs.add(userChatDTO);

            // 创建AI回复记录
            AiRequestChatDTO aiChatDTO = createAiChatDTO(requestDTO);
            chatDTOs.add(aiChatDTO);
        }

        return new PageImpl<>(
            chatDTOs,
            page.getPageable(),
            page.getTotalElements() * 2 // 总数量翻倍，因为每个原始记录分成了两条
        );
    }

    /**
     * 根据员工ID和状态查询AI请求聊天记录
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiRequestChatDTO> findChatByEmployeeIdAndStatus(Long employeeId, RequestStatus status, Pageable pageable) {
        LOG.debug("Request to get AiRequestChatDTOs by employeeId : {} and status : {}", employeeId, status);
        Page<AiRequestDTO> page = this.findByEmployeeIdAndStatus(employeeId, status, pageable);
        return convertToAiRequestChatDTOPage(page);
    }

    /**
     * 创建用户提问记录的ChatDTO
     */
    private AiRequestChatDTO createUserChatDTO(AiRequestDTO requestDTO) {
        AiRequestChatDTO chatDTO = new AiRequestChatDTO();

        // 复制基本属性
        chatDTO.setId(requestDTO.getId());
        chatDTO.setTenantId(requestDTO.getTenantId());
        chatDTO.setEmployeeId(requestDTO.getEmployeeId());
        chatDTO.setToolType(requestDTO.getToolType());
        chatDTO.setContent(requestDTO.getPrompt()); // 用户提问内容
        chatDTO.setRequestTime(requestDTO.getRequestTime());
        chatDTO.setResponseTime(null); // 提问没有响应时间
        chatDTO.setStatus(requestDTO.getStatus());
        chatDTO.setErrorMessage(requestDTO.getErrorMessage());
        chatDTO.setMetadata(requestDTO.getMetadata());
        chatDTO.setVersion(requestDTO.getVersion());
        chatDTO.setCreatedBy(requestDTO.getCreatedBy());
        chatDTO.setCreatedAt(requestDTO.getCreatedAt());
        chatDTO.setUpdatedBy(requestDTO.getUpdatedBy());
        chatDTO.setUpdatedAt(requestDTO.getUpdatedAt());
        chatDTO.setIsDeleted(requestDTO.getIsDeleted());

        // 设置用户标识和原始请求ID
        chatDTO.setIsUser(true);
        chatDTO.setOriginalRequestId(requestDTO.getId());

        return chatDTO;
    }

    /**
     * 创建AI回复记录的ChatDTO
     */
    private AiRequestChatDTO createAiChatDTO(AiRequestDTO requestDTO) {
        AiRequestChatDTO chatDTO = new AiRequestChatDTO();

        // 复制基本属性
        chatDTO.setId(requestDTO.getId());
        chatDTO.setTenantId(requestDTO.getTenantId());
        chatDTO.setEmployeeId(requestDTO.getEmployeeId());
        chatDTO.setToolType(requestDTO.getToolType());
        chatDTO.setContent(requestDTO.getResponse()); // AI回复内容
        chatDTO.setRequestTime(requestDTO.getRequestTime());
        chatDTO.setResponseTime(requestDTO.getResponseTime());
        chatDTO.setStatus(requestDTO.getStatus());
        chatDTO.setErrorMessage(requestDTO.getErrorMessage());
        chatDTO.setMetadata(requestDTO.getMetadata());
        chatDTO.setVersion(requestDTO.getVersion());
        chatDTO.setCreatedBy(requestDTO.getCreatedBy());
        chatDTO.setCreatedAt(requestDTO.getCreatedAt());
        chatDTO.setUpdatedBy(requestDTO.getUpdatedBy());
        chatDTO.setUpdatedAt(requestDTO.getUpdatedAt());
        chatDTO.setIsDeleted(requestDTO.getIsDeleted());
        // 已删除 chatDTO.setTool(requestDTO.getTool());

        // 设置AI标识和原始请求ID
        chatDTO.setIsUser(false);
        chatDTO.setOriginalRequestId(requestDTO.getId());

        return chatDTO;
    }
}
