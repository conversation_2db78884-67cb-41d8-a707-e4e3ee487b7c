package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.ContractRiskPoint;
import com.whiskerguard.ai.repository.ContractRiskPointRepository;
import com.whiskerguard.ai.service.ContractRiskPointService;
import com.whiskerguard.ai.service.dto.ContractRiskPointDTO;
import com.whiskerguard.ai.service.mapper.ContractRiskPointMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.ContractRiskPoint}.
 */
@Service
@Transactional
public class ContractRiskPointServiceImpl implements ContractRiskPointService {

    private static final Logger LOG = LoggerFactory.getLogger(ContractRiskPointServiceImpl.class);

    private final ContractRiskPointRepository contractRiskPointRepository;

    private final ContractRiskPointMapper contractRiskPointMapper;

    public ContractRiskPointServiceImpl(
        ContractRiskPointRepository contractRiskPointRepository,
        ContractRiskPointMapper contractRiskPointMapper
    ) {
        this.contractRiskPointRepository = contractRiskPointRepository;
        this.contractRiskPointMapper = contractRiskPointMapper;
    }

    @Override
    public ContractRiskPointDTO save(ContractRiskPointDTO contractRiskPointDTO) {
        LOG.debug("Request to save ContractRiskPoint : {}", contractRiskPointDTO);
        ContractRiskPoint contractRiskPoint = contractRiskPointMapper.toEntity(contractRiskPointDTO);
        contractRiskPoint = contractRiskPointRepository.save(contractRiskPoint);
        return contractRiskPointMapper.toDto(contractRiskPoint);
    }

    @Override
    public ContractRiskPointDTO update(ContractRiskPointDTO contractRiskPointDTO) {
        LOG.debug("Request to update ContractRiskPoint : {}", contractRiskPointDTO);
        ContractRiskPoint contractRiskPoint = contractRiskPointMapper.toEntity(contractRiskPointDTO);
        contractRiskPoint = contractRiskPointRepository.save(contractRiskPoint);
        return contractRiskPointMapper.toDto(contractRiskPoint);
    }

    @Override
    public Optional<ContractRiskPointDTO> partialUpdate(ContractRiskPointDTO contractRiskPointDTO) {
        LOG.debug("Request to partially update ContractRiskPoint : {}", contractRiskPointDTO);

        return contractRiskPointRepository
            .findById(contractRiskPointDTO.getId())
            .map(existingContractRiskPoint -> {
                contractRiskPointMapper.partialUpdate(existingContractRiskPoint, contractRiskPointDTO);

                return existingContractRiskPoint;
            })
            .map(contractRiskPointRepository::save)
            .map(contractRiskPointMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ContractRiskPointDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ContractRiskPoints");
        return contractRiskPointRepository.findAll(pageable).map(contractRiskPointMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ContractRiskPointDTO> findOne(Long id) {
        LOG.debug("Request to get ContractRiskPoint : {}", id);
        return contractRiskPointRepository.findById(id).map(contractRiskPointMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ContractRiskPoint : {}", id);
        contractRiskPointRepository.deleteById(id);
    }
}
