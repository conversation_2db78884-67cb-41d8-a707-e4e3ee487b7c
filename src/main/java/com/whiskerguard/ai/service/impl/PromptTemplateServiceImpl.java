package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import com.whiskerguard.ai.service.PromptTemplateService;
import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.PromptTemplate}.
 */
@Service
@Transactional
public class PromptTemplateServiceImpl implements PromptTemplateService {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateServiceImpl.class);

    private final PromptTemplateRepository promptTemplateRepository;

    private final PromptTemplateMapper promptTemplateMapper;

    public PromptTemplateServiceImpl(PromptTemplateRepository promptTemplateRepository, PromptTemplateMapper promptTemplateMapper) {
        this.promptTemplateRepository = promptTemplateRepository;
        this.promptTemplateMapper = promptTemplateMapper;
    }

    @Override
    public PromptTemplateDTO save(PromptTemplateDTO promptTemplateDTO) {
        LOG.debug("Request to save PromptTemplate : {}", promptTemplateDTO);
        PromptTemplate promptTemplate = promptTemplateMapper.toEntity(promptTemplateDTO);
        promptTemplate = promptTemplateRepository.save(promptTemplate);
        return promptTemplateMapper.toDto(promptTemplate);
    }

    @Override
    public PromptTemplateDTO update(PromptTemplateDTO promptTemplateDTO) {
        LOG.debug("Request to update PromptTemplate : {}", promptTemplateDTO);
        PromptTemplate promptTemplate = promptTemplateMapper.toEntity(promptTemplateDTO);
        promptTemplate = promptTemplateRepository.save(promptTemplate);
        return promptTemplateMapper.toDto(promptTemplate);
    }

    @Override
    public Optional<PromptTemplateDTO> partialUpdate(PromptTemplateDTO promptTemplateDTO) {
        LOG.debug("Request to partially update PromptTemplate : {}", promptTemplateDTO);

        return promptTemplateRepository
            .findById(promptTemplateDTO.getId())
            .map(existingPromptTemplate -> {
                promptTemplateMapper.partialUpdate(existingPromptTemplate, promptTemplateDTO);

                return existingPromptTemplate;
            })
            .map(promptTemplateRepository::save)
            .map(promptTemplateMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PromptTemplateDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PromptTemplates");
        return promptTemplateRepository.findAll(pageable).map(promptTemplateMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateDTO> findOne(Long id) {
        LOG.debug("Request to get PromptTemplate : {}", id);
        return promptTemplateRepository.findById(id).map(promptTemplateMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PromptTemplate : {}", id);
        promptTemplateRepository.deleteById(id);
    }
}
