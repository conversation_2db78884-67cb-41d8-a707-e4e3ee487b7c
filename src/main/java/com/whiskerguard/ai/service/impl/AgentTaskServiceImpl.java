package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.AgentTaskService;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AgentTask}.
 */
@Service
@Transactional
public class AgentTaskServiceImpl implements AgentTaskService {

    private static final Logger LOG = LoggerFactory.getLogger(AgentTaskServiceImpl.class);

    private final AgentTaskRepository agentTaskRepository;

    private final AgentTaskMapper agentTaskMapper;

    public AgentTaskServiceImpl(AgentTaskRepository agentTaskRepository, AgentTaskMapper agentTaskMapper) {
        this.agentTaskRepository = agentTaskRepository;
        this.agentTaskMapper = agentTaskMapper;
    }

    @Override
    public AgentTaskDTO save(AgentTaskDTO agentTaskDTO) {
        LOG.debug("Request to save AgentTask : {}", agentTaskDTO);
        AgentTask agentTask = agentTaskMapper.toEntity(agentTaskDTO);
        agentTask = agentTaskRepository.save(agentTask);
        return agentTaskMapper.toDto(agentTask);
    }

    @Override
    public AgentTaskDTO update(AgentTaskDTO agentTaskDTO) {
        LOG.debug("Request to update AgentTask : {}", agentTaskDTO);
        AgentTask agentTask = agentTaskMapper.toEntity(agentTaskDTO);
        agentTask = agentTaskRepository.save(agentTask);
        return agentTaskMapper.toDto(agentTask);
    }

    @Override
    public Optional<AgentTaskDTO> partialUpdate(AgentTaskDTO agentTaskDTO) {
        LOG.debug("Request to partially update AgentTask : {}", agentTaskDTO);

        return agentTaskRepository
            .findById(agentTaskDTO.getId())
            .map(existingAgentTask -> {
                agentTaskMapper.partialUpdate(existingAgentTask, agentTaskDTO);

                return existingAgentTask;
            })
            .map(agentTaskRepository::save)
            .map(agentTaskMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AgentTaskDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AgentTasks");
        return agentTaskRepository.findAll(pageable).map(agentTaskMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgentTaskDTO> findOne(Long id) {
        LOG.debug("Request to get AgentTask : {}", id);
        return agentTaskRepository.findById(id).map(agentTaskMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AgentTask : {}", id);
        agentTaskRepository.deleteById(id);
    }
}
