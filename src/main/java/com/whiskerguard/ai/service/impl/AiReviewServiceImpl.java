/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiReviewServiceImpl.java
 * 包    名：com.whiskerguard.ai.service.impl
 * 描    述：AI评审管理服务实现类，处理AI评审的业务逻辑
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AiReview;
import com.whiskerguard.ai.repository.AiReviewRepository;
import com.whiskerguard.ai.service.AiReviewService;
import com.whiskerguard.ai.service.dto.AiReviewDTO;
import com.whiskerguard.ai.service.mapper.AiReviewMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI评审管理服务实现类
 * <p>
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AiReview}.
 * <p>
 * 实现AI评审的创建、查询、更新和删除等功能，提供分页查询和按条件筛选的能力。
 */
@Service
@Transactional
public class AiReviewServiceImpl implements AiReviewService {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AiReviewServiceImpl.class);

    /**
     * AI评审仓库接口，用于数据库操作
     */
    private final AiReviewRepository aiReviewRepository;

    /**
     * AI评审对象映射器，用于DTO与实体的转换
     */
    private final AiReviewMapper aiReviewMapper;

    /**
     * 构造函数，通过依赖注入获取所需的组件
     *
     * @param aiReviewRepository AI评审仓库，用于数据访问
     * @param aiReviewMapper AI评审映射器，用于对象转换
     */
    public AiReviewServiceImpl(AiReviewRepository aiReviewRepository, AiReviewMapper aiReviewMapper) {
        this.aiReviewRepository = aiReviewRepository;
        this.aiReviewMapper = aiReviewMapper;
    }

    /**
     * 保存AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiReviewDTO save(AiReviewDTO aiReviewDTO) {
        LOG.debug("Request to save AiReview : {}", aiReviewDTO);
        AiReview aiReview = aiReviewMapper.toEntity(aiReviewDTO);
        aiReview = aiReviewRepository.save(aiReview);
        return aiReviewMapper.toDto(aiReview);
    }

    /**
     * 更新AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    public AiReviewDTO update(AiReviewDTO aiReviewDTO) {
        LOG.debug("Request to update AiReview : {}", aiReviewDTO);
        AiReview aiReview = aiReviewMapper.toEntity(aiReviewDTO);
        aiReview = aiReviewRepository.save(aiReview);
        return aiReviewMapper.toDto(aiReview);
    }

    /**
     * 部分更新AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    public Optional<AiReviewDTO> partialUpdate(AiReviewDTO aiReviewDTO) {
        LOG.debug("Request to partially update AiReview : {}", aiReviewDTO);

        return aiReviewRepository
            .findById(aiReviewDTO.getId())
            .map(existingAiReview -> {
                aiReviewMapper.partialUpdate(existingAiReview, aiReviewDTO);

                return existingAiReview;
            })
            .map(aiReviewRepository::save)
            .map(aiReviewMapper::toDto);
    }

    /**
     * 查询所有AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiReviewDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AiReviews");
        return aiReviewRepository.findAll(pageable).map(aiReviewMapper::toDto);
    }

    /**
     * 根据ID查询AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AiReviewDTO> findOne(Long id) {
        LOG.debug("Request to get AiReview : {}", id);
        return aiReviewRepository.findById(id).map(aiReviewMapper::toDto);
    }

    /**
     * 根据ID删除AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AiReview : {}", id);
        aiReviewRepository.deleteById(id);
    }

    /**
     * 根据员工ID查询AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiReviewDTO> findByEmployeeId(Long employeeId, Pageable pageable) {
        LOG.debug("Request to get AiReviews by employeeId : {}", employeeId);
        return aiReviewRepository.findByEmployeeId(employeeId, pageable).map(aiReviewMapper::toDto);
    }

    /**
     * 根据员工ID和评审结果查询AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiReviewDTO> findByEmployeeIdAndReviewResult(
        Long employeeId,
        com.whiskerguard.ai.domain.enumeration.ReviewResult reviewResult,
        Pageable pageable
    ) {
        LOG.debug("Request to get AiReviews by employeeId : {} and reviewResult : {}", employeeId, reviewResult);
        return aiReviewRepository.findByEmployeeIdAndReviewResult(employeeId, reviewResult, pageable).map(aiReviewMapper::toDto);
    }

    /**
     * 根据员工ID和评审人查询AI评审
     * <p>
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AiReviewDTO> findByEmployeeIdAndReviewer(Long employeeId, String reviewer, Pageable pageable) {
        LOG.debug("Request to get AiReviews by employeeId : {} and reviewer : {}", employeeId, reviewer);
        return aiReviewRepository.findByEmployeeIdAndReviewer(employeeId, reviewer, pageable).map(aiReviewMapper::toDto);
    }
}
