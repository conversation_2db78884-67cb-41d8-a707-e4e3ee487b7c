package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.repository.AgentContextRepository;
import com.whiskerguard.ai.service.AgentContextService;
import com.whiskerguard.ai.service.dto.AgentContextDTO;
import com.whiskerguard.ai.service.mapper.AgentContextMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AgentContext}.
 */
@Service
@Transactional
public class AgentContextServiceImpl implements AgentContextService {

    private static final Logger LOG = LoggerFactory.getLogger(AgentContextServiceImpl.class);

    private final AgentContextRepository agentContextRepository;

    private final AgentContextMapper agentContextMapper;

    public AgentContextServiceImpl(AgentContextRepository agentContextRepository, AgentContextMapper agentContextMapper) {
        this.agentContextRepository = agentContextRepository;
        this.agentContextMapper = agentContextMapper;
    }

    @Override
    public AgentContextDTO save(AgentContextDTO agentContextDTO) {
        LOG.debug("Request to save AgentContext : {}", agentContextDTO);
        AgentContext agentContext = agentContextMapper.toEntity(agentContextDTO);
        agentContext = agentContextRepository.save(agentContext);
        return agentContextMapper.toDto(agentContext);
    }

    @Override
    public AgentContextDTO update(AgentContextDTO agentContextDTO) {
        LOG.debug("Request to update AgentContext : {}", agentContextDTO);
        AgentContext agentContext = agentContextMapper.toEntity(agentContextDTO);
        agentContext = agentContextRepository.save(agentContext);
        return agentContextMapper.toDto(agentContext);
    }

    @Override
    public Optional<AgentContextDTO> partialUpdate(AgentContextDTO agentContextDTO) {
        LOG.debug("Request to partially update AgentContext : {}", agentContextDTO);

        return agentContextRepository
            .findById(agentContextDTO.getId())
            .map(existingAgentContext -> {
                agentContextMapper.partialUpdate(existingAgentContext, agentContextDTO);

                return existingAgentContext;
            })
            .map(agentContextRepository::save)
            .map(agentContextMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AgentContextDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AgentContexts");
        return agentContextRepository.findAll(pageable).map(agentContextMapper::toDto);
    }

    public Page<AgentContextDTO> findAllWithEagerRelationships(Pageable pageable) {
        return agentContextRepository.findAllWithEagerRelationships(pageable).map(agentContextMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgentContextDTO> findOne(Long id) {
        LOG.debug("Request to get AgentContext : {}", id);
        return agentContextRepository.findOneWithEagerRelationships(id).map(agentContextMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AgentContext : {}", id);
        agentContextRepository.deleteById(id);
    }
}
