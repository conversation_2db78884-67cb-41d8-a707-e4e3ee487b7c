package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.AgentConfig;
import com.whiskerguard.ai.repository.AgentConfigRepository;
import com.whiskerguard.ai.service.AgentConfigService;
import com.whiskerguard.ai.service.dto.AgentConfigDTO;
import com.whiskerguard.ai.service.mapper.AgentConfigMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.AgentConfig}.
 */
@Service
@Transactional
public class AgentConfigServiceImpl implements AgentConfigService {

    private static final Logger LOG = LoggerFactory.getLogger(AgentConfigServiceImpl.class);

    private final AgentConfigRepository agentConfigRepository;

    private final AgentConfigMapper agentConfigMapper;

    public AgentConfigServiceImpl(AgentConfigRepository agentConfigRepository, AgentConfigMapper agentConfigMapper) {
        this.agentConfigRepository = agentConfigRepository;
        this.agentConfigMapper = agentConfigMapper;
    }

    @Override
    public AgentConfigDTO save(AgentConfigDTO agentConfigDTO) {
        LOG.debug("Request to save AgentConfig : {}", agentConfigDTO);
        AgentConfig agentConfig = agentConfigMapper.toEntity(agentConfigDTO);
        agentConfig = agentConfigRepository.save(agentConfig);
        return agentConfigMapper.toDto(agentConfig);
    }

    @Override
    public AgentConfigDTO update(AgentConfigDTO agentConfigDTO) {
        LOG.debug("Request to update AgentConfig : {}", agentConfigDTO);
        AgentConfig agentConfig = agentConfigMapper.toEntity(agentConfigDTO);
        agentConfig = agentConfigRepository.save(agentConfig);
        return agentConfigMapper.toDto(agentConfig);
    }

    @Override
    public Optional<AgentConfigDTO> partialUpdate(AgentConfigDTO agentConfigDTO) {
        LOG.debug("Request to partially update AgentConfig : {}", agentConfigDTO);

        return agentConfigRepository
            .findById(agentConfigDTO.getId())
            .map(existingAgentConfig -> {
                agentConfigMapper.partialUpdate(existingAgentConfig, agentConfigDTO);

                return existingAgentConfig;
            })
            .map(agentConfigRepository::save)
            .map(agentConfigMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AgentConfigDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AgentConfigs");
        return agentConfigRepository.findAll(pageable).map(agentConfigMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgentConfigDTO> findOne(Long id) {
        LOG.debug("Request to get AgentConfig : {}", id);
        return agentConfigRepository.findById(id).map(agentConfigMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AgentConfig : {}", id);
        agentConfigRepository.deleteById(id);
    }
}
