package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.ContractReview}.
 */
public interface ContractReviewService {
    /**
     * Save a contractReview.
     *
     * @param contractReviewDTO the entity to save.
     * @return the persisted entity.
     */
    ContractReviewDTO save(ContractReviewDTO contractReviewDTO);

    /**
     * Updates a contractReview.
     *
     * @param contractReviewDTO the entity to update.
     * @return the persisted entity.
     */
    ContractReviewDTO update(ContractReviewDTO contractReviewDTO);

    /**
     * Partially updates a contractReview.
     *
     * @param contractReviewDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ContractReviewDTO> partialUpdate(ContractReviewDTO contractReviewDTO);

    /**
     * Get all the contractReviews.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ContractReviewDTO> findAll(Pageable pageable);

    /**
     * Get the "id" contractReview.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ContractReviewDTO> findOne(Long id);

    /**
     * Delete the "id" contractReview.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
