package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.ContractClauseIssueDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.ContractClauseIssue}.
 */
public interface ContractClauseIssueService {
    /**
     * Save a contractClauseIssue.
     *
     * @param contractClauseIssueDTO the entity to save.
     * @return the persisted entity.
     */
    ContractClauseIssueDTO save(ContractClauseIssueDTO contractClauseIssueDTO);

    /**
     * Updates a contractClauseIssue.
     *
     * @param contractClauseIssueDTO the entity to update.
     * @return the persisted entity.
     */
    ContractClauseIssueDTO update(ContractClauseIssueDTO contractClauseIssueDTO);

    /**
     * Partially updates a contractClauseIssue.
     *
     * @param contractClauseIssueDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ContractClauseIssueDTO> partialUpdate(ContractClauseIssueDTO contractClauseIssueDTO);

    /**
     * Get all the contractClauseIssues.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ContractClauseIssueDTO> findAll(Pageable pageable);

    /**
     * Get the "id" contractClauseIssue.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ContractClauseIssueDTO> findOne(Long id);

    /**
     * Delete the "id" contractClauseIssue.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
