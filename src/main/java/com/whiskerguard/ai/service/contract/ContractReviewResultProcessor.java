/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewResultProcessor.java
 * 包    名：com.whiskerguard.ai.service.contract
 * 描    述：合同审查结果处理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.*;
import com.whiskerguard.ai.domain.enumeration.PartyType;
import com.whiskerguard.ai.domain.enumeration.RiskCategory;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.*;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同审查结果处理器
 * <p>
 * 负责处理AI返回的审查结果，包括：
 * 1. JSON结果解析和验证
 * 2. 结构化数据转换
 * 3. 关联实体持久化
 * 4. 错误处理和容错
 */
@Component
@Transactional
public class ContractReviewResultProcessor {

    private final Logger log = LoggerFactory.getLogger(ContractReviewResultProcessor.class);

    private final ObjectMapper objectMapper;
    private final ContractPartyRepository contractPartyRepository;
    private final ContractRiskPointRepository contractRiskPointRepository;
    private final ContractClauseIssueRepository contractClauseIssueRepository;

    public ContractReviewResultProcessor(
        ObjectMapper objectMapper,
        ContractPartyRepository contractPartyRepository,
        ContractRiskPointRepository contractRiskPointRepository,
        ContractClauseIssueRepository contractClauseIssueRepository
    ) {
        this.objectMapper = objectMapper;
        this.contractPartyRepository = contractPartyRepository;
        this.contractRiskPointRepository = contractRiskPointRepository;
        this.contractClauseIssueRepository = contractClauseIssueRepository;
    }

    /**
     * 处理审查结果并持久化相关实体
     */
    public void processAndPersistResults(
        ContractReview reviewRecord,
        ContractReviewResponseDTO response,
        List<ContractPartyExtractor.ContractPartyInfo> extractedParties
    ) {
        log.debug("开始处理审查结果，审查ID: {}", reviewRecord.getId());

        try {
            // 1. 持久化关联方信息
            persistContractParties(reviewRecord, extractedParties, response.getPartyAnalysis());

            // 2. 持久化风险点
            persistRiskPoints(reviewRecord, response.getRiskPoints());

            // 3. 持久化条款问题
            persistClauseIssues(reviewRecord, response.getClauseIssues());

            log.debug("审查结果处理完成，审查ID: {}", reviewRecord.getId());
        } catch (Exception e) {
            log.error("处理审查结果失败，审查ID: {}", reviewRecord.getId(), e);
            throw new RuntimeException("审查结果处理失败", e);
        }
    }

    /**
     * 持久化合同关联方信息
     */
    private void persistContractParties(
        ContractReview reviewRecord,
        List<ContractPartyExtractor.ContractPartyInfo> extractedParties,
        List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> partyAnalysis
    ) {
        List<ContractParty> contractParties = new ArrayList<>();

        for (ContractPartyExtractor.ContractPartyInfo extractedParty : extractedParties) {
            ContractParty contractParty = new ContractParty();

            // 基本信息
            contractParty.setTenantId(reviewRecord.getTenantId());
            contractParty.setReview(reviewRecord);
            contractParty.setPartyName(extractedParty.getName());
            contractParty.setPartyType(convertPartyType(extractedParty.getType()));
            contractParty.setPartyRole(extractedParty.getRole());

            // 查找对应的风险分析结果
            ContractReviewResponseDTO.PartyRiskAnalysisDTO riskAnalysis = findPartyRiskAnalysis(extractedParty.getName(), partyAnalysis);

            if (riskAnalysis != null) {
                contractParty.setRiskLevel(parseRiskLevel(riskAnalysis.getRiskLevel()));

                // 将风险因素和合规问题转换为JSON
                try {
                    if (riskAnalysis.getRiskFactors() != null) {
                        contractParty.setRiskFactors(objectMapper.writeValueAsString(riskAnalysis.getRiskFactors()));
                    }
                    if (riskAnalysis.getComplianceIssues() != null) {
                        contractParty.setComplianceIssues(objectMapper.writeValueAsString(riskAnalysis.getComplianceIssues()));
                    }
                } catch (Exception e) {
                    log.warn("转换关联方风险信息为JSON失败: {}", e.getMessage());
                }
            }

            // 审计信息
            contractParty.setCreatedAt(Instant.now());
            contractParty.setVersion(1);
            contractParty.setIsDeleted(false);

            contractParties.add(contractParty);
        }

        // 批量保存
        if (!contractParties.isEmpty()) {
            contractPartyRepository.saveAll(contractParties);
            log.debug("保存了 {} 个关联方记录", contractParties.size());
        }
    }

    /**
     * 持久化风险点信息
     */
    private void persistRiskPoints(ContractReview reviewRecord, List<ContractReviewResponseDTO.RiskPointDTO> riskPoints) {
        if (riskPoints == null || riskPoints.isEmpty()) {
            return;
        }

        List<ContractRiskPoint> contractRiskPoints = new ArrayList<>();

        for (ContractReviewResponseDTO.RiskPointDTO riskPointDTO : riskPoints) {
            ContractRiskPoint riskPoint = new ContractRiskPoint();

            // 基本信息
            riskPoint.setTenantId(reviewRecord.getTenantId());
            riskPoint.setReview(reviewRecord);
            riskPoint.setRiskCategory(parseRiskCategory(riskPointDTO.getCategory()));
            riskPoint.setRiskDescription(riskPointDTO.getDescription());
            riskPoint.setSeverity(riskPointDTO.getSeverity());
            riskPoint.setLegalBasis(riskPointDTO.getLegalBasis());
            riskPoint.setRiskScore(riskPointDTO.getRiskScore());
            riskPoint.setIsCritical(riskPointDTO.getIsCritical());

            // 将涉及条款和建议转换为JSON
            try {
                if (riskPointDTO.getAffectedClauses() != null) {
                    riskPoint.setAffectedClauses(objectMapper.writeValueAsString(riskPointDTO.getAffectedClauses()));
                }
                if (riskPointDTO.getSuggestions() != null) {
                    riskPoint.setSuggestions(objectMapper.writeValueAsString(riskPointDTO.getSuggestions()));
                }
            } catch (Exception e) {
                log.warn("转换风险点信息为JSON失败: {}", e.getMessage());
            }

            // 审计信息
            riskPoint.setCreatedAt(Instant.now());
            riskPoint.setVersion(1);
            riskPoint.setIsDeleted(false);

            contractRiskPoints.add(riskPoint);
        }

        // 批量保存
        contractRiskPointRepository.saveAll(contractRiskPoints);
        log.debug("保存了 {} 个风险点记录", contractRiskPoints.size());
    }

    /**
     * 持久化条款问题信息
     */
    private void persistClauseIssues(ContractReview reviewRecord, List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues) {
        if (clauseIssues == null || clauseIssues.isEmpty()) {
            return;
        }

        List<ContractClauseIssue> contractClauseIssues = new ArrayList<>();

        for (ContractReviewResponseDTO.ClauseIssueDTO clauseIssueDTO : clauseIssues) {
            ContractClauseIssue clauseIssue = new ContractClauseIssue();

            // 基本信息
            clauseIssue.setTenantId(reviewRecord.getTenantId());
            clauseIssue.setReview(reviewRecord);
            clauseIssue.setClauseText(clauseIssueDTO.getClauseText());
            clauseIssue.setClauseNumber(clauseIssueDTO.getClauseNumber());
            clauseIssue.setIssueType(clauseIssueDTO.getIssueType());
            clauseIssue.setIssueDescription(clauseIssueDTO.getDescription());
            clauseIssue.setSeverity(clauseIssueDTO.getSeverity());
            clauseIssue.setLegalRisk(clauseIssueDTO.getLegalRisk());

            // 将建议和参考法规转换为JSON
            try {
                if (clauseIssueDTO.getSuggestions() != null) {
                    clauseIssue.setSuggestions(objectMapper.writeValueAsString(clauseIssueDTO.getSuggestions()));
                }
                if (clauseIssueDTO.getReferenceLaws() != null) {
                    clauseIssue.setReferenceLaws(objectMapper.writeValueAsString(clauseIssueDTO.getReferenceLaws()));
                }
            } catch (Exception e) {
                log.warn("转换条款问题信息为JSON失败: {}", e.getMessage());
            }

            // 审计信息
            clauseIssue.setCreatedAt(Instant.now());
            clauseIssue.setVersion(1);
            clauseIssue.setIsDeleted(false);

            contractClauseIssues.add(clauseIssue);
        }

        // 批量保存
        contractClauseIssueRepository.saveAll(contractClauseIssues);
        log.debug("保存了 {} 个条款问题记录", contractClauseIssues.size());
    }

    /**
     * 查找关联方的风险分析结果
     */
    private ContractReviewResponseDTO.PartyRiskAnalysisDTO findPartyRiskAnalysis(
        String partyName,
        List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> partyAnalysis
    ) {
        if (partyAnalysis == null) {
            return null;
        }

        return partyAnalysis.stream().filter(analysis -> partyName.equals(analysis.getPartyName())).findFirst().orElse(null);
    }

    /**
     * 转换关联方类型
     */
    private PartyType convertPartyType(com.whiskerguard.ai.domain.enumeration.PartyType extractedType) {
        if (extractedType == null) {
            return PartyType.OTHER;
        }

        // 直接返回相同的枚举值，因为它们是同一个枚举
        return extractedType;
    }

    /**
     * 解析风险等级
     */
    private RiskLevel parseRiskLevel(RiskLevel riskLevel) {
        return riskLevel != null ? riskLevel : RiskLevel.MEDIUM;
    }

    /**
     * 解析风险类别
     */
    private RiskCategory parseRiskCategory(String category) {
        if (category == null) {
            return RiskCategory.BUSINESS_RISK;
        }

        try {
            // 尝试直接匹配枚举值
            return RiskCategory.valueOf(category.toUpperCase().replace(" ", "_"));
        } catch (IllegalArgumentException e) {
            // 根据关键词匹配
            String lowerCategory = category.toLowerCase();
            if (lowerCategory.contains("法律") || lowerCategory.contains("合规")) {
                return RiskCategory.LEGAL_COMPLIANCE;
            } else if (lowerCategory.contains("财务") || lowerCategory.contains("金融")) {
                return RiskCategory.FINANCIAL_RISK;
            } else if (lowerCategory.contains("操作") || lowerCategory.contains("运营")) {
                return RiskCategory.OPERATIONAL_RISK;
            } else if (lowerCategory.contains("声誉") || lowerCategory.contains("品牌")) {
                return RiskCategory.REPUTATION_RISK;
            } else {
                return RiskCategory.BUSINESS_RISK;
            }
        }
    }
}
