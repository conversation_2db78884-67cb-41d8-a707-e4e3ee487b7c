/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyContext.java
 * 包    名：com.whiskerguard.ai.service.contract.context
 * 描    述：企业内部制度上下文管理器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract.context;

import com.whiskerguard.ai.client.dto.InternalPolicyDTO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业内部制度上下文管理器
 * <p>
 * 管理企业内部制度和政策信息，
 * 为合同审查提供企业内部合规要求。
 */
public class InternalPolicyContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 所有内部制度 */
    private List<InternalPolicyDTO> allPolicies = new ArrayList<>();

    /** 合同相关制度 */
    private List<InternalPolicyDTO> contractPolicies = new ArrayList<>();

    public InternalPolicyContext() {}

    public InternalPolicyContext(List<InternalPolicyDTO> allPolicies, List<InternalPolicyDTO> contractPolicies) {
        this.allPolicies = allPolicies != null ? allPolicies : new ArrayList<>();
        this.contractPolicies = contractPolicies != null ? contractPolicies : new ArrayList<>();
    }

    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return allPolicies.isEmpty() && contractPolicies.isEmpty();
    }

    /**
     * 获取制度总数
     */
    public int getTotalPolicyCount() {
        return allPolicies.size();
    }

    /**
     * 获取合同相关制度数量
     */
    public int getContractPolicyCount() {
        return contractPolicies.size();
    }

    /**
     * 格式化为提示词文本
     */
    public String formatForPrompt() {
        if (isEmpty()) {
            return "该企业暂无内部制度要求";
        }

        StringBuilder sb = new StringBuilder();

        // 优先显示合同相关制度
        if (!contractPolicies.isEmpty()) {
            sb.append("**合同相关内部制度：**\n");
            for (InternalPolicyDTO policy : contractPolicies) {
                sb.append(formatSinglePolicy(policy, true)).append("\n");
            }
            sb.append("\n");
        }

        // 显示其他重要制度
        List<InternalPolicyDTO> otherImportantPolicies = getOtherImportantPolicies();
        if (!otherImportantPolicies.isEmpty()) {
            sb.append("**其他重要内部制度：**\n");
            for (InternalPolicyDTO policy : otherImportantPolicies) {
                sb.append(formatSinglePolicy(policy, false)).append("\n");
            }
        }

        return sb.toString().trim();
    }

    /**
     * 格式化单个制度信息
     */
    private String formatSinglePolicy(InternalPolicyDTO policy, boolean detailed) {
        StringBuilder sb = new StringBuilder();

        sb.append("- **").append(policy.getPolicyName()).append("**");

        if (policy.getPolicyLevel() != null) {
            sb.append("（").append(policy.getPolicyLevel()).append("）");
        }

        if (Boolean.TRUE.equals(policy.getMandatory())) {
            sb.append("【强制执行】");
        }

        sb.append("\n");

        // 详细信息
        if (detailed) {
            if (policy.getSummary() != null) {
                sb.append("  制度摘要：").append(truncateText(policy.getSummary(), 150)).append("\n");
            }

            if (policy.getKeyRequirements() != null && !policy.getKeyRequirements().isEmpty()) {
                sb.append("  关键要求：");
                for (String requirement : policy.getKeyRequirements()) {
                    sb.append(requirement).append("；");
                }
                sb.append("\n");
            }

            if (policy.getComplianceCheckpoints() != null && !policy.getComplianceCheckpoints().isEmpty()) {
                sb.append("  合规检查点：");
                for (String checkpoint : policy.getComplianceCheckpoints()) {
                    sb.append(checkpoint).append("；");
                }
                sb.append("\n");
            }

            if (policy.getViolationConsequences() != null) {
                sb.append("  违规后果：").append(truncateText(policy.getViolationConsequences(), 100)).append("\n");
            }
        } else {
            // 简化信息
            if (policy.getSummary() != null) {
                sb.append("  ").append(truncateText(policy.getSummary(), 80)).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 获取其他重要制度（排除合同相关制度）
     */
    private List<InternalPolicyDTO> getOtherImportantPolicies() {
        return allPolicies
            .stream()
            .filter(policy -> !isContractPolicy(policy))
            .filter(policy -> isImportantPolicy(policy))
            .limit(5) // 限制数量，避免提示词过长
            .collect(Collectors.toList());
    }

    /**
     * 判断是否为合同相关制度
     */
    private boolean isContractPolicy(InternalPolicyDTO policy) {
        return contractPolicies.stream().anyMatch(cp -> cp.getId().equals(policy.getId()));
    }

    /**
     * 判断是否为重要制度
     */
    private boolean isImportantPolicy(InternalPolicyDTO policy) {
        // 强制执行的制度
        if (Boolean.TRUE.equals(policy.getMandatory())) {
            return true;
        }

        // 重要程度高的制度
        if (policy.getImportance() != null && policy.getImportance() >= 4) {
            return true;
        }

        // 特定类别的制度
        String category = policy.getCategory();
        if (category != null) {
            return (
                category.contains("FINANCE") || category.contains("LEGAL") || category.contains("RISK") || category.contains("COMPLIANCE")
            );
        }

        return false;
    }

    /**
     * 获取强制执行的制度
     */
    public List<InternalPolicyDTO> getMandatoryPolicies() {
        List<InternalPolicyDTO> mandatoryPolicies = new ArrayList<>();

        mandatoryPolicies.addAll(
            allPolicies.stream().filter(policy -> Boolean.TRUE.equals(policy.getMandatory())).collect(Collectors.toList())
        );

        return mandatoryPolicies;
    }

    /**
     * 获取特定类别的制度
     */
    public List<InternalPolicyDTO> getPoliciesByCategory(String category) {
        return allPolicies.stream().filter(policy -> category.equals(policy.getCategory())).collect(Collectors.toList());
    }

    /**
     * 获取违规后果严重的制度
     */
    public List<InternalPolicyDTO> getHighRiskPolicies() {
        return allPolicies
            .stream()
            .filter(policy -> policy.getViolationConsequences() != null)
            .filter(policy -> {
                String consequences = policy.getViolationConsequences().toLowerCase();
                return (
                    consequences.contains("解除") ||
                    consequences.contains("终止") ||
                    consequences.contains("赔偿") ||
                    consequences.contains("处罚")
                );
            })
            .collect(Collectors.toList());
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "无";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "...";
    }

    // Getters and Setters
    public List<InternalPolicyDTO> getAllPolicies() {
        return allPolicies;
    }

    public void setAllPolicies(List<InternalPolicyDTO> allPolicies) {
        this.allPolicies = allPolicies != null ? allPolicies : new ArrayList<>();
    }

    public List<InternalPolicyDTO> getContractPolicies() {
        return contractPolicies;
    }

    public void setContractPolicies(List<InternalPolicyDTO> contractPolicies) {
        this.contractPolicies = contractPolicies != null ? contractPolicies : new ArrayList<>();
    }

    @Override
    public String toString() {
        return (
            "InternalPolicyContext{" +
            "totalPolicyCount=" +
            allPolicies.size() +
            ", contractPolicyCount=" +
            contractPolicies.size() +
            ", mandatoryPolicyCount=" +
            getMandatoryPolicies().size() +
            '}'
        );
    }
}
