package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.ContractPartyDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.ContractParty}.
 */
public interface ContractPartyService {
    /**
     * Save a contractParty.
     *
     * @param contractPartyDTO the entity to save.
     * @return the persisted entity.
     */
    ContractPartyDTO save(ContractPartyDTO contractPartyDTO);

    /**
     * Updates a contractParty.
     *
     * @param contractPartyDTO the entity to update.
     * @return the persisted entity.
     */
    ContractPartyDTO update(ContractPartyDTO contractPartyDTO);

    /**
     * Partially updates a contractParty.
     *
     * @param contractPartyDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ContractPartyDTO> partialUpdate(ContractPartyDTO contractPartyDTO);

    /**
     * Get all the contractParties.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ContractPartyDTO> findAll(Pageable pageable);

    /**
     * Get the "id" contractParty.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ContractPartyDTO> findOne(Long id);

    /**
     * Delete the "id" contractParty.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
