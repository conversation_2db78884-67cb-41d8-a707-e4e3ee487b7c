/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiReviewService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：AI评审管理服务接口，定义AI评审相关的业务操作
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.domain.enumeration.ReviewResult;
import com.whiskerguard.ai.service.dto.AiReviewDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * AI评审管理服务接口
 * <p>
 * Service Interface for managing {@link com.whiskerguard.ai.domain.AiReview}.
 * <p>
 * 定义了AI评审实体的增删改查以及按条件筛选等业务操作方法。
 */
public interface AiReviewService {
    /**
     * 保存AI评审
     * <p>
     * Save a aiReview.
     *
     * @param aiReviewDTO 要保存的实体。the entity to save.
     * @return 持久化后的实体。the persisted entity.
     */
    AiReviewDTO save(AiReviewDTO aiReviewDTO);

    /**
     * 更新AI评审
     * <p>
     * Updates a aiReview.
     *
     * @param aiReviewDTO 要更新的实体。the entity to update.
     * @return 持久化后的实体。the persisted entity.
     */
    AiReviewDTO update(AiReviewDTO aiReviewDTO);

    /**
     * 部分更新AI评审
     * <p>
     * Partially updates a aiReview.
     *
     * @param aiReviewDTO 要部分更新的实体。the entity to update partially.
     * @return 持久化后的实体。the persisted entity.
     */
    Optional<AiReviewDTO> partialUpdate(AiReviewDTO aiReviewDTO);

    /**
     * 获取所有AI评审
     * <p>
     * Get all the aiReviews.
     *
     * @param pageable 分页信息。the pagination information.
     * @return 实体列表。the list of entities.
     */
    Page<AiReviewDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取AI评审
     * <p>
     * Get the "id" aiReview.
     *
     * @param id 实体的ID。the id of the entity.
     * @return 实体。the entity.
     */
    Optional<AiReviewDTO> findOne(Long id);

    /**
     * 根据ID删除AI评审
     * <p>
     * Delete the "id" aiReview.
     *
     * @param id 要删除实体的ID。the id of the entity.
     */
    void delete(Long id);

    /**
     * 根据员工ID分页查询AI审核列表
     * <p>
     * Find AI reviews by employee ID with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI审核DTO列表。paginated list of AI review DTOs.
     */
    Page<AiReviewDTO> findByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据员工ID和审核结果分页查询AI审核列表
     * <p>
     * Find AI reviews by employee ID and review result with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param reviewResult 审核结果。review result.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI审核DTO列表。paginated list of AI review DTOs.
     */
    Page<AiReviewDTO> findByEmployeeIdAndReviewResult(Long employeeId, ReviewResult reviewResult, Pageable pageable);

    /**
     * 根据员工ID和审核人分页查询AI审核列表
     * <p>
     * Find AI reviews by employee ID and reviewer with pagination.
     *
     * @param employeeId 员工ID。employee ID.
     * @param reviewer 审核人。reviewer.
     * @param pageable 分页参数。pagination parameters.
     * @return 分页的AI审核DTO列表。paginated list of AI review DTOs.
     */
    Page<AiReviewDTO> findByEmployeeIdAndReviewer(Long employeeId, String reviewer, Pageable pageable);
}
