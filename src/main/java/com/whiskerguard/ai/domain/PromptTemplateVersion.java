package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 提示词模板版本（PromptTemplateVersion）实体
 * 管理模板的版本历史
 */
@Entity
@Table(name = "prompt_template_version")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateVersion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 版本号
     */
    @NotNull
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;

    /**
     * 版本名称
     */
    @Size(max = 200)
    @Column(name = "version_name", length = 200)
    private String versionName;

    /**
     * 版本描述
     */
    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 模板内容
     */
    @Lob
    @Column(name = "content", nullable = false)
    private String content;

    /**
     * 变量定义JSON
     */
    @Lob
    @Column(name = "variables_definition")
    private String variablesDefinition;

    /**
     * 版本状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PromptTemplateStatus status;

    /**
     * 是否为当前活跃版本
     */
    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    /**
     * 创建者ID
     */
    @Column(name = "created_by_id")
    private Long createdById;

    /**
     * 版本变更说明
     */
    @Size(max = 1000)
    @Column(name = "change_log", length = 1000)
    private String changeLog;

    /**
     * 使用次数统计
     */
    @Column(name = "usage_count")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    /**
     * 性能评分
     */
    @Column(name = "performance_score")
    private Double performanceScore;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "promptTemplateVariables", "promptTemplateVersions" }, allowSetters = true)
    private PromptTemplate promptTemplate;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromptTemplateVersion id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PromptTemplateVersion tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getVersionNumber() {
        return this.versionNumber;
    }

    public PromptTemplateVersion versionNumber(Integer versionNumber) {
        this.setVersionNumber(versionNumber);
        return this;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getVersionName() {
        return this.versionName;
    }

    public PromptTemplateVersion versionName(String versionName) {
        this.setVersionName(versionName);
        return this;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getDescription() {
        return this.description;
    }

    public PromptTemplateVersion description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return this.content;
    }

    public PromptTemplateVersion content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVariablesDefinition() {
        return this.variablesDefinition;
    }

    public PromptTemplateVersion variablesDefinition(String variablesDefinition) {
        this.setVariablesDefinition(variablesDefinition);
        return this;
    }

    public void setVariablesDefinition(String variablesDefinition) {
        this.variablesDefinition = variablesDefinition;
    }

    public PromptTemplateStatus getStatus() {
        return this.status;
    }

    public PromptTemplateVersion status(PromptTemplateStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Boolean getIsActive() {
        return this.isActive;
    }

    public PromptTemplateVersion isActive(Boolean isActive) {
        this.setIsActive(isActive);
        return this;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Long getCreatedById() {
        return this.createdById;
    }

    public PromptTemplateVersion createdById(Long createdById) {
        this.setCreatedById(createdById);
        return this;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public String getChangeLog() {
        return this.changeLog;
    }

    public PromptTemplateVersion changeLog(String changeLog) {
        this.setChangeLog(changeLog);
        return this;
    }

    public void setChangeLog(String changeLog) {
        this.changeLog = changeLog;
    }

    public Long getUsageCount() {
        return this.usageCount;
    }

    public PromptTemplateVersion usageCount(Long usageCount) {
        this.setUsageCount(usageCount);
        return this;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return this.lastUsedAt;
    }

    public PromptTemplateVersion lastUsedAt(Instant lastUsedAt) {
        this.setLastUsedAt(lastUsedAt);
        return this;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Double getPerformanceScore() {
        return this.performanceScore;
    }

    public PromptTemplateVersion performanceScore(Double performanceScore) {
        this.setPerformanceScore(performanceScore);
        return this;
    }

    public void setPerformanceScore(Double performanceScore) {
        this.performanceScore = performanceScore;
    }

    public Integer getVersion() {
        return this.version;
    }

    public PromptTemplateVersion version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public PromptTemplateVersion createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public PromptTemplateVersion createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public PromptTemplateVersion updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public PromptTemplateVersion updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public PromptTemplateVersion isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public PromptTemplate getPromptTemplate() {
        return this.promptTemplate;
    }

    public void setPromptTemplate(PromptTemplate promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    public PromptTemplateVersion promptTemplate(PromptTemplate promptTemplate) {
        this.setPromptTemplate(promptTemplate);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateVersion)) {
            return false;
        }
        return getId() != null && getId().equals(((PromptTemplateVersion) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateVersion{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", versionNumber=" + getVersionNumber() +
            ", versionName='" + getVersionName() + "'" +
            ", description='" + getDescription() + "'" +
            ", content='" + getContent() + "'" +
            ", variablesDefinition='" + getVariablesDefinition() + "'" +
            ", status='" + getStatus() + "'" +
            ", isActive='" + getIsActive() + "'" +
            ", createdById=" + getCreatedById() +
            ", changeLog='" + getChangeLog() + "'" +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            ", performanceScore=" + getPerformanceScore() +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
