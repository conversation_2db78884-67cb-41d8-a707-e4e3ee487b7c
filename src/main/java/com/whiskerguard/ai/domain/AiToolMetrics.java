package com.whiskerguard.ai.domain;

import com.whiskerguard.ai.domain.enumeration.MetricsPeriod;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * AI工具指标（AiToolMetrics）实体
 */
@Entity
@Table(name = "ai_tool_metrics")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiToolMetrics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 统计周期
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "period", nullable = false)
    private MetricsPeriod period;

    /**
     * 响应时间
     */
    @Column(name = "response_time")
    private Integer responseTime;

    /**
     * 成功次数
     */
    @Column(name = "success_count")
    private Long successCount;

    /**
     * 失败次数
     */
    @Column(name = "failure_count")
    private Long failureCount;

    /**
     * 总请求数
     */
    @Column(name = "total_requests")
    private Long totalRequests;

    /**
     * 错误率
     */
    @Column(name = "error_rate")
    private Float errorRate;

    /**
     * 采集日期
     */
    @NotNull
    @Column(name = "collect_date", nullable = false)
    private Instant collectDate;

    /**
     * 扩展元数据
     */
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version")
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at")
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    private AiTool tool;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AiToolMetrics id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public AiToolMetrics tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public MetricsPeriod getPeriod() {
        return this.period;
    }

    public AiToolMetrics period(MetricsPeriod period) {
        this.setPeriod(period);
        return this;
    }

    public void setPeriod(MetricsPeriod period) {
        this.period = period;
    }

    public Integer getResponseTime() {
        return this.responseTime;
    }

    public AiToolMetrics responseTime(Integer responseTime) {
        this.setResponseTime(responseTime);
        return this;
    }

    public void setResponseTime(Integer responseTime) {
        this.responseTime = responseTime;
    }

    public Long getSuccessCount() {
        return this.successCount;
    }

    public AiToolMetrics successCount(Long successCount) {
        this.setSuccessCount(successCount);
        return this;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailureCount() {
        return this.failureCount;
    }

    public AiToolMetrics failureCount(Long failureCount) {
        this.setFailureCount(failureCount);
        return this;
    }

    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }

    public Long getTotalRequests() {
        return this.totalRequests;
    }

    public AiToolMetrics totalRequests(Long totalRequests) {
        this.setTotalRequests(totalRequests);
        return this;
    }

    public void setTotalRequests(Long totalRequests) {
        this.totalRequests = totalRequests;
    }

    public Float getErrorRate() {
        return this.errorRate;
    }

    public AiToolMetrics errorRate(Float errorRate) {
        this.setErrorRate(errorRate);
        return this;
    }

    public void setErrorRate(Float errorRate) {
        this.errorRate = errorRate;
    }

    public Instant getCollectDate() {
        return this.collectDate;
    }

    public AiToolMetrics collectDate(Instant collectDate) {
        this.setCollectDate(collectDate);
        return this;
    }

    public void setCollectDate(Instant collectDate) {
        this.collectDate = collectDate;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public AiToolMetrics metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AiToolMetrics version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AiToolMetrics createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AiToolMetrics createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AiToolMetrics updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AiToolMetrics updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AiToolMetrics isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AiTool getTool() {
        return this.tool;
    }

    public void setTool(AiTool aiTool) {
        this.tool = aiTool;
    }

    public AiToolMetrics tool(AiTool aiTool) {
        this.setTool(aiTool);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiToolMetrics)) {
            return false;
        }
        return getId() != null && getId().equals(((AiToolMetrics) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiToolMetrics{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", period='" + getPeriod() + "'" +
            ", responseTime=" + getResponseTime() +
            ", successCount=" + getSuccessCount() +
            ", failureCount=" + getFailureCount() +
            ", totalRequests=" + getTotalRequests() +
            ", errorRate=" + getErrorRate() +
            ", collectDate='" + getCollectDate() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
