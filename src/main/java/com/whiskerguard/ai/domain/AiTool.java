package com.whiskerguard.ai.domain;

import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * AI工具（AiTool）实体
 */
@Entity
@Table(name = "ai_tool")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiTool implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 工具名称
     */
    @NotNull
    @Size(max = 32)
    @Column(name = "name", length = 32, nullable = false)
    private String name;

    /**
     * 工具关键词
     */
    @NotNull
    @Size(max = 32)
    @Column(name = "tool_key", length = 32, nullable = false)
    private String toolKey;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * API 地址
     */
    @NotNull
    @Size(max = 256)
    @Column(name = "api_url", length = 256, nullable = false)
    private String apiUrl;

    /**
     * API 密钥
     */
    @NotNull
    @Size(max = 256)
    @Column(name = "api_key", length = 256, nullable = false)
    private String apiKey;

    /**
     * 鉴权类型
     */
    @Size(max = 32)
    @Column(name = "auth_type", length = 32)
    private String authType;

    /**
     * 接口路径
     */
    @Size(max = 128)
    @Column(name = "path", length = 128)
    private String path;

    /**
     * 工具状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ToolStatus status;

    /**
     * 路由权重
     */
    @NotNull
    @Column(name = "weight", nullable = false)
    private Integer weight;

    /**
     * 并发许可数
     */
    @NotNull
    @Column(name = "max_concurrent_calls", nullable = false)
    private Integer maxConcurrentCalls;

    /**
     * 是否为模型类型
     */
    @Column(name = "is_model")
    private Boolean isModel;

    /**
     * 模型分类
     */
    @Size(max = 32)
    @Column(name = "model_category", length = 32)
    private String modelCategory;

    /**
     * 模型提供商
     */
    @Size(max = 64)
    @Column(name = "model_provider", length = 64)
    private String modelProvider;

    /**
     * 备注信息
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 扩展元数据
     */
    @Column(name = "metadata")
    private String metadata;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AiTool id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public AiTool tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return this.name;
    }

    public AiTool name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getToolKey() {
        return this.toolKey;
    }

    public AiTool toolKey(String toolKey) {
        this.setToolKey(toolKey);
        return this;
    }

    public void setToolKey(String toolKey) {
        this.toolKey = toolKey;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AiTool version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getApiUrl() {
        return this.apiUrl;
    }

    public AiTool apiUrl(String apiUrl) {
        this.setApiUrl(apiUrl);
        return this;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiKey() {
        return this.apiKey;
    }

    public AiTool apiKey(String apiKey) {
        this.setApiKey(apiKey);
        return this;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getAuthType() {
        return this.authType;
    }

    public AiTool authType(String authType) {
        this.setAuthType(authType);
        return this;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getPath() {
        return this.path;
    }

    public AiTool path(String path) {
        this.setPath(path);
        return this;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public ToolStatus getStatus() {
        return this.status;
    }

    public AiTool status(ToolStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(ToolStatus status) {
        this.status = status;
    }

    public Integer getWeight() {
        return this.weight;
    }

    public AiTool weight(Integer weight) {
        this.setWeight(weight);
        return this;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public Integer getMaxConcurrentCalls() {
        return this.maxConcurrentCalls;
    }

    public AiTool maxConcurrentCalls(Integer maxConcurrentCalls) {
        this.setMaxConcurrentCalls(maxConcurrentCalls);
        return this;
    }

    public void setMaxConcurrentCalls(Integer maxConcurrentCalls) {
        this.maxConcurrentCalls = maxConcurrentCalls;
    }

    public Boolean getIsModel() {
        return this.isModel;
    }

    public AiTool isModel(Boolean isModel) {
        this.setIsModel(isModel);
        return this;
    }

    public void setIsModel(Boolean isModel) {
        this.isModel = isModel;
    }

    public String getModelCategory() {
        return this.modelCategory;
    }

    public AiTool modelCategory(String modelCategory) {
        this.setModelCategory(modelCategory);
        return this;
    }

    public void setModelCategory(String modelCategory) {
        this.modelCategory = modelCategory;
    }

    public String getModelProvider() {
        return this.modelProvider;
    }

    public AiTool modelProvider(String modelProvider) {
        this.setModelProvider(modelProvider);
        return this;
    }

    public void setModelProvider(String modelProvider) {
        this.modelProvider = modelProvider;
    }

    public String getRemark() {
        return this.remark;
    }

    public AiTool remark(String remark) {
        this.setRemark(remark);
        return this;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public AiTool metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AiTool createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AiTool createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AiTool updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AiTool updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AiTool isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiTool)) {
            return false;
        }
        return getId() != null && getId().equals(((AiTool) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiTool{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", name='" + getName() + "'" +
            ", toolKey='" + getToolKey() + "'" +
            ", version=" + getVersion() +
            ", apiUrl='" + getApiUrl() + "'" +
            ", apiKey='" + getApiKey() + "'" +
            ", authType='" + getAuthType() + "'" +
            ", path='" + getPath() + "'" +
            ", status='" + getStatus() + "'" +
            ", weight=" + getWeight() +
            ", maxConcurrentCalls=" + getMaxConcurrentCalls() +
            ", isModel='" + getIsModel() + "'" +
            ", modelCategory='" + getModelCategory() + "'" +
            ", modelProvider='" + getModelProvider() + "'" +
            ", remark='" + getRemark() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
