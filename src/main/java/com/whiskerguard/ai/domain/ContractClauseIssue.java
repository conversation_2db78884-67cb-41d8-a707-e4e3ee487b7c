package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 条款问题实体
 * 存储合同条款中发现的具体问题
 */
@Entity
@Table(name = "contract_clause_issue")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractClauseIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 条款内容
     */
    @Lob
    @Column(name = "clause_text", nullable = false)
    private String clauseText;

    /**
     * 条款编号/位置
     */
    @Size(max = 32)
    @Column(name = "clause_number", length = 32)
    private String clauseNumber;

    /**
     * 问题类型
     */
    @Size(max = 64)
    @Column(name = "issue_type", length = 64)
    private String issueType;

    /**
     * 问题描述
     */
    @Lob
    @Column(name = "issue_description", nullable = false)
    private String issueDescription;

    /**
     * 严重程度
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false)
    private RiskLevel severity;

    /**
     * 法律风险说明
     */
    @Lob
    @Column(name = "legal_risk")
    private String legalRisk;

    /**
     * 修改建议
     */
    @Lob
    @Column(name = "suggestions")
    private String suggestions;

    /**
     * 参考法规
     */
    @Lob
    @Column(name = "reference_laws")
    private String referenceLaws;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "review_id")
    @JsonIgnoreProperties(value = { "contractParties", "contractRiskPoints", "contractClauseIssues" }, allowSetters = true)
    private ContractReview review;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ContractClauseIssue id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ContractClauseIssue tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getClauseText() {
        return this.clauseText;
    }

    public ContractClauseIssue clauseText(String clauseText) {
        this.setClauseText(clauseText);
        return this;
    }

    public void setClauseText(String clauseText) {
        this.clauseText = clauseText;
    }

    public String getClauseNumber() {
        return this.clauseNumber;
    }

    public ContractClauseIssue clauseNumber(String clauseNumber) {
        this.setClauseNumber(clauseNumber);
        return this;
    }

    public void setClauseNumber(String clauseNumber) {
        this.clauseNumber = clauseNumber;
    }

    public String getIssueType() {
        return this.issueType;
    }

    public ContractClauseIssue issueType(String issueType) {
        this.setIssueType(issueType);
        return this;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public String getIssueDescription() {
        return this.issueDescription;
    }

    public ContractClauseIssue issueDescription(String issueDescription) {
        this.setIssueDescription(issueDescription);
        return this;
    }

    public void setIssueDescription(String issueDescription) {
        this.issueDescription = issueDescription;
    }

    public RiskLevel getSeverity() {
        return this.severity;
    }

    public ContractClauseIssue severity(RiskLevel severity) {
        this.setSeverity(severity);
        return this;
    }

    public void setSeverity(RiskLevel severity) {
        this.severity = severity;
    }

    public String getLegalRisk() {
        return this.legalRisk;
    }

    public ContractClauseIssue legalRisk(String legalRisk) {
        this.setLegalRisk(legalRisk);
        return this;
    }

    public void setLegalRisk(String legalRisk) {
        this.legalRisk = legalRisk;
    }

    public String getSuggestions() {
        return this.suggestions;
    }

    public ContractClauseIssue suggestions(String suggestions) {
        this.setSuggestions(suggestions);
        return this;
    }

    public void setSuggestions(String suggestions) {
        this.suggestions = suggestions;
    }

    public String getReferenceLaws() {
        return this.referenceLaws;
    }

    public ContractClauseIssue referenceLaws(String referenceLaws) {
        this.setReferenceLaws(referenceLaws);
        return this;
    }

    public void setReferenceLaws(String referenceLaws) {
        this.referenceLaws = referenceLaws;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ContractClauseIssue version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public ContractClauseIssue createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public ContractClauseIssue updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ContractClauseIssue isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public ContractReview getReview() {
        return this.review;
    }

    public void setReview(ContractReview contractReview) {
        this.review = contractReview;
    }

    public ContractClauseIssue review(ContractReview contractReview) {
        this.setReview(contractReview);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractClauseIssue)) {
            return false;
        }
        return getId() != null && getId().equals(((ContractClauseIssue) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractClauseIssue{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", clauseText='" + getClauseText() + "'" +
            ", clauseNumber='" + getClauseNumber() + "'" +
            ", issueType='" + getIssueType() + "'" +
            ", issueDescription='" + getIssueDescription() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", legalRisk='" + getLegalRisk() + "'" +
            ", suggestions='" + getSuggestions() + "'" +
            ", referenceLaws='" + getReferenceLaws() + "'" +
            ", version=" + getVersion() +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
