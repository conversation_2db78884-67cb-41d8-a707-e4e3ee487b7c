package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.VariableType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 提示词模板变量（PromptTemplateVariable）实体
 * 定义模板中使用的变量信息
 */
@Entity
@Table(name = "prompt_template_variable")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateVariable implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 变量名称
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "variable_name", length = 100, nullable = false)
    private String variableName;

    /**
     * 变量显示名称
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "display_name", length = 200, nullable = false)
    private String displayName;

    /**
     * 变量描述
     */
    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 变量类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "variable_type", nullable = false)
    private VariableType variableType;

    /**
     * 默认值
     */
    @Lob
    @Column(name = "default_value")
    private String defaultValue;

    /**
     * 是否必填
     */
    @NotNull
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired;

    /**
     * 变量验证规则
     */
    @Size(max = 500)
    @Column(name = "validation_rule", length = 500)
    private String validationRule;

    /**
     * 变量示例值
     */
    @Lob
    @Column(name = "example_value")
    private String exampleValue;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @NotNull
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "promptTemplateVariables", "promptTemplateVersions" }, allowSetters = true)
    private PromptTemplate promptTemplate;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromptTemplateVariable id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PromptTemplateVariable tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getVariableName() {
        return this.variableName;
    }

    public PromptTemplateVariable variableName(String variableName) {
        this.setVariableName(variableName);
        return this;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public PromptTemplateVariable displayName(String displayName) {
        this.setDisplayName(displayName);
        return this;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return this.description;
    }

    public PromptTemplateVariable description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public VariableType getVariableType() {
        return this.variableType;
    }

    public PromptTemplateVariable variableType(VariableType variableType) {
        this.setVariableType(variableType);
        return this;
    }

    public void setVariableType(VariableType variableType) {
        this.variableType = variableType;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public PromptTemplateVariable defaultValue(String defaultValue) {
        this.setDefaultValue(defaultValue);
        return this;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getIsRequired() {
        return this.isRequired;
    }

    public PromptTemplateVariable isRequired(Boolean isRequired) {
        this.setIsRequired(isRequired);
        return this;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getValidationRule() {
        return this.validationRule;
    }

    public PromptTemplateVariable validationRule(String validationRule) {
        this.setValidationRule(validationRule);
        return this;
    }

    public void setValidationRule(String validationRule) {
        this.validationRule = validationRule;
    }

    public String getExampleValue() {
        return this.exampleValue;
    }

    public PromptTemplateVariable exampleValue(String exampleValue) {
        this.setExampleValue(exampleValue);
        return this;
    }

    public void setExampleValue(String exampleValue) {
        this.exampleValue = exampleValue;
    }

    public Integer getSortOrder() {
        return this.sortOrder;
    }

    public PromptTemplateVariable sortOrder(Integer sortOrder) {
        this.setSortOrder(sortOrder);
        return this;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsEnabled() {
        return this.isEnabled;
    }

    public PromptTemplateVariable isEnabled(Boolean isEnabled) {
        this.setIsEnabled(isEnabled);
        return this;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Integer getVersion() {
        return this.version;
    }

    public PromptTemplateVariable version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public PromptTemplateVariable createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public PromptTemplateVariable createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public PromptTemplateVariable updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public PromptTemplateVariable updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public PromptTemplateVariable isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public PromptTemplate getPromptTemplate() {
        return this.promptTemplate;
    }

    public void setPromptTemplate(PromptTemplate promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    public PromptTemplateVariable promptTemplate(PromptTemplate promptTemplate) {
        this.setPromptTemplate(promptTemplate);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateVariable)) {
            return false;
        }
        return getId() != null && getId().equals(((PromptTemplateVariable) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateVariable{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", variableName='" + getVariableName() + "'" +
            ", displayName='" + getDisplayName() + "'" +
            ", description='" + getDescription() + "'" +
            ", variableType='" + getVariableType() + "'" +
            ", defaultValue='" + getDefaultValue() + "'" +
            ", isRequired='" + getIsRequired() + "'" +
            ", validationRule='" + getValidationRule() + "'" +
            ", exampleValue='" + getExampleValue() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", isEnabled='" + getIsEnabled() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
