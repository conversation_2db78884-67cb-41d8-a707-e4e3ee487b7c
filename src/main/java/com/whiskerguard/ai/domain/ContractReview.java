package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.ReviewStatus;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 合同审查记录实体
 * 存储合同审查的基本信息和结果
 */
@Entity
@Table(name = "contract_review")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ContractReview implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID - 多租户数据隔离
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 员工ID
     */
    @NotNull
    @Column(name = "employee_id", nullable = false)
    private Long employeeId;

    /**
     * 合同类型
     */
    @Size(max = 64)
    @Column(name = "contract_type", length = 64)
    private String contractType;

    /**
     * 合同标题
     */
    @Size(max = 256)
    @Column(name = "contract_title", length = 256)
    private String contractTitle;

    /**
     * 合同内容
     */
    @Lob
    @Column(name = "contract_content", nullable = false, columnDefinition = "LONGTEXT")
    private String contractContent;

    /**
     * 审查结果（JSON格式）
     */
    @Lob
    @Column(name = "review_result", columnDefinition = "LONGTEXT")
    private String reviewResult;

    /**
     * 审查状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ReviewStatus status;

    /**
     * 整体风险等级
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "overall_risk_level")
    private RiskLevel overallRiskLevel;

    /**
     * 风险分数 (0-100)
     */
    @Min(value = 0)
    @Max(value = 100)
    @Column(name = "risk_score")
    private Integer riskScore;

    /**
     * 风险总结
     */
    @Lob
    @Column(name = "risk_summary")
    private String riskSummary;

    /**
     * AI调用ID - 关联到ai_request表
     */
    @Column(name = "ai_request_id")
    private Long aiRequestId;

    /**
     * 审查开始时间
     */
    @Column(name = "review_start_time")
    private Instant reviewStartTime;

    /**
     * 审查完成时间
     */
    @Column(name = "review_end_time")
    private Instant reviewEndTime;

    /**
     * 审查耗时（毫秒）
     */
    @Column(name = "review_duration")
    private Long reviewDuration;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "review")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "review" }, allowSetters = true)
    private Set<ContractParty> contractParties = new HashSet<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "review")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "review" }, allowSetters = true)
    private Set<ContractRiskPoint> contractRiskPoints = new HashSet<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "review")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "review" }, allowSetters = true)
    private Set<ContractClauseIssue> contractClauseIssues = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ContractReview id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ContractReview tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public ContractReview employeeId(Long employeeId) {
        this.setEmployeeId(employeeId);
        return this;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getContractType() {
        return this.contractType;
    }

    public ContractReview contractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractTitle() {
        return this.contractTitle;
    }

    public ContractReview contractTitle(String contractTitle) {
        this.setContractTitle(contractTitle);
        return this;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }

    public String getContractContent() {
        return this.contractContent;
    }

    public ContractReview contractContent(String contractContent) {
        this.setContractContent(contractContent);
        return this;
    }

    public void setContractContent(String contractContent) {
        this.contractContent = contractContent;
    }

    public String getReviewResult() {
        return this.reviewResult;
    }

    public ContractReview reviewResult(String reviewResult) {
        this.setReviewResult(reviewResult);
        return this;
    }

    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    public ReviewStatus getStatus() {
        return this.status;
    }

    public ContractReview status(ReviewStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public RiskLevel getOverallRiskLevel() {
        return this.overallRiskLevel;
    }

    public ContractReview overallRiskLevel(RiskLevel overallRiskLevel) {
        this.setOverallRiskLevel(overallRiskLevel);
        return this;
    }

    public void setOverallRiskLevel(RiskLevel overallRiskLevel) {
        this.overallRiskLevel = overallRiskLevel;
    }

    public Integer getRiskScore() {
        return this.riskScore;
    }

    public ContractReview riskScore(Integer riskScore) {
        this.setRiskScore(riskScore);
        return this;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public String getRiskSummary() {
        return this.riskSummary;
    }

    public ContractReview riskSummary(String riskSummary) {
        this.setRiskSummary(riskSummary);
        return this;
    }

    public void setRiskSummary(String riskSummary) {
        this.riskSummary = riskSummary;
    }

    public Long getAiRequestId() {
        return this.aiRequestId;
    }

    public ContractReview aiRequestId(Long aiRequestId) {
        this.setAiRequestId(aiRequestId);
        return this;
    }

    public void setAiRequestId(Long aiRequestId) {
        this.aiRequestId = aiRequestId;
    }

    public Instant getReviewStartTime() {
        return this.reviewStartTime;
    }

    public ContractReview reviewStartTime(Instant reviewStartTime) {
        this.setReviewStartTime(reviewStartTime);
        return this;
    }

    public void setReviewStartTime(Instant reviewStartTime) {
        this.reviewStartTime = reviewStartTime;
    }

    public Instant getReviewEndTime() {
        return this.reviewEndTime;
    }

    public ContractReview reviewEndTime(Instant reviewEndTime) {
        this.setReviewEndTime(reviewEndTime);
        return this;
    }

    public void setReviewEndTime(Instant reviewEndTime) {
        this.reviewEndTime = reviewEndTime;
    }

    public Long getReviewDuration() {
        return this.reviewDuration;
    }

    public ContractReview reviewDuration(Long reviewDuration) {
        this.setReviewDuration(reviewDuration);
        return this;
    }

    public void setReviewDuration(Long reviewDuration) {
        this.reviewDuration = reviewDuration;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public ContractReview metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ContractReview version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public ContractReview createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public ContractReview createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public ContractReview updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public ContractReview updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ContractReview isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Set<ContractParty> getContractParties() {
        return this.contractParties;
    }

    public void setContractParties(Set<ContractParty> contractParties) {
        if (this.contractParties != null) {
            this.contractParties.forEach(i -> i.setReview(null));
        }
        if (contractParties != null) {
            contractParties.forEach(i -> i.setReview(this));
        }
        this.contractParties = contractParties;
    }

    public ContractReview contractParties(Set<ContractParty> contractParties) {
        this.setContractParties(contractParties);
        return this;
    }

    public ContractReview addContractParty(ContractParty contractParty) {
        this.contractParties.add(contractParty);
        contractParty.setReview(this);
        return this;
    }

    public ContractReview removeContractParty(ContractParty contractParty) {
        this.contractParties.remove(contractParty);
        contractParty.setReview(null);
        return this;
    }

    public Set<ContractRiskPoint> getContractRiskPoints() {
        return this.contractRiskPoints;
    }

    public void setContractRiskPoints(Set<ContractRiskPoint> contractRiskPoints) {
        if (this.contractRiskPoints != null) {
            this.contractRiskPoints.forEach(i -> i.setReview(null));
        }
        if (contractRiskPoints != null) {
            contractRiskPoints.forEach(i -> i.setReview(this));
        }
        this.contractRiskPoints = contractRiskPoints;
    }

    public ContractReview contractRiskPoints(Set<ContractRiskPoint> contractRiskPoints) {
        this.setContractRiskPoints(contractRiskPoints);
        return this;
    }

    public ContractReview addContractRiskPoint(ContractRiskPoint contractRiskPoint) {
        this.contractRiskPoints.add(contractRiskPoint);
        contractRiskPoint.setReview(this);
        return this;
    }

    public ContractReview removeContractRiskPoint(ContractRiskPoint contractRiskPoint) {
        this.contractRiskPoints.remove(contractRiskPoint);
        contractRiskPoint.setReview(null);
        return this;
    }

    public Set<ContractClauseIssue> getContractClauseIssues() {
        return this.contractClauseIssues;
    }

    public void setContractClauseIssues(Set<ContractClauseIssue> contractClauseIssues) {
        if (this.contractClauseIssues != null) {
            this.contractClauseIssues.forEach(i -> i.setReview(null));
        }
        if (contractClauseIssues != null) {
            contractClauseIssues.forEach(i -> i.setReview(this));
        }
        this.contractClauseIssues = contractClauseIssues;
    }

    public ContractReview contractClauseIssues(Set<ContractClauseIssue> contractClauseIssues) {
        this.setContractClauseIssues(contractClauseIssues);
        return this;
    }

    public ContractReview addContractClauseIssue(ContractClauseIssue contractClauseIssue) {
        this.contractClauseIssues.add(contractClauseIssue);
        contractClauseIssue.setReview(this);
        return this;
    }

    public ContractReview removeContractClauseIssue(ContractClauseIssue contractClauseIssue) {
        this.contractClauseIssues.remove(contractClauseIssue);
        contractClauseIssue.setReview(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ContractReview)) {
            return false;
        }
        return getId() != null && getId().equals(((ContractReview) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ContractReview{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", contractType='" + getContractType() + "'" +
            ", contractTitle='" + getContractTitle() + "'" +
            ", contractContent='" + getContractContent() + "'" +
            ", reviewResult='" + getReviewResult() + "'" +
            ", status='" + getStatus() + "'" +
            ", overallRiskLevel='" + getOverallRiskLevel() + "'" +
            ", riskScore=" + getRiskScore() +
            ", riskSummary='" + getRiskSummary() + "'" +
            ", aiRequestId=" + getAiRequestId() +
            ", reviewStartTime='" + getReviewStartTime() + "'" +
            ", reviewEndTime='" + getReviewEndTime() + "'" +
            ", reviewDuration=" + getReviewDuration() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
