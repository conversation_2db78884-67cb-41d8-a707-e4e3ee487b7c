package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 提示词模板（PromptTemplate）实体
 * 存储提示词模板的基本信息和内容
 */
@Entity
@Table(name = "prompt_template")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 模板唯一标识键
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "template_key", length = 100, nullable = false)
    private String templateKey;

    /**
     * 模板名称
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "name", length = 200, nullable = false)
    private String name;

    /**
     * 模板描述
     */
    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 模板类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "template_type", nullable = false)
    private PromptTemplateType templateType;

    /**
     * 模板内容
     */
    @Lob
    @Column(name = "content", nullable = false, columnDefinition = "LONGTEXT")
    private String content;

    /**
     * 模板状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PromptTemplateStatus status;

    /**
     * 版本号
     */
    @NotNull
    @Column(name = "template_version", nullable = false)
    private Integer templateVersion;

    /**
     * 是否为系统默认模板
     */
    @NotNull
    @Column(name = "is_system_default", nullable = false)
    private Boolean isSystemDefault;

    /**
     * 创建者ID
     */
    @Column(name = "created_by_id")
    private Long createdById;

    /**
     * 最后修改者ID
     */
    @Column(name = "last_modified_by_id")
    private Long lastModifiedById;

    /**
     * 使用次数统计
     */
    @Column(name = "usage_count")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "promptTemplate")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "promptTemplate" }, allowSetters = true)
    private Set<PromptTemplateVariable> promptTemplateVariables = new HashSet<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "promptTemplate")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "promptTemplate" }, allowSetters = true)
    private Set<PromptTemplateVersion> promptTemplateVersions = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromptTemplate id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PromptTemplate tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getTemplateKey() {
        return this.templateKey;
    }

    public PromptTemplate templateKey(String templateKey) {
        this.setTemplateKey(templateKey);
        return this;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public String getName() {
        return this.name;
    }

    public PromptTemplate name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public PromptTemplate description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PromptTemplateType getTemplateType() {
        return this.templateType;
    }

    public PromptTemplate templateType(PromptTemplateType templateType) {
        this.setTemplateType(templateType);
        return this;
    }

    public void setTemplateType(PromptTemplateType templateType) {
        this.templateType = templateType;
    }

    public String getContent() {
        return this.content;
    }

    public PromptTemplate content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PromptTemplateStatus getStatus() {
        return this.status;
    }

    public PromptTemplate status(PromptTemplateStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Integer getTemplateVersion() {
        return this.templateVersion;
    }

    public PromptTemplate templateVersion(Integer templateVersion) {
        this.setTemplateVersion(templateVersion);
        return this;
    }

    public void setTemplateVersion(Integer templateVersion) {
        this.templateVersion = templateVersion;
    }

    public Boolean getIsSystemDefault() {
        return this.isSystemDefault;
    }

    public PromptTemplate isSystemDefault(Boolean isSystemDefault) {
        this.setIsSystemDefault(isSystemDefault);
        return this;
    }

    public void setIsSystemDefault(Boolean isSystemDefault) {
        this.isSystemDefault = isSystemDefault;
    }

    public Long getCreatedById() {
        return this.createdById;
    }

    public PromptTemplate createdById(Long createdById) {
        this.setCreatedById(createdById);
        return this;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public Long getLastModifiedById() {
        return this.lastModifiedById;
    }

    public PromptTemplate lastModifiedById(Long lastModifiedById) {
        this.setLastModifiedById(lastModifiedById);
        return this;
    }

    public void setLastModifiedById(Long lastModifiedById) {
        this.lastModifiedById = lastModifiedById;
    }

    public Long getUsageCount() {
        return this.usageCount;
    }

    public PromptTemplate usageCount(Long usageCount) {
        this.setUsageCount(usageCount);
        return this;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return this.lastUsedAt;
    }

    public PromptTemplate lastUsedAt(Instant lastUsedAt) {
        this.setLastUsedAt(lastUsedAt);
        return this;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Integer getVersion() {
        return this.version;
    }

    public PromptTemplate version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public PromptTemplate createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public PromptTemplate createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public PromptTemplate updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public PromptTemplate updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public PromptTemplate isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Set<PromptTemplateVariable> getPromptTemplateVariables() {
        return this.promptTemplateVariables;
    }

    public void setPromptTemplateVariables(Set<PromptTemplateVariable> promptTemplateVariables) {
        if (this.promptTemplateVariables != null) {
            this.promptTemplateVariables.forEach(i -> i.setPromptTemplate(null));
        }
        if (promptTemplateVariables != null) {
            promptTemplateVariables.forEach(i -> i.setPromptTemplate(this));
        }
        this.promptTemplateVariables = promptTemplateVariables;
    }

    public PromptTemplate promptTemplateVariables(Set<PromptTemplateVariable> promptTemplateVariables) {
        this.setPromptTemplateVariables(promptTemplateVariables);
        return this;
    }

    public PromptTemplate addPromptTemplateVariable(PromptTemplateVariable promptTemplateVariable) {
        this.promptTemplateVariables.add(promptTemplateVariable);
        promptTemplateVariable.setPromptTemplate(this);
        return this;
    }

    public PromptTemplate removePromptTemplateVariable(PromptTemplateVariable promptTemplateVariable) {
        this.promptTemplateVariables.remove(promptTemplateVariable);
        promptTemplateVariable.setPromptTemplate(null);
        return this;
    }

    public Set<PromptTemplateVersion> getPromptTemplateVersions() {
        return this.promptTemplateVersions;
    }

    public void setPromptTemplateVersions(Set<PromptTemplateVersion> promptTemplateVersions) {
        if (this.promptTemplateVersions != null) {
            this.promptTemplateVersions.forEach(i -> i.setPromptTemplate(null));
        }
        if (promptTemplateVersions != null) {
            promptTemplateVersions.forEach(i -> i.setPromptTemplate(this));
        }
        this.promptTemplateVersions = promptTemplateVersions;
    }

    public PromptTemplate promptTemplateVersions(Set<PromptTemplateVersion> promptTemplateVersions) {
        this.setPromptTemplateVersions(promptTemplateVersions);
        return this;
    }

    public PromptTemplate addPromptTemplateVersion(PromptTemplateVersion promptTemplateVersion) {
        this.promptTemplateVersions.add(promptTemplateVersion);
        promptTemplateVersion.setPromptTemplate(this);
        return this;
    }

    public PromptTemplate removePromptTemplateVersion(PromptTemplateVersion promptTemplateVersion) {
        this.promptTemplateVersions.remove(promptTemplateVersion);
        promptTemplateVersion.setPromptTemplate(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplate)) {
            return false;
        }
        return getId() != null && getId().equals(((PromptTemplate) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplate{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", templateKey='" + getTemplateKey() + "'" +
            ", name='" + getName() + "'" +
            ", description='" + getDescription() + "'" +
            ", templateType='" + getTemplateType() + "'" +
            ", content='" + getContent() + "'" +
            ", status='" + getStatus() + "'" +
            ", templateVersion=" + getTemplateVersion() +
            ", isSystemDefault='" + getIsSystemDefault() + "'" +
            ", createdById=" + getCreatedById() +
            ", lastModifiedById=" + getLastModifiedById() +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
