package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.util.List;

/**
 * Data Transfer Object for Legal document review responses
 * 法律文档审查响应的数据传输对象
 */
public class LegalReviewResponseDTO implements Serializable {

    private String reviewId;
    private String modelUsed;
    private String summary;
    private List<LegalIssueDTO> issues;
    private List<String> suggestions;
    private ComplianceAssessmentDTO complianceAssessment;
    private long processingTimeMs;

    public LegalReviewResponseDTO() {}

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }

    public String getModelUsed() {
        return modelUsed;
    }

    public void setModelUsed(String modelUsed) {
        this.modelUsed = modelUsed;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<LegalIssueDTO> getIssues() {
        return issues;
    }

    public void setIssues(List<LegalIssueDTO> issues) {
        this.issues = issues;
    }

    public List<String> getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(List<String> suggestions) {
        this.suggestions = suggestions;
    }

    public ComplianceAssessmentDTO getComplianceAssessment() {
        return complianceAssessment;
    }

    public void setComplianceAssessment(ComplianceAssessmentDTO complianceAssessment) {
        this.complianceAssessment = complianceAssessment;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
}
