package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * MetadataFilterDTO
 *
 * 用于封装元数据过滤条件
 */
public class MetadataFilterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 过滤字段名
     */
    private String field;

    /**
     * 过滤操作类型：eq(等于), ne(不等于), gt(大于), gte(大于等于), lt(小于), lte(小于等于), in(包含), nin(不包含)
     */
    private String operator;

    /**
     * 过滤值
     */
    private Object value;

    /**
     * 嵌套的AND条件
     */
    private List<MetadataFilterDTO> and;

    /**
     * 嵌套的OR条件
     */
    private List<MetadataFilterDTO> or;

    public MetadataFilterDTO() {}

    public MetadataFilterDTO(String field, String operator, Object value) {
        this.field = field;
        this.operator = operator;
        this.value = value;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public List<MetadataFilterDTO> getAnd() {
        return and;
    }

    public void setAnd(List<MetadataFilterDTO> and) {
        this.and = and;
    }

    public List<MetadataFilterDTO> getOr() {
        return or;
    }

    public void setOr(List<MetadataFilterDTO> or) {
        this.or = or;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MetadataFilterDTO)) return false;
        MetadataFilterDTO that = (MetadataFilterDTO) o;
        return (
            Objects.equals(field, that.field) &&
            Objects.equals(operator, that.operator) &&
            Objects.equals(value, that.value) &&
            Objects.equals(and, that.and) &&
            Objects.equals(or, that.or)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(field, operator, value, and, or);
    }

    @Override
    public String toString() {
        return (
            "MetadataFilterDTO{" +
            "field='" +
            field +
            '\'' +
            ", operator='" +
            operator +
            '\'' +
            ", value=" +
            value +
            ", and=" +
            and +
            ", or=" +
            or +
            '}'
        );
    }
}
