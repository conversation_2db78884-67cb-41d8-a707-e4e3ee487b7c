package com.whiskerguard.ai.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * DTO for ChatLaw usage information in response
 * 用于ChatLaw响应中使用信息的数据传输对象
 */
public class ChatLawUsageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    @JsonProperty("total_tokens")
    private Integer totalTokens;

    public ChatLawUsageDTO() {}

    // Getters and Setters
    public Integer getPromptTokens() {
        return promptTokens;
    }

    public void setPromptTokens(Integer promptTokens) {
        this.promptTokens = promptTokens;
    }

    public Integer getCompletionTokens() {
        return completionTokens;
    }

    public void setCompletionTokens(Integer completionTokens) {
        this.completionTokens = completionTokens;
    }

    public Integer getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Integer totalTokens) {
        this.totalTokens = totalTokens;
    }

    @Override
    public String toString() {
        return (
            "ChatLawUsageDTO{" +
            "promptTokens=" +
            promptTokens +
            ", completionTokens=" +
            completionTokens +
            ", totalTokens=" +
            totalTokens +
            '}'
        );
    }
}
