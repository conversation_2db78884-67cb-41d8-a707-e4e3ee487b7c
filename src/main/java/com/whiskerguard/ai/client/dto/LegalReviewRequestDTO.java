package com.whiskerguard.ai.client.dto;

import java.io.Serializable;

/**
 * Data Transfer Object for Legal document review requests
 * 法律文档审查请求的数据传输对象
 */
public class LegalReviewRequestDTO implements Serializable {

    private String documentContent;
    private DocumentType documentType;
    private LegalModelType preferredModel;

    public LegalReviewRequestDTO() {}

    public String getDocumentContent() {
        return documentContent;
    }

    public void setDocumentContent(String documentContent) {
        this.documentContent = documentContent;
    }

    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    public LegalModelType getPreferredModel() {
        return preferredModel;
    }

    public void setPreferredModel(LegalModelType preferredModel) {
        this.preferredModel = preferredModel;
    }

    /**
     * Types of legal documents for review
     * 待审查的法律文档类型
     */
    public enum DocumentType {
        CONTRACT("合同"),
        POLICY("政策"),
        REGULATION("规章制度"),
        AGREEMENT("协议"),
        OTHER("其他");

        private final String displayName;

        DocumentType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Types of legal models available for review
     * 可用于审查的法律模型类型
     */
    public enum LegalModelType {
        CHATLAW("ChatLaw (302.ai)"),
        LAWGPT("LaWGPT"),
        AUTO("自动选择");

        private final String displayName;

        LegalModelType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
