package com.whiskerguard.ai.client.dto;

import java.io.Serializable;

/**
 * Data Transfer Object for compliance assessment results
 * 合规性评估结果的数据传输对象
 */
public class ComplianceAssessmentDTO implements Serializable {

    private int overallScore;
    private ComplianceStatus status;
    private int criticalIssues;
    private int highPriorityIssues;
    private int mediumPriorityIssues;
    private int lowPriorityIssues;
    private String notes;

    public ComplianceAssessmentDTO() {}

    public int getOverallScore() {
        return overallScore;
    }

    public void setOverallScore(int overallScore) {
        this.overallScore = overallScore;
    }

    public ComplianceStatus getStatus() {
        return status;
    }

    public void setStatus(ComplianceStatus status) {
        this.status = status;
    }

    public int getCriticalIssues() {
        return criticalIssues;
    }

    public void setCriticalIssues(int criticalIssues) {
        this.criticalIssues = criticalIssues;
    }

    public int getHighPriorityIssues() {
        return highPriorityIssues;
    }

    public void setHighPriorityIssues(int highPriorityIssues) {
        this.highPriorityIssues = highPriorityIssues;
    }

    public int getMediumPriorityIssues() {
        return mediumPriorityIssues;
    }

    public void setMediumPriorityIssues(int mediumPriorityIssues) {
        this.mediumPriorityIssues = mediumPriorityIssues;
    }

    public int getLowPriorityIssues() {
        return lowPriorityIssues;
    }

    public void setLowPriorityIssues(int lowPriorityIssues) {
        this.lowPriorityIssues = lowPriorityIssues;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    /**
     * Compliance status levels
     * 合规状态级别
     */
    public enum ComplianceStatus {
        COMPLIANT,
        PARTIALLY_COMPLIANT,
        NON_COMPLIANT,
    }
}
