package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * RetrieveRequestDTO
 *
 * 用于封装向量检索请求参数，支持上下文管理和高级检索选项
 */
public class RetrieveRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 文本检索查询，不能为空 */
    @NotBlank
    @Size(min = 1, max = 5000)
    private String query;

    /** 返回的最相关结果数量，不能为空 */
    @NotNull
    private Integer topK;

    /** 距离度量方式（cosine/dot），不能为空 */
    @NotBlank
    private String distanceMetric;

    /** 最大上下文长度（字符数），可选 */
    private Integer maxContextLength;

    /** 上下文格式（RAW/MARKDOWN/HTML），可选，默认RAW */
    private String contextFormat;

    /** 是否合并相邻片段，可选，默认false */
    private Boolean mergeAdjacentChunks;

    /** 是否包含元数据，可选，默认false */
    private Boolean includeMetadata;

    public RetrieveRequestDTO() {}

    public RetrieveRequestDTO(String query, Integer topK, String distanceMetric) {
        this.query = query;
        this.topK = topK;
        this.distanceMetric = distanceMetric;
        this.maxContextLength = null;
        this.contextFormat = "RAW";
        this.mergeAdjacentChunks = false;
        this.includeMetadata = false;
    }

    public RetrieveRequestDTO(
        String query,
        Integer topK,
        String distanceMetric,
        Integer maxContextLength,
        String contextFormat,
        Boolean mergeAdjacentChunks,
        Boolean includeMetadata
    ) {
        this.query = query;
        this.topK = topK;
        this.distanceMetric = distanceMetric;
        this.maxContextLength = maxContextLength;
        this.contextFormat = contextFormat;
        this.mergeAdjacentChunks = mergeAdjacentChunks;
        this.includeMetadata = includeMetadata;
    }

    // ==== Getters & Setters ====

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public String getDistanceMetric() {
        return distanceMetric;
    }

    public void setDistanceMetric(String distanceMetric) {
        this.distanceMetric = distanceMetric;
    }

    public Integer getMaxContextLength() {
        return maxContextLength;
    }

    public void setMaxContextLength(Integer maxContextLength) {
        this.maxContextLength = maxContextLength;
    }

    public String getContextFormat() {
        return contextFormat != null ? contextFormat : "RAW";
    }

    public void setContextFormat(String contextFormat) {
        this.contextFormat = contextFormat;
    }

    public Boolean getMergeAdjacentChunks() {
        return mergeAdjacentChunks != null ? mergeAdjacentChunks : false;
    }

    public void setMergeAdjacentChunks(Boolean mergeAdjacentChunks) {
        this.mergeAdjacentChunks = mergeAdjacentChunks;
    }

    public Boolean getIncludeMetadata() {
        return includeMetadata != null ? includeMetadata : false;
    }

    public void setIncludeMetadata(Boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    // ==== equals, hashCode, toString ====

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RetrieveRequestDTO)) return false;
        RetrieveRequestDTO that = (RetrieveRequestDTO) o;
        return (
            Objects.equals(query, that.query) &&
            Objects.equals(topK, that.topK) &&
            Objects.equals(distanceMetric, that.distanceMetric) &&
            Objects.equals(maxContextLength, that.maxContextLength) &&
            Objects.equals(contextFormat, that.contextFormat) &&
            Objects.equals(mergeAdjacentChunks, that.mergeAdjacentChunks) &&
            Objects.equals(includeMetadata, that.includeMetadata)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(query, topK, distanceMetric, maxContextLength, contextFormat, mergeAdjacentChunks, includeMetadata);
    }

    @Override
    public String toString() {
        return (
            "RetrieveRequestDTO{" +
            "query='" +
            query +
            '\'' +
            ", topK=" +
            topK +
            ", distanceMetric='" +
            distanceMetric +
            '\'' +
            ", maxContextLength=" +
            maxContextLength +
            ", contextFormat='" +
            contextFormat +
            '\'' +
            ", mergeAdjacentChunks=" +
            mergeAdjacentChunks +
            ", includeMetadata=" +
            includeMetadata +
            "}"
        );
    }
}
