package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * IndexRequestDTO
 *
 * 单文档入库请求，包含 documentId（文档唯一标识）和 content（原始文本内容）。
 */
public class IndexRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 文档唯一标识，不能为空，最大长度 128 */
    @NotBlank
    @Size(max = 128)
    private String documentId;

    /** 文本内容，不能为空，最大长度 100000 */
    @NotBlank
    @Size(max = 100000)
    private String content;

    /** 文档来源（文件名/URL），最大长度 256 */
    @Size(max = 256)
    private String source;

    public IndexRequestDTO() {}

    public IndexRequestDTO(String documentId, String content) {
        this.documentId = documentId;
        this.content = content;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof IndexRequestDTO)) return false;
        IndexRequestDTO that = (IndexRequestDTO) o;
        return Objects.equals(documentId, that.documentId) && Objects.equals(content, that.content) && Objects.equals(source, that.source);
    }

    @Override
    public int hashCode() {
        return Objects.hash(documentId, content, source);
    }

    @Override
    public String toString() {
        return (
            "IndexRequestDTO{" +
            "documentId='" +
            documentId +
            '\'' +
            ", content='" +
            (content.length() > 50 ? content.substring(0, 50) + "...'" : content) +
            ", source='" +
            source +
            '\'' +
            '}'
        );
    }
}
