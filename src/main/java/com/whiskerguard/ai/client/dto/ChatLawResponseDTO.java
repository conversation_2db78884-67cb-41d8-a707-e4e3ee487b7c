package com.whiskerguard.ai.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;

/**
 * DTO for ChatLaw API response
 * 用于ChatLaw API响应的数据传输对象
 */
public class ChatLawResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("id")
    private String id;

    @JsonProperty("object")
    private String object;

    @JsonProperty("created")
    private Long created;

    @JsonProperty("model")
    private String model;

    @JsonProperty("choices")
    private List<ChatLawChoiceDTO> choices;

    @JsonProperty("usage")
    private ChatLawUsageDTO usage;

    public ChatLawResponseDTO() {}

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatLawChoiceDTO> getChoices() {
        return choices;
    }

    public void setChoices(List<ChatLawChoiceDTO> choices) {
        this.choices = choices;
    }

    public ChatLawUsageDTO getUsage() {
        return usage;
    }

    public void setUsage(ChatLawUsageDTO usage) {
        this.usage = usage;
    }

    @Override
    public String toString() {
        return (
            "ChatLawResponseDTO{" +
            "id='" +
            id +
            '\'' +
            ", object='" +
            object +
            '\'' +
            ", created=" +
            created +
            ", model='" +
            model +
            '\'' +
            ", choices=" +
            choices +
            ", usage=" +
            usage +
            '}'
        );
    }
}
