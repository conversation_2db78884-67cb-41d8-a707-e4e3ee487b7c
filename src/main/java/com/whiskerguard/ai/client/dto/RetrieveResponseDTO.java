package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * RetrieveResponseDTO
 *
 * 向量检索接口返回的数据结构，封装了多条匹配结果。
 * 支持元数据和格式化上下文。
 */
public class RetrieveResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检索结果列表，每项包含文本片段和相似度分数
     */
    @NotNull
    private List<Result> results;

    /**
     * 合并后的上下文，根据请求参数决定是否返回
     */
    private String mergedContext;

    /**
     * 上下文格式，RAW/MARKDOWN/HTML
     */
    private String contextFormat;

    public RetrieveResponseDTO() {}

    public RetrieveResponseDTO(List<Result> results) {
        this.results = results;
        this.mergedContext = null;
        this.contextFormat = "RAW";
    }

    public RetrieveResponseDTO(List<Result> results, String mergedContext, String contextFormat) {
        this.results = results;
        this.mergedContext = mergedContext;
        this.contextFormat = contextFormat;
    }

    public List<Result> getResults() {
        return results;
    }

    public void setResults(List<Result> results) {
        this.results = results;
    }

    public String getMergedContext() {
        return mergedContext;
    }

    public void setMergedContext(String mergedContext) {
        this.mergedContext = mergedContext;
    }

    public String getContextFormat() {
        return contextFormat;
    }

    public void setContextFormat(String contextFormat) {
        this.contextFormat = contextFormat;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RetrieveResponseDTO that = (RetrieveResponseDTO) o;
        return (
            Objects.equals(results, that.results) &&
            Objects.equals(mergedContext, that.mergedContext) &&
            Objects.equals(contextFormat, that.contextFormat)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(results, mergedContext, contextFormat);
    }

    @Override
    public String toString() {
        return (
            "RetrieveResponseDTO{" +
            "results=" +
            results +
            ", mergedContext='" +
            mergedContext +
            '\'' +
            ", contextFormat='" +
            contextFormat +
            '\'' +
            '}'
        );
    }

    /**
     * 检索结果项
     */
    public static class Result implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 文本内容
         */
        private String content;

        /**
         * 相似度分数
         */
        private Double score;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;

        /**
         * 文档ID
         */
        private String documentId;

        /**
         * 文档标题
         */
        private String title;

        /**
         * 文档来源
         */
        private String source;

        public Result() {}

        public Result(String content, Double score) {
            this.content = content;
            this.score = score;
        }

        public Result(String content, Double score, Map<String, Object> metadata) {
            this.content = content;
            this.score = score;
            this.metadata = metadata;
        }

        // Getters and Setters
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getText() {
            return content; // 兼容性方法
        }

        public void setText(String text) {
            this.content = text; // 兼容性方法
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Result result = (Result) o;
            return (
                Objects.equals(content, result.content) &&
                Objects.equals(score, result.score) &&
                Objects.equals(documentId, result.documentId)
            );
        }

        @Override
        public int hashCode() {
            return Objects.hash(content, score, documentId);
        }

        @Override
        public String toString() {
            return (
                "Result{" +
                "content='" +
                content +
                '\'' +
                ", score=" +
                score +
                ", documentId='" +
                documentId +
                '\'' +
                ", title='" +
                title +
                '\'' +
                ", source='" +
                source +
                '\'' +
                '}'
            );
        }
    }
}
