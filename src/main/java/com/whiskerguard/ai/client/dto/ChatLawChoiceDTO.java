package com.whiskerguard.ai.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * DTO for ChatLaw choice in response
 * 用于ChatLaw响应中选择项的数据传输对象
 */
public class ChatLawChoiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("index")
    private Integer index;

    @JsonProperty("message")
    private ChatLawMessageDTO message;

    @JsonProperty("finish_reason")
    private String finishReason;

    public ChatLawChoiceDTO() {}

    // Getters and Setters
    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public ChatLawMessageDTO getMessage() {
        return message;
    }

    public void setMessage(ChatLawMessageDTO message) {
        this.message = message;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }

    @Override
    public String toString() {
        return "ChatLawChoiceDTO{" + "index=" + index + ", message=" + message + ", finishReason='" + finishReason + '\'' + '}';
    }
}
