package com.whiskerguard.ai.client;

import com.whiskerguard.ai.security.SecurityUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Feign 客户端认证拦截器
 *
 * 自动将当前请求的认证信息传递给下游微服务调用
 * 支持优雅降级，当没有认证信息时不会阻断调用
 */
@Component
public class UserFeignClientInterceptor implements RequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(UserFeignClientInterceptor.class);
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER = "Bearer";

    @Override
    public void apply(RequestTemplate template) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                // 获取当前请求的Authorization头
                String token = request.getHeader(AUTHORIZATION_HEADER);

                if (token != null && !token.trim().isEmpty()) {
                    // 将Authorization头添加到Feign请求中
                    template.header(AUTHORIZATION_HEADER, token);
                    log.debug("已添加认证头到 Feign 请求: {}", template.url());
                } else {
                    log.debug("当前请求没有认证头，Feign 请求将不包含认证信息: {}", template.url());
                }
            } else {
                log.debug("无法获取当前请求上下文，Feign 请求将不包含认证信息: {}", template.url());
            }
        } catch (Exception e) {
            log.warn("处理 Feign 认证拦截时发生错误，请求将继续但不包含认证信息: {}", e.getMessage());
        }
    }
}
