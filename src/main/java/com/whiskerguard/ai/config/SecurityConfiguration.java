package com.whiskerguard.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;

@Configuration
@EnableMethodSecurity(securedEnabled = true)
public class SecurityConfiguration {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, MvcRequestMatcher.Builder mvc) throws Exception {
        //        http
        //            .csrf(AbstractHttpConfigurer::disable)
        //            .authorizeHttpRequests(authz ->
        //                // prettier-ignore
        //                authz
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/authenticate")).permitAll()
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/authenticate")).permitAll()
        //                    .requestMatchers(mvc.pattern("/api/admin/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //                    .requestMatchers(mvc.pattern("/api/**")).authenticated()
        //                    .requestMatchers(mvc.pattern("/v3/api-docs/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //                    .requestMatchers(mvc.pattern("/management/health")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/health/**")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/info")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/prometheus")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //            )
        //            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        //            .exceptionHandling(exceptions ->
        //                exceptions
        //                    .authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint())
        //                    .accessDeniedHandler(new BearerTokenAccessDeniedHandler())
        //            )
        //            .oauth2ResourceServer(oauth2 -> oauth2.jwt(withDefaults()));
        // http.authorizeHttpRequests(authz -> authz.anyRequest().permitAll());
        // return http.build();

        http.csrf(AbstractHttpConfigurer::disable).
            authorizeHttpRequests(authz -> authz.anyRequest().permitAll());
        return http.build();
    }

    @Bean
    MvcRequestMatcher.Builder mvc(HandlerMappingIntrospector introspector) {
        return new MvcRequestMatcher.Builder(introspector);
    }
}
