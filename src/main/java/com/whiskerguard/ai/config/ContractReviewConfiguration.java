/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewConfiguration.java
 * 包    名：com.whiskerguard.ai.config
 * 描    述：合同智能审查功能配置类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.config;

import java.util.concurrent.Executor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 合同智能审查功能配置类
 * <p>
 * 配置合同审查相关的参数和组件，包括：
 * 1. 审查功能开关和参数
 * 2. 异步执行器配置
 * 3. 性能优化参数
 * 4. 缓存配置
 */
@Configuration
@EnableAsync
public class ContractReviewConfiguration {

    /**
     * 合同审查配置属性
     */
    @Bean
    @ConfigurationProperties(prefix = "whiskerguard.ai.contract-review")
    public ContractReviewProperties contractReviewProperties() {
        return new ContractReviewProperties();
    }

    /**
     * 合同审查专用异步执行器
     * <p>
     * 用于并行处理企业信息检索、法规查询等耗时操作
     */
    @Bean("contractReviewExecutor")
    public Executor contractReviewExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(5);

        // 最大线程数
        executor.setMaxPoolSize(20);

        // 队列容量
        executor.setQueueCapacity(100);

        // 线程名前缀
        executor.setThreadNamePrefix("ContractReview-");

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());

        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();
        return executor;
    }

    /**
     * 合同审查配置属性类
     */
    public static class ContractReviewProperties {

        /** 是否启用合同审查功能 */
        private boolean enabled = true;

        /** 审查超时时间（毫秒） */
        private long timeout = 60000;

        /** 最大并发审查数 */
        private int maxConcurrent = 10;

        /** 是否启用深度分析 */
        private boolean enableDeepAnalysis = true;

        /** 是否启用企业背景调查 */
        private boolean enableCompanyBackground = true;

        /** 是否启用内部制度检查 */
        private boolean enableInternalPolicyCheck = true;

        /** 是否启用历史案例参考 */
        private boolean enableHistoricalCaseReference = true;

        /** 企业信息检索超时时间（毫秒） */
        private long companyInfoTimeout = 15000;

        /** 法规检索超时时间（毫秒） */
        private long regulationTimeout = 10000;

        /** RAG检索超时时间（毫秒） */
        private long ragTimeout = 8000;

        /** 最大合同内容长度 */
        private int maxContractLength = 1000000;

        /** 最小合同内容长度 */
        private int minContractLength = 10;

        /** 是否启用结果缓存 */
        private boolean enableResultCache = true;

        /** 缓存过期时间（小时） */
        private int cacheExpirationHours = 24;

        /** 是否启用审查记录持久化 */
        private boolean enablePersistence = true;

        /** 是否启用审查统计 */
        private boolean enableStatistics = true;

        /** 风险评分阈值配置 */
        private RiskThresholds riskThresholds = new RiskThresholds();

        /** 提示词配置 */
        private PromptConfig promptConfig = new PromptConfig();

        /** 性能配置 */
        private PerformanceConfig performanceConfig = new PerformanceConfig();

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public long getTimeout() {
            return timeout;
        }

        public void setTimeout(long timeout) {
            this.timeout = timeout;
        }

        public int getMaxConcurrent() {
            return maxConcurrent;
        }

        public void setMaxConcurrent(int maxConcurrent) {
            this.maxConcurrent = maxConcurrent;
        }

        public boolean isEnableDeepAnalysis() {
            return enableDeepAnalysis;
        }

        public void setEnableDeepAnalysis(boolean enableDeepAnalysis) {
            this.enableDeepAnalysis = enableDeepAnalysis;
        }

        public boolean isEnableCompanyBackground() {
            return enableCompanyBackground;
        }

        public void setEnableCompanyBackground(boolean enableCompanyBackground) {
            this.enableCompanyBackground = enableCompanyBackground;
        }

        public boolean isEnableInternalPolicyCheck() {
            return enableInternalPolicyCheck;
        }

        public void setEnableInternalPolicyCheck(boolean enableInternalPolicyCheck) {
            this.enableInternalPolicyCheck = enableInternalPolicyCheck;
        }

        public boolean isEnableHistoricalCaseReference() {
            return enableHistoricalCaseReference;
        }

        public void setEnableHistoricalCaseReference(boolean enableHistoricalCaseReference) {
            this.enableHistoricalCaseReference = enableHistoricalCaseReference;
        }

        public long getCompanyInfoTimeout() {
            return companyInfoTimeout;
        }

        public void setCompanyInfoTimeout(long companyInfoTimeout) {
            this.companyInfoTimeout = companyInfoTimeout;
        }

        public long getRegulationTimeout() {
            return regulationTimeout;
        }

        public void setRegulationTimeout(long regulationTimeout) {
            this.regulationTimeout = regulationTimeout;
        }

        public long getRagTimeout() {
            return ragTimeout;
        }

        public void setRagTimeout(long ragTimeout) {
            this.ragTimeout = ragTimeout;
        }

        public int getMaxContractLength() {
            return maxContractLength;
        }

        public void setMaxContractLength(int maxContractLength) {
            this.maxContractLength = maxContractLength;
        }

        public int getMinContractLength() {
            return minContractLength;
        }

        public void setMinContractLength(int minContractLength) {
            this.minContractLength = minContractLength;
        }

        public boolean isEnableResultCache() {
            return enableResultCache;
        }

        public void setEnableResultCache(boolean enableResultCache) {
            this.enableResultCache = enableResultCache;
        }

        public int getCacheExpirationHours() {
            return cacheExpirationHours;
        }

        public void setCacheExpirationHours(int cacheExpirationHours) {
            this.cacheExpirationHours = cacheExpirationHours;
        }

        public boolean isEnablePersistence() {
            return enablePersistence;
        }

        public void setEnablePersistence(boolean enablePersistence) {
            this.enablePersistence = enablePersistence;
        }

        public boolean isEnableStatistics() {
            return enableStatistics;
        }

        public void setEnableStatistics(boolean enableStatistics) {
            this.enableStatistics = enableStatistics;
        }

        public RiskThresholds getRiskThresholds() {
            return riskThresholds;
        }

        public void setRiskThresholds(RiskThresholds riskThresholds) {
            this.riskThresholds = riskThresholds;
        }

        public PromptConfig getPromptConfig() {
            return promptConfig;
        }

        public void setPromptConfig(PromptConfig promptConfig) {
            this.promptConfig = promptConfig;
        }

        public PerformanceConfig getPerformanceConfig() {
            return performanceConfig;
        }

        public void setPerformanceConfig(PerformanceConfig performanceConfig) {
            this.performanceConfig = performanceConfig;
        }

        /**
         * 风险阈值配置
         */
        public static class RiskThresholds {

            /** 高风险分数阈值 */
            private int highRiskThreshold = 80;

            /** 中风险分数阈值 */
            private int mediumRiskThreshold = 50;

            /** 关键风险标识阈值 */
            private int criticalRiskThreshold = 90;

            // Getters and Setters
            public int getHighRiskThreshold() {
                return highRiskThreshold;
            }

            public void setHighRiskThreshold(int highRiskThreshold) {
                this.highRiskThreshold = highRiskThreshold;
            }

            public int getMediumRiskThreshold() {
                return mediumRiskThreshold;
            }

            public void setMediumRiskThreshold(int mediumRiskThreshold) {
                this.mediumRiskThreshold = mediumRiskThreshold;
            }

            public int getCriticalRiskThreshold() {
                return criticalRiskThreshold;
            }

            public void setCriticalRiskThreshold(int criticalRiskThreshold) {
                this.criticalRiskThreshold = criticalRiskThreshold;
            }
        }

        /**
         * 提示词配置
         */
        public static class PromptConfig {

            /** 最大提示词长度 */
            private int maxPromptLength = 50000;

            /** 是否包含企业详细信息 */
            private boolean includeDetailedCompanyInfo = true;

            /** 是否包含历史案例 */
            private boolean includeHistoricalCases = true;

            /** 企业信息截断长度 */
            private int companyInfoTruncateLength = 500;

            // Getters and Setters
            public int getMaxPromptLength() {
                return maxPromptLength;
            }

            public void setMaxPromptLength(int maxPromptLength) {
                this.maxPromptLength = maxPromptLength;
            }

            public boolean isIncludeDetailedCompanyInfo() {
                return includeDetailedCompanyInfo;
            }

            public void setIncludeDetailedCompanyInfo(boolean includeDetailedCompanyInfo) {
                this.includeDetailedCompanyInfo = includeDetailedCompanyInfo;
            }

            public boolean isIncludeHistoricalCases() {
                return includeHistoricalCases;
            }

            public void setIncludeHistoricalCases(boolean includeHistoricalCases) {
                this.includeHistoricalCases = includeHistoricalCases;
            }

            public int getCompanyInfoTruncateLength() {
                return companyInfoTruncateLength;
            }

            public void setCompanyInfoTruncateLength(int companyInfoTruncateLength) {
                this.companyInfoTruncateLength = companyInfoTruncateLength;
            }
        }

        /**
         * 性能配置
         */
        public static class PerformanceConfig {

            /** 是否启用并行处理 */
            private boolean enableParallelProcessing = true;

            /** 并行任务超时时间（秒） */
            private int parallelTaskTimeoutSeconds = 30;

            /** 是否启用降级策略 */
            private boolean enableFallbackStrategy = true;

            /** 重试次数 */
            private int retryAttempts = 2;

            /** 重试间隔（毫秒） */
            private long retryIntervalMs = 1000;

            // Getters and Setters
            public boolean isEnableParallelProcessing() {
                return enableParallelProcessing;
            }

            public void setEnableParallelProcessing(boolean enableParallelProcessing) {
                this.enableParallelProcessing = enableParallelProcessing;
            }

            public int getParallelTaskTimeoutSeconds() {
                return parallelTaskTimeoutSeconds;
            }

            public void setParallelTaskTimeoutSeconds(int parallelTaskTimeoutSeconds) {
                this.parallelTaskTimeoutSeconds = parallelTaskTimeoutSeconds;
            }

            public boolean isEnableFallbackStrategy() {
                return enableFallbackStrategy;
            }

            public void setEnableFallbackStrategy(boolean enableFallbackStrategy) {
                this.enableFallbackStrategy = enableFallbackStrategy;
            }

            public int getRetryAttempts() {
                return retryAttempts;
            }

            public void setRetryAttempts(int retryAttempts) {
                this.retryAttempts = retryAttempts;
            }

            public long getRetryIntervalMs() {
                return retryIntervalMs;
            }

            public void setRetryIntervalMs(long retryIntervalMs) {
                this.retryIntervalMs = retryIntervalMs;
            }
        }
    }
}
