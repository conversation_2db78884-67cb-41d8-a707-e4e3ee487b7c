package com.whiskerguard.ai.config;

import jakarta.servlet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.server.*;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import tech.jhipster.config.JHipsterProperties;

/**
 * Configuration of web application with Servlet 3.0 APIs.
 * <p>
 * 配置 Web 应用程序，包括异步支持和 CORS 过滤器
 */
@Configuration
public class WebConfigurer implements ServletContextInitializer, WebMvcConfigurer {

    private static final Logger LOG = LoggerFactory.getLogger(WebConfigurer.class);

    private final Environment env;
    private final JHipsterProperties jHipsterProperties;
    private final AsyncTaskExecutor taskExecutor;

    public WebConfigurer(
        Environment env,
        JHipsterProperties jHipsterProperties,
        @Qualifier("mvcTaskExecutor") AsyncTaskExecutor taskExecutor
    ) {
        this.env = env;
        this.jHipsterProperties = jHipsterProperties;
        this.taskExecutor = taskExecutor;
    }

    @Override
    public void onStartup(ServletContext servletContext) {
        if (env.getActiveProfiles().length != 0) {
            LOG.info("Web application configuration, using profiles: {}", (Object[]) env.getActiveProfiles());
        }

        LOG.info("Web application fully configured");
    }

    /**
     * 配置异步支持
     * <p>
     * 使用自定义的线程池执行器替代默认的 SimpleAsyncTaskExecutor，
     * 以提高生产环境下的性能和资源利用率
     *
     * @param configurer 异步支持配置器
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        LOG.debug("配置 WebMVC 异步支持，使用自定义线程池执行器");
        configurer.setTaskExecutor(taskExecutor);
        configurer.setDefaultTimeout(30000); // 设置默认超时时间为 30 秒
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = jHipsterProperties.getCors();
        if (!CollectionUtils.isEmpty(config.getAllowedOrigins()) || !CollectionUtils.isEmpty(config.getAllowedOriginPatterns())) {
            LOG.debug("Registering CORS filter");
            source.registerCorsConfiguration("/api/**", config);
            source.registerCorsConfiguration("/management/**", config);
            source.registerCorsConfiguration("/v3/api-docs", config);
            source.registerCorsConfiguration("/swagger-ui/**", config);
        }
        return new CorsFilter(source);
    }
}
