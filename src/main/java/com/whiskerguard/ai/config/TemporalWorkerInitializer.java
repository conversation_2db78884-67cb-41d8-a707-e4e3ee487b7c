package com.whiskerguard.ai.config;

import com.whiskerguard.ai.service.workflow.AiTaskActivitiesImpl;
import com.whiskerguard.ai.service.workflow.AiWorkflowImpl;
import io.temporal.client.WorkflowClient;
import io.temporal.common.RetryOptions;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.serviceclient.WorkflowServiceStubsOptions;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 初始化 Temporal Worker
 */
@Configuration
public class TemporalWorkerInitializer {

    private static final Logger log = LoggerFactory.getLogger(TemporalWorkerInitializer.class);

    @Value("${temporal.server.host:127.0.0.1}")
    private String temporalHost;

    @Value("${temporal.server.port:7233}")
    private int temporalPort;

    @Value("${temporal.task-queue:AiTaskQueue}")
    private String taskQueue;

    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {
        // 设置系统属性，强制使用NIO而不是Epoll
        System.setProperty("io.grpc.netty.shaded.io.netty.transport.noNative", "true");

        // 使用自定义配置创建WorkflowServiceStubs，优化超时和连接设置
        return WorkflowServiceStubs.newInstance(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(temporalHost + ":" + temporalPort)
                .setRpcTimeout(java.time.Duration.ofSeconds(30)) // 增加 RPC 超时到 30 秒，避免活动报告失败
                .setRpcLongPollTimeout(java.time.Duration.ofSeconds(120)) // 增加长轮询超时到 2 分钟
                .setConnectionBackoffResetFrequency(java.time.Duration.ofSeconds(30))
                .setGrpcReconnectFrequency(java.time.Duration.ofSeconds(10))
                // 增加 gRPC 保活设置，防止连接被意外关闭
                .setEnableKeepAlive(true)
                .setKeepAliveTime(java.time.Duration.ofSeconds(30))
                .setKeepAliveTimeout(java.time.Duration.ofSeconds(5))
                // 注意：setKeepAliveWithoutCalls 在某些版本中可能不存在，已移除
                .build()
        );
    }

    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs workflowServiceStubs) {
        return WorkflowClient.newInstance(workflowServiceStubs);
    }

    @Bean
    public RetryOptions temporalRetryOptions() {
        return RetryOptions.newBuilder()
            .setInitialInterval(java.time.Duration.ofSeconds(1))
            .setMaximumInterval(java.time.Duration.ofSeconds(10))
            .setBackoffCoefficient(2.0)
            .setMaximumAttempts(3)
            .build();
    }

    @Bean
    public ApplicationRunner initTemporalWorker(ApplicationContext context, WorkflowClient workflowClient) {
        return args -> {
            try {
                WorkerFactory factory = WorkerFactory.newInstance(workflowClient);
                Worker worker = factory.newWorker(taskQueue);

                // 注册工作流和活动实现
                worker.registerWorkflowImplementationTypes(AiWorkflowImpl.class);
                worker.registerActivitiesImplementations(context.getBean(AiTaskActivitiesImpl.class));

                // 启动worker工厂
                factory.start();
                log.info("Temporal worker started successfully with queue: {}", taskQueue);
            } catch (Exception e) {
                log.error("Failed to initialize Temporal worker", e);
                // 在生产环境中，你可能需要重新抛出异常或采取其他恢复措施
                throw new RuntimeException("Failed to initialize Temporal worker", e);
            }
        };
    }
}
