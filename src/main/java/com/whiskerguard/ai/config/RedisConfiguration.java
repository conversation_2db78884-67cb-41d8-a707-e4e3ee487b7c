package com.whiskerguard.ai.config;

import java.net.URI;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.jhipster.config.JHipsterProperties;

/**
 * Redis配置类
 * 配置RedissonClient用于异步结果存储
 */
@Configuration
public class RedisConfiguration {

    /**
     * 配置RedissonClient
     * 用于存储异步任务结果
     *
     * @param jHipsterProperties JHipster配置属性
     * @return 配置好的RedissonClient实例
     */
    @Bean
    public RedissonClient redissonClient(JHipsterProperties jHipsterProperties) {
        Config config = new Config();

        // 获取Redis服务器地址
        String[] servers = jHipsterProperties.getCache().getRedis().getServer();
        URI redisUri = URI.create(servers[0]);

        // 配置单服务器模式
        SingleServerConfig singleServerConfig = config
            .useSingleServer()
            .setAddress(servers[0])
            .setConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
            .setConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize());

        // 如果有密码，设置密码
        if (redisUri.getUserInfo() != null) {
            singleServerConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
        }

        return Redisson.create(config);
    }
}
