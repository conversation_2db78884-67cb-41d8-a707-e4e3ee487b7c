package com.whiskerguard.ai.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration for AI legal models integration
 * AI法律模型集成配置
 */
@Configuration
public class LegalModelConfiguration {

    /**
     * Configure RestTemplate for AI model API calls
     * 为AI模型API调用配置RestTemplate
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        // Configure simple request factory with timeouts
        // 配置带超时的简单请求工厂
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000); // 30 seconds
        factory.setReadTimeout(60000); // 60 seconds

        return builder.requestFactory(() -> factory).build();
    }
}
