package com.whiskerguard.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Configuration for WebClient builder used by AI invokers.
 */
@Configuration
public class WebClientConfig {

    /**
     * Provides a WebClient.Builder bean for dynamic baseUrl and headers.
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }
}
