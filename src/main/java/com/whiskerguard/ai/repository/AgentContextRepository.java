package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AgentContext;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AgentContext entity.
 */
@Repository
public interface AgentContextRepository extends JpaRepository<AgentContext, Long> {
    default Optional<AgentContext> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<AgentContext> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<AgentContext> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select agentContext from AgentContext agentContext left join fetch agentContext.agentTask",
        countQuery = "select count(agentContext) from AgentContext agentContext"
    )
    Page<AgentContext> findAllWithToOneRelationships(Pageable pageable);

    @Query("select agentContext from AgentContext agentContext left join fetch agentContext.agentTask")
    List<AgentContext> findAllWithToOneRelationships();

    @Query("select agentContext from AgentContext agentContext left join fetch agentContext.agentTask where agentContext.id =:id")
    Optional<AgentContext> findOneWithToOneRelationships(@Param("id") Long id);
}
