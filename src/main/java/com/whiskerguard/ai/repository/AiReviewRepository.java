package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AiReview;
import com.whiskerguard.ai.domain.enumeration.ReviewResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AiReview entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AiReviewRepository extends JpaRepository<AiReview, Long> {
    /**
     * 根据员工ID分页查询AI审核列表
     * @param employeeId 员工ID
     * @param pageable 分页参数
     * @return 分页的AI审核列表
     */
    Page<AiReview> findByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据员工ID和审核结果分页查询AI审核列表
     * @param employeeId 员工ID
     * @param reviewResult 审核结果
     * @param pageable 分页参数
     * @return 分页的AI审核列表
     */
    Page<AiReview> findByEmployeeIdAndReviewResult(Long employeeId, ReviewResult reviewResult, Pageable pageable);

    /**
     * 根据员工ID和审核人分页查询AI审核列表
     * @param employeeId 员工ID
     * @param reviewer 审核人
     * @param pageable 分页参数
     * @return 分页的AI审核列表
     */
    Page<AiReview> findByEmployeeIdAndReviewer(Long employeeId, String reviewer, Pageable pageable);
}
