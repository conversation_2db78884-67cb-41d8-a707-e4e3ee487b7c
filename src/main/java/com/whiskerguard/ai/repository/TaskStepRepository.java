package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.TaskStep;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the TaskStep entity.
 */
@Repository
public interface TaskStepRepository extends JpaRepository<TaskStep, Long> {
    default Optional<TaskStep> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<TaskStep> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<TaskStep> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select taskStep from TaskStep taskStep left join fetch taskStep.agentTask",
        countQuery = "select count(taskStep) from TaskStep taskStep"
    )
    Page<TaskStep> findAllWithToOneRelationships(Pageable pageable);

    @Query("select taskStep from TaskStep taskStep left join fetch taskStep.agentTask")
    List<TaskStep> findAllWithToOneRelationships();

    @Query("select taskStep from TaskStep taskStep left join fetch taskStep.agentTask where taskStep.id =:id")
    Optional<TaskStep> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * 根据任务ID查找所有步骤，按步骤顺序排序
     */
    List<TaskStep> findByAgentTaskIdOrderByStepOrder(Long taskId);
}
