package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AiRequest;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AiRequest entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AiRequestRepository extends JpaRepository<AiRequest, Long> {
    /**
     * 根据员工ID分页查询AI请求列表
     * @param employeeId 员工ID
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据员工ID和请求状态分页查询AI请求列表
     * @param employeeId 员工ID
     * @param status 请求状态
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeIdAndStatus(
        Long employeeId,
        com.whiskerguard.ai.domain.enumeration.RequestStatus status,
        Pageable pageable
    );

    /**
     * 根据员工ID和工具类型分页查询AI请求列表
     * @param employeeId 员工ID
     * @param toolType 工具类型
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeIdAndToolType(Long employeeId, String toolType, Pageable pageable);

    /**
     * 根据员工ID查询AI请求列表（不分页）
     * @param employeeId 员工ID
     * @return AI请求列表
     */
    List<AiRequest> findByEmployeeIdOrderByCreatedAtDesc(Long employeeId);
}
