/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiResponseParseUtil.java
 * 包    名：com.whiskerguard.ai.util
 * 描    述：AI响应解析工具类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/17
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI响应解析工具类
 * <p>
 * 提供统一的AI响应数据解析功能，支持中英文字段名映射、
 * 安全的类型转换、复杂数据结构处理等功能。
 *
 * 主要功能：
 * 1. 中英文字段名兼容获取
 * 2. 安全的类型转换
 * 3. 复杂数据结构解析
 * 4. 错误处理和日志记录
 */
public final class AiResponseParseUtil {

    private static final Logger log = LoggerFactory.getLogger(AiResponseParseUtil.class);

    private AiResponseParseUtil() {
        // 工具类，禁止实例化
    }

    /**
     * 获取字符串值 - 支持中英文字段名
     * <p>
     * 优先使用中文字段名，如果不存在则使用英文字段名
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 字符串值，如果都不存在则返回null
     */
    public static String getStringValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数值 - 支持中英文字段名
     * <p>
     * 支持Number类型和String类型的安全转换
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 整数值，如果无法转换则返回null
     */
    public static Integer getIntegerValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析整数值: {}", value);
                return null;
            }
        }

        return null;
    }

    /**
     * 获取布尔值 - 支持中英文字段名
     * <p>
     * 支持Boolean类型和String类型的安全转换
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 布尔值，如果无法转换则返回null
     */
    public static Boolean getBooleanValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }

        return null;
    }

    /**
     * 获取对象值 - 支持中英文字段名
     * <p>
     * 通用的对象获取方法，返回原始Object类型
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 对象值，如果都不存在则返回null
     */
    public static Object getObjectValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        return value;
    }

    /**
     * 获取列表值 - 支持中英文字段名
     * <p>
     * 安全的列表类型获取，支持泛型
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @param <T> 列表元素类型
     * @return 列表值，如果不是List类型则返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> getListValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        if (value instanceof List) {
            return (List<T>) value;
        }

        return null;
    }

    /**
     * 获取Map值 - 支持中英文字段名
     * <p>
     * 安全的Map类型获取
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return Map值，如果不是Map类型则返回null
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getMapValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }

        return null;
    }

    /**
     * 获取Map列表值 - 支持中英文字段名
     * <p>
     * 安全的Map列表类型获取
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return Map列表值，如果不是List<Map>类型则返回null
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> getListMapValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return null;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        if (value instanceof List) {
            return (List<Map<String, Object>>) value;
        }

        return null;
    }

    /**
     * 安全转换对象列表为字符串列表
     * <p>
     * 将Object列表中的每个元素安全转换为字符串
     *
     * @param objectList 对象列表
     * @return 字符串列表
     */
    public static List<String> convertToStringList(List<Object> objectList) {
        if (objectList == null) {
            return new ArrayList<>();
        }

        List<String> stringList = new ArrayList<>();
        for (Object obj : objectList) {
            stringList.add(obj != null ? obj.toString() : "");
        }
        return stringList;
    }

    /**
     * 安全获取字符串列表 - 支持中英文字段名
     * <p>
     * 获取列表并安全转换为字符串列表
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 字符串列表，如果不存在则返回空列表
     */
    public static List<String> getStringListValue(Map<String, Object> map, String chineseKey, String englishKey) {
        List<Object> objectList = getListValue(map, chineseKey, englishKey);
        return convertToStringList(objectList);
    }

    /**
     * 检查值是否存在
     * <p>
     * 检查中文或英文字段名对应的值是否存在且不为null
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 如果值存在且不为null则返回true
     */
    public static boolean hasValue(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return false;
        }

        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }

        return value != null;
    }

    /**
     * 获取值的类型信息
     * <p>
     * 用于调试和日志记录
     *
     * @param map 数据Map
     * @param chineseKey 中文字段名
     * @param englishKey 英文字段名
     * @return 值的类型信息字符串
     */
    public static String getValueTypeInfo(Map<String, Object> map, String chineseKey, String englishKey) {
        if (map == null) {
            return "map is null";
        }

        Object value = map.get(chineseKey);
        String keyUsed = chineseKey;
        if (value == null) {
            value = map.get(englishKey);
            keyUsed = englishKey;
        }

        if (value == null) {
            return String.format("key '%s' and '%s' not found", chineseKey, englishKey);
        }

        return String.format("key '%s' -> %s: %s", keyUsed, value.getClass().getSimpleName(), value);
    }
}
