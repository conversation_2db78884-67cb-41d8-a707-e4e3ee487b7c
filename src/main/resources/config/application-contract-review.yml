# =============================================================================
# 合同智能审查功能配置文件
# 公司名称：中合数联（苏州）科技有限公司
# 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
# 文件名称：application-contract-review.yml
# 描    述：合同智能审查功能的专用配置
# 作    者：[yanhaishui]
# 邮    箱：<EMAIL>
# 创建日期：2025/1/20
# 版本信息：1.0
# =============================================================================

# 合同智能审查功能配置
whiskerguard:
  ai:
    contract-review:
    # 基础功能开关
    enabled: true

    # 超时配置（毫秒）
    timeout: 60000
    company-info-timeout: 15000
    regulation-timeout: 10000
    rag-timeout: 8000

    # 并发控制
    max-concurrent: 10

    # 功能开关
    enable-deep-analysis: true
    enable-company-background: true
    enable-internal-policy-check: true
    enable-historical-case-reference: true

    # 内容长度限制
    max-contract-length: 1000000
    min-contract-length: 10

    # 缓存配置
    enable-result-cache: true
    cache-expiration-hours: 24

    # 持久化配置
    enable-persistence: true
    enable-statistics: true

    # 风险阈值配置
    risk-thresholds:
      high-risk-threshold: 80
      medium-risk-threshold: 50
      critical-risk-threshold: 90

    # 提示词配置
    prompt-config:
      max-prompt-length: 50000
      include-detailed-company-info: true
      include-historical-cases: true
      company-info-truncate-length: 500

    # 性能配置
    performance-config:
      enable-parallel-processing: true
      parallel-task-timeout-seconds: 30
      enable-fallback-strategy: true
      retry-attempts: 2
      retry-interval-ms: 1000

# Feign客户端配置优化
feign:
  client:
    config:
      # 法规制度服务配置
      whiskerguardregulatoryservice:
        connect-timeout: 3000
        read-timeout: 10000
        logger-level: basic
        error-decoder: com.whiskerguard.ai.client.config.CustomErrorDecoder
        request-interceptors:
          - com.whiskerguard.ai.client.UserFeignClientInterceptor
        retryer: com.whiskerguard.ai.client.config.CustomRetryer

      # 天眼查服务配置
      whiskerguardgeneralservice:
        connect-timeout: 5000
        read-timeout: 15000
        logger-level: basic
        error-decoder: com.whiskerguard.ai.client.config.CustomErrorDecoder
        request-interceptors:
          - com.whiskerguard.ai.client.UserFeignClientInterceptor
        retryer: com.whiskerguard.ai.client.config.CustomRetryer

      # RAG检索服务配置
      whiskerguardretrievalservice:
        connect-timeout: 2000
        read-timeout: 8000
        logger-level: basic
        error-decoder: com.whiskerguard.ai.client.config.CustomErrorDecoder
        request-interceptors:
          - com.whiskerguard.ai.client.UserFeignClientInterceptor

# 线程池配置
spring:
  task:
    execution:
      pool:
        # 合同审查专用线程池
        contract-review:
          core-size: 5
          max-size: 20
          queue-capacity: 100
          keep-alive: 60s
          thread-name-prefix: 'ContractReview-'
          allow-core-thread-timeout: true
    scheduling:
      pool:
        size: 3

  # 数据库配置优化
  jpa:
    properties:
      hibernate:
        # 批量操作优化
        jdbc:
          batch_size: 50
          batch_versioned_data: true
        order_inserts: true
        order_updates: true

        # 查询优化
        default_batch_fetch_size: 16
        max_fetch_depth: 3

        # 统计信息
        generate_statistics: true

  # 连接池配置
  datasource:
    hikari:
      # 合同审查可能涉及大量数据操作，适当增加连接池大小
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

# 缓存配置
cache:
  contract-review:
    # 企业信息缓存
    company-info:
      expire-after-write: 1h
      maximum-size: 1000

    # 法规信息缓存
    regulation-info:
      expire-after-write: 6h
      maximum-size: 500

    # 审查结果缓存
    review-result:
      expire-after-write: 24h
      maximum-size: 200

# 监控和指标配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    tags:
      application: whiskerguard-ai-service
      module: contract-review
    export:
      prometheus:
        enabled: true
  health:
    contract-review:
      enabled: true

# 日志配置
logging:
  level:
    com.whiskerguard.ai.service.contract: DEBUG
    com.whiskerguard.ai.client: INFO
    com.whiskerguard.ai.web.rest.ContractReviewController: INFO
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{tenantId}] %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{tenantId}] %logger{36} - %msg%n'

# AI工具配置
ai:
  tools:
    # 合同审查工具配置
    contract-review-comprehensive:
      enabled: true
      model: 'deepseek'
      max-tokens: 4000
      temperature: 0.1
      timeout: 45000
      system-prompt: |
        你是一位资深的合同审查专家和风险评估师，具有丰富的法律知识和实务经验。
        请基于提供的信息进行全面、专业、准确的合同审查分析。
        严格按照JSON格式返回结构化的审查结果。

      # 工具特定配置
      config:
        enable-streaming: false
        enable-function-calling: false
        response-format: 'json'
        max-retries: 2
        retry-delay: 1000

# 安全配置
security:
  contract-review:
    # 权限配置
    required-authorities:
      - 'CONTRACT_REVIEW'
      - 'CONTRACT_REVIEW_HISTORY'
      - 'CONTRACT_REVIEW_DETAIL'
      - 'CONTRACT_REVIEW_EXPORT'
      - 'CONTRACT_REVIEW_STATISTICS'

    # 租户隔离配置
    tenant-isolation:
      enabled: true
      header-name: 'X-Tenant-ID'
      validate-access: true

    # 审计配置
    audit:
      enabled: true
      log-requests: true
      log-responses: false
      sensitive-fields:
        - 'contractContent'
        - 'partyName'
        - 'creditCode'
