<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="now()" dbms="mysql"/>
    <property name="floatType" value="float" dbms="mysql"/>
    <property name="clobType" value="clob" dbms="mysql"/>
    <property name="blobType" value="longblob" dbms="mysql"/>
    <property name="uuidType" value="varchar(36)" dbms="mysql"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql"/>
    <property name="timeType" value="time(6)" dbms="mysql"/>

    <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <!-- Fix AiTool checksum validation -->
    <include file="config/liquibase/changelog/20250606103000_fix_aitool_checksum.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115757_added_entity_AiTool.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115758_added_entity_AiRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115759_added_entity_AiReview.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115800_added_entity_AiToolMetrics.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250529000001_add_employee_id_to_entities.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250529_modify_ai_request_response_length.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20251221000000_add_legal_ai_models.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081135_added_entity_ContractReview.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081136_added_entity_ContractParty.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081137_added_entity_ContractRiskPoint.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081138_added_entity_ContractClauseIssue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616000000_added_prompt_template_entities.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094110_added_entity_PromptTemplate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094111_added_entity_PromptTemplateVariable.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094112_added_entity_PromptTemplateVersion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094113_added_entity_TenantPromptConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033812_added_entity_AgentTask.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033813_added_entity_TaskStep.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033814_added_entity_AgentContext.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033815_added_entity_KnowledgeCache.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033816_added_entity_AgentConfig.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
    <include file="config/liquibase/changelog/20250506115758_added_entity_constraints_AiRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115759_added_entity_constraints_AiReview.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250506115800_added_entity_constraints_AiToolMetrics.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081136_added_entity_constraints_ContractParty.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081137_added_entity_constraints_ContractRiskPoint.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250613081138_added_entity_constraints_ContractClauseIssue.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616000001_added_prompt_template_constraints.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094111_added_entity_constraints_PromptTemplateVariable.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250616094112_added_entity_constraints_PromptTemplateVersion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033813_added_entity_constraints_TaskStep.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619033814_added_entity_constraints_AgentContext.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <include file="config/liquibase/changelog/20250616200000_fix_prompt_template_content_column.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>
