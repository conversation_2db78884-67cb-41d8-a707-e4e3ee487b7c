<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ContractClauseIssue.
    -->
    <changeSet id="20250613081138-1" author="jhipster">
        <createTable tableName="contract_clause_issue" remarks="条款问题实体\n存储合同条款中发现的具体问题">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID">
                <constraints nullable="false" />
            </column>
            <column name="review_id" type="bigint" remarks="关联的审查记录ID">
                <constraints nullable="false" />
            </column>
            <column name="clause_text" type="${clobType}" remarks="条款内容">
                <constraints nullable="false" />
            </column>
            <column name="clause_number" type="varchar(32)" remarks="条款编号/位置">
                <constraints nullable="true" />
            </column>
            <column name="issue_type" type="varchar(64)" remarks="问题类型">
                <constraints nullable="true" />
            </column>
            <column name="issue_description" type="${clobType}" remarks="问题描述">
                <constraints nullable="false" />
            </column>
            <column name="severity" type="varchar(255)" remarks="严重程度">
                <constraints nullable="false" />
            </column>
            <column name="legal_risk" type="${clobType}" remarks="法律风险说明">
                <constraints nullable="true" />
            </column>
            <column name="suggestions" type="${clobType}" remarks="修改建议">
                <constraints nullable="true" />
            </column>
            <column name="reference_laws" type="${clobType}" remarks="参考法规">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="contract_clause_issue" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_clause_issue" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250613081138-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/contract_clause_issue.csv"
                  separator=";"
                  tableName="contract_clause_issue"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="review_id" type="numeric"/>
            <column name="clause_text" type="clob"/>
            <column name="clause_number" type="string"/>
            <column name="issue_type" type="string"/>
            <column name="issue_description" type="clob"/>
            <column name="severity" type="string"/>
            <column name="legal_risk" type="clob"/>
            <column name="suggestions" type="clob"/>
            <column name="reference_laws" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_at" type="date"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
