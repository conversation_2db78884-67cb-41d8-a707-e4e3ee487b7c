<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AgentTask.
    -->
    <changeSet id="20250619033812-1" author="jhipster">
        <createTable tableName="agent_task" remarks="Agent任务实体\n记录Agent执行的任务信息">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="task_type" type="varchar(255)" remarks="任务类型">
                <constraints nullable="false" />
            </column>
            <column name="title" type="varchar(200)" remarks="任务标题">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(1000)" remarks="任务描述">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="任务状态">
                <constraints nullable="false" />
            </column>
            <column name="priority" type="varchar(255)" remarks="任务优先级">
                <constraints nullable="false" />
            </column>
            <column name="request_data" type="${clobType}" remarks="请求数据">
                <constraints nullable="true" />
            </column>
            <column name="response_data" type="${clobType}" remarks="响应数据">
                <constraints nullable="true" />
            </column>
            <column name="error_message" type="varchar(2000)" remarks="错误信息">
                <constraints nullable="true" />
            </column>
            <column name="start_time" type="${datetimeType}" remarks="开始时间">
                <constraints nullable="true" />
            </column>
            <column name="end_time" type="${datetimeType}" remarks="结束时间">
                <constraints nullable="true" />
            </column>
            <column name="execution_time" type="bigint" remarks="执行时长(毫秒)">
                <constraints nullable="true" />
            </column>
            <column name="progress" type="integer" remarks="进度百分比">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="agent_task" columnName="start_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="agent_task" columnName="end_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="agent_task" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="agent_task" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619033812-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/agent_task.csv"
                  separator=";"
                  tableName="agent_task"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="task_type" type="string"/>
            <column name="title" type="string"/>
            <column name="description" type="string"/>
            <column name="status" type="string"/>
            <column name="priority" type="string"/>
            <column name="request_data" type="clob"/>
            <column name="response_data" type="clob"/>
            <column name="error_message" type="string"/>
            <column name="start_time" type="date"/>
            <column name="end_time" type="date"/>
            <column name="execution_time" type="numeric"/>
            <column name="progress" type="numeric"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
