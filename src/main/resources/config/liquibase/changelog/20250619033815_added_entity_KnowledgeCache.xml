<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity KnowledgeCache.
    -->
    <changeSet id="20250619033815-1" author="jhipster">
        <createTable tableName="knowledge_cache" remarks="知识项缓存实体\n缓存从RAG服务检索到的知识项">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="knowledge_type" type="varchar(50)" remarks="知识类型">
                <constraints nullable="false" />
            </column>
            <column name="query_key" type="varchar(200)" remarks="查询关键词">
                <constraints nullable="false" />
            </column>
            <column name="content" type="${clobType}" remarks="知识内容">
                <constraints nullable="false" />
            </column>
            <column name="similarity_score" type="double" remarks="相似度分数">
                <constraints nullable="true" />
            </column>
            <column name="source_service" type="varchar(50)" remarks="来源服务">
                <constraints nullable="true" />
            </column>
            <column name="expire_time" type="${datetimeType}" remarks="缓存过期时间">
                <constraints nullable="false" />
            </column>
            <column name="access_count" type="bigint" remarks="访问次数">
                <constraints nullable="true" />
            </column>
            <column name="last_access_time" type="${datetimeType}" remarks="最后访问时间">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="knowledge_cache" columnName="expire_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="knowledge_cache" columnName="last_access_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="knowledge_cache" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="knowledge_cache" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619033815-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/knowledge_cache.csv"
                  separator=";"
                  tableName="knowledge_cache"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="knowledge_type" type="string"/>
            <column name="query_key" type="string"/>
            <column name="content" type="clob"/>
            <column name="similarity_score" type="numeric"/>
            <column name="source_service" type="string"/>
            <column name="expire_time" type="date"/>
            <column name="access_count" type="numeric"/>
            <column name="last_access_time" type="date"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
