<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity PromptTemplateVariable.
    -->
    <changeSet id="20250616000001-1" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="prompt_template_id"
                                 baseTableName="prompt_template_variable"
                                 constraintName="fk_prompt_template_variable__prompt_template_id"
                                 referencedColumnNames="id"
                                 referencedTableName="prompt_template"/>
    </changeSet>

    <!--
        Added the constraints for entity PromptTemplateVersion.
    -->
    <changeSet id="20250616000001-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="prompt_template_id"
                                 baseTableName="prompt_template_version"
                                 constraintName="fk_prompt_template_version__prompt_template_id"
                                 referencedColumnNames="id"
                                 referencedTableName="prompt_template"/>
    </changeSet>

    <!--
        Added indexes for better query performance.
    -->
    <changeSet id="20250616000001-3" author="jhipster">
        <!-- 为 prompt_template 表添加索引 -->
        <createIndex indexName="idx_prompt_template_tenant_type" tableName="prompt_template">
            <column name="tenant_id"/>
            <column name="template_type"/>
        </createIndex>
        
        <createIndex indexName="idx_prompt_template_status" tableName="prompt_template">
            <column name="status"/>
        </createIndex>
        
        <createIndex indexName="idx_prompt_template_system_default" tableName="prompt_template">
            <column name="is_system_default"/>
        </createIndex>

        <!-- 为 prompt_template_variable 表添加索引 -->
        <createIndex indexName="idx_prompt_template_variable_template_id" tableName="prompt_template_variable">
            <column name="prompt_template_id"/>
        </createIndex>
        
        <createIndex indexName="idx_prompt_template_variable_name" tableName="prompt_template_variable">
            <column name="variable_name"/>
        </createIndex>

        <!-- 为 prompt_template_version 表添加索引 -->
        <createIndex indexName="idx_prompt_template_version_template_id" tableName="prompt_template_version">
            <column name="prompt_template_id"/>
        </createIndex>
        
        <createIndex indexName="idx_prompt_template_version_active" tableName="prompt_template_version">
            <column name="is_active"/>
        </createIndex>
        
        <createIndex indexName="idx_prompt_template_version_number" tableName="prompt_template_version">
            <column name="prompt_template_id"/>
            <column name="version_number"/>
        </createIndex>

        <!-- 为 tenant_prompt_config 表添加索引 -->
        <createIndex indexName="idx_tenant_prompt_config_tenant_key" tableName="tenant_prompt_config">
            <column name="tenant_id"/>
            <column name="config_key"/>
        </createIndex>
        
        <createIndex indexName="idx_tenant_prompt_config_type" tableName="tenant_prompt_config">
            <column name="config_type"/>
        </createIndex>
        
        <createIndex indexName="idx_tenant_prompt_config_enabled" tableName="tenant_prompt_config">
            <column name="is_enabled"/>
        </createIndex>
    </changeSet>

    <!--
        Added unique constraints.
    -->
    <changeSet id="20250616000001-4" author="jhipster">
        <!-- 确保租户+配置键的唯一性 -->
        <addUniqueConstraint 
            columnNames="tenant_id,config_key" 
            constraintName="uk_tenant_prompt_config_tenant_key" 
            tableName="tenant_prompt_config"/>
            
        <!-- 确保模板+版本号的唯一性 -->
        <addUniqueConstraint 
            columnNames="prompt_template_id,version_number" 
            constraintName="uk_prompt_template_version_template_version" 
            tableName="prompt_template_version"/>
            
        <!-- 确保模板+变量名的唯一性 -->
        <addUniqueConstraint 
            columnNames="prompt_template_id,variable_name" 
            constraintName="uk_prompt_template_variable_template_name" 
            tableName="prompt_template_variable"/>
    </changeSet>
</databaseChangeLog>
