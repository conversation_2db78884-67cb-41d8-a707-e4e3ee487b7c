<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AiReview.
    -->
    <changeSet id="20250506115759-1" author="jhipster">
        <createTable tableName="ai_review" remarks="AI审核（AiReview）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="employee_id" type="bigint" remarks="职工（用户）ID">
                <constraints nullable="false" />
            </column>
            <column name="review_content" type="varchar(255)" remarks="审核内容">
                <constraints nullable="false" />
            </column>
            <column name="review_result" type="varchar(255)" remarks="审核结果">
                <constraints nullable="false" />
            </column>
            <column name="review_date" type="${datetimeType}" remarks="审核日期">
                <constraints nullable="false" />
            </column>
            <column name="reviewer" type="varchar(64)" remarks="审核人">
                <constraints nullable="false" />
            </column>
            <column name="comments" type="varchar(255)" remarks="审核意见">
                <constraints nullable="true" />
            </column>
            <column name="feedback" type="varchar(255)" remarks="反馈数据">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="request_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="ai_review" columnName="review_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="ai_review" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="ai_review" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250506115759-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/ai_review.csv"
                  separator=";"
                  tableName="ai_review"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="employee_id" type="numeric"/>
            <column name="review_content" type="string"/>
            <column name="review_result" type="string"/>
            <column name="review_date" type="date"/>
            <column name="reviewer" type="string"/>
            <column name="comments" type="string"/>
            <column name="feedback" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
