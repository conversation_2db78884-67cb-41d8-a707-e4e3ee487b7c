<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PromptTemplateVariable.
    -->
    <changeSet id="20250616094111-1" author="jhipster">
        <createTable tableName="prompt_template_variable" remarks="提示词模板变量（PromptTemplateVariable）实体\n定义模板中使用的变量信息">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="variable_name" type="varchar(100)" remarks="变量名称">
                <constraints nullable="false" />
            </column>
            <column name="display_name" type="varchar(200)" remarks="变量显示名称">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(500)" remarks="变量描述">
                <constraints nullable="true" />
            </column>
            <column name="variable_type" type="varchar(255)" remarks="变量类型">
                <constraints nullable="false" />
            </column>
            <column name="default_value" type="${clobType}" remarks="默认值">
                <constraints nullable="true" />
            </column>
            <column name="is_required" type="boolean" remarks="是否必填">
                <constraints nullable="false" />
            </column>
            <column name="validation_rule" type="varchar(500)" remarks="变量验证规则">
                <constraints nullable="true" />
            </column>
            <column name="example_value" type="${clobType}" remarks="变量示例值">
                <constraints nullable="true" />
            </column>
            <column name="sort_order" type="integer" remarks="排序顺序">
                <constraints nullable="true" />
            </column>
            <column name="is_enabled" type="boolean" remarks="是否启用">
                <constraints nullable="false" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="prompt_template_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template_variable" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_variable" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250616094111-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/prompt_template_variable.csv"
                  separator=";"
                  tableName="prompt_template_variable"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="variable_name" type="string"/>
            <column name="display_name" type="string"/>
            <column name="description" type="string"/>
            <column name="variable_type" type="string"/>
            <column name="default_value" type="clob"/>
            <column name="is_required" type="boolean"/>
            <column name="validation_rule" type="string"/>
            <column name="example_value" type="clob"/>
            <column name="sort_order" type="numeric"/>
            <column name="is_enabled" type="boolean"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
