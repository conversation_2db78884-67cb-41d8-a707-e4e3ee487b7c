<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ContractRiskPoint.
    -->
    <changeSet id="20250613081137-1" author="jhipster">
        <createTable tableName="contract_risk_point" remarks="风险点实体\n存储合同审查中识别的具体风险点">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID">
                <constraints nullable="false" />
            </column>
            <column name="review_id" type="bigint" remarks="关联的审查记录ID">
                <constraints nullable="false" />
            </column>
            <column name="risk_category" type="varchar(255)" remarks="风险类别">
                <constraints nullable="false" />
            </column>
            <column name="risk_description" type="${clobType}" remarks="风险描述">
                <constraints nullable="false" />
            </column>
            <column name="severity" type="varchar(255)" remarks="风险等级">
                <constraints nullable="false" />
            </column>
            <column name="affected_clauses" type="${clobType}" remarks="涉及的条款">
                <constraints nullable="true" />
            </column>
            <column name="legal_basis" type="${clobType}" remarks="法律依据">
                <constraints nullable="true" />
            </column>
            <column name="suggestions" type="${clobType}" remarks="修改建议">
                <constraints nullable="true" />
            </column>
            <column name="risk_score" type="integer" remarks="风险分数 (0-100)">
                <constraints nullable="true" />
            </column>
            <column name="is_critical" type="boolean" remarks="是否为关键风险">
                <constraints nullable="true" />
            </column>
            <column name="risk_source" type="varchar(128)" remarks="风险来源">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="contract_risk_point" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_risk_point" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250613081137-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/contract_risk_point.csv"
                  separator=";"
                  tableName="contract_risk_point"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="review_id" type="numeric"/>
            <column name="risk_category" type="string"/>
            <column name="risk_description" type="clob"/>
            <column name="severity" type="string"/>
            <column name="affected_clauses" type="clob"/>
            <column name="legal_basis" type="clob"/>
            <column name="suggestions" type="clob"/>
            <column name="risk_score" type="numeric"/>
            <column name="is_critical" type="boolean"/>
            <column name="risk_source" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_at" type="date"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
