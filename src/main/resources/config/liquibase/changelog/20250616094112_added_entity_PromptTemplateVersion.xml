<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PromptTemplateVersion.
    -->
    <changeSet id="20250616094112-1" author="jhipster">
        <createTable tableName="prompt_template_version" remarks="提示词模板版本（PromptTemplateVersion）实体\n管理模板的版本历史">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="version_number" type="integer" remarks="版本号">
                <constraints nullable="false" />
            </column>
            <column name="version_name" type="varchar(200)" remarks="版本名称">
                <constraints nullable="true" />
            </column>
            <column name="description" type="varchar(1000)" remarks="版本描述">
                <constraints nullable="true" />
            </column>
            <column name="content" type="${clobType}" remarks="模板内容">
                <constraints nullable="false" />
            </column>
            <column name="variables_definition" type="${clobType}" remarks="变量定义JSON">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="版本状态">
                <constraints nullable="false" />
            </column>
            <column name="is_active" type="boolean" remarks="是否为当前活跃版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by_id" type="bigint" remarks="创建者ID">
                <constraints nullable="true" />
            </column>
            <column name="change_log" type="varchar(1000)" remarks="版本变更说明">
                <constraints nullable="true" />
            </column>
            <column name="usage_count" type="bigint" remarks="使用次数统计">
                <constraints nullable="true" />
            </column>
            <column name="last_used_at" type="${datetimeType}" remarks="最后使用时间">
                <constraints nullable="true" />
            </column>
            <column name="performance_score" type="double" remarks="性能评分">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="prompt_template_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template_version" columnName="last_used_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_version" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_version" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250616094112-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/prompt_template_version.csv"
                  separator=";"
                  tableName="prompt_template_version"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="version_number" type="numeric"/>
            <column name="version_name" type="string"/>
            <column name="description" type="string"/>
            <column name="content" type="clob"/>
            <column name="variables_definition" type="clob"/>
            <column name="status" type="string"/>
            <column name="is_active" type="boolean"/>
            <column name="created_by_id" type="numeric"/>
            <column name="change_log" type="string"/>
            <column name="usage_count" type="numeric"/>
            <column name="last_used_at" type="date"/>
            <column name="performance_score" type="numeric"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
