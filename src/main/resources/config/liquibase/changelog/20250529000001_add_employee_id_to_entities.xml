<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        为 AiRequest 和 AiReview 实体添加 employee_id 字段
        这是为了关联 org-service 的 Employee 实体
    -->
    
    <!-- 为 ai_request 表添加 employee_id 字段 -->
    <changeSet id="20250529000001-1" author="jhipster">
        <!-- 检查字段是否已存在，如果不存在则添加 -->
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="ai_request" columnName="employee_id"/>
            </not>
        </preConditions>

        <comment>为 ai_request 表添加 employee_id 字段</comment>

        <addColumn tableName="ai_request">
            <column name="employee_id" type="bigint" remarks="职工（用户）ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="ai_request" columnName="employee_id"/>
        </rollback>
    </changeSet>

    <!-- 为 ai_review 表添加 employee_id 字段 -->
    <changeSet id="20250529000001-2" author="jhipster">
        <!-- 检查字段是否已存在，如果不存在则添加 -->
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="ai_review" columnName="employee_id"/>
            </not>
        </preConditions>

        <comment>为 ai_review 表添加 employee_id 字段</comment>

        <addColumn tableName="ai_review">
            <column name="employee_id" type="bigint" remarks="职工（用户）ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <rollback>
            <dropColumn tableName="ai_review" columnName="employee_id"/>
        </rollback>
    </changeSet>

    <!-- 为现有数据设置默认的 employee_id 值 -->
    <changeSet id="20250529000001-3" author="jhipster">
        <comment>为现有数据设置默认的 employee_id 值</comment>
        
        <sql>
            -- 为现有的 ai_request 记录设置默认 employee_id
            UPDATE ai_request SET employee_id = 1 WHERE employee_id IS NULL;
            
            -- 为现有的 ai_review 记录设置默认 employee_id  
            UPDATE ai_review SET employee_id = 1 WHERE employee_id IS NULL;
        </sql>
        
        <rollback>
            <comment>此变更集不需要回滚，因为它只是设置默认值</comment>
        </rollback>
    </changeSet>

</databaseChangeLog>
