<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        修改 AiRequest 表的字段长度以支持更长的 AI 响应内容
    -->
    <changeSet id="20250529-modify-ai-request-response-length" author="yanh<PERSON>hui">
        <comment>修改 AI 请求表的响应字段长度，支持更长的 AI 响应内容</comment>
        
        <!-- 修改 response 字段长度从 varchar(255) 到 TEXT -->
        <modifyDataType tableName="ai_request" columnName="response" newDataType="TEXT"/>
        
        <!-- 修改 prompt 字段长度从 varchar(255) 到 TEXT，支持更长的提示词 -->
        <modifyDataType tableName="ai_request" columnName="prompt" newDataType="TEXT"/>
        
        <!-- 修改 error_message 字段长度从 varchar(255) 到 TEXT，支持更详细的错误信息 -->
        <modifyDataType tableName="ai_request" columnName="error_message" newDataType="TEXT"/>
        
        <!-- 修改 metadata 字段长度从 varchar(255) 到 TEXT，支持更复杂的元数据 -->
        <modifyDataType tableName="ai_request" columnName="metadata" newDataType="TEXT"/>
        
        <rollback>
            <!-- 回滚时恢复原始长度（注意：可能会导致数据截断） -->
            <modifyDataType tableName="ai_request" columnName="response" newDataType="varchar(255)"/>
            <modifyDataType tableName="ai_request" columnName="prompt" newDataType="varchar(255)"/>
            <modifyDataType tableName="ai_request" columnName="error_message" newDataType="varchar(255)"/>
            <modifyDataType tableName="ai_request" columnName="metadata" newDataType="varchar(255)"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
