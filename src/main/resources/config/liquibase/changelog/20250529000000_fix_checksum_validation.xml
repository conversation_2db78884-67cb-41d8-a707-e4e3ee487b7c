<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        修复 JDL 更新后的校验和验证问题
        这个变更集将清除有问题的校验和记录，允许 Liquibase 重新计算校验和
    -->
    <changeSet id="20250529000000-1" author="jhipster" runOnChange="true">
        <comment>清除有问题的校验和记录以解决 JDL 更新后的验证失败</comment>
        
        <!-- 删除有问题的校验和记录 -->
        <sql>
            DELETE FROM DATABASECHANGELOG 
            WHERE ID IN (
                '20250506115758-1',
                '20250506115758-1-data',
                '20250506115759-1', 
                '20250506115759-1-data'
            ) AND AUTHOR = 'jhipster';
        </sql>
        
        <rollback>
            <comment>此变更集不需要回滚，因为它只是清理校验和记录</comment>
        </rollback>
    </changeSet>

</databaseChangeLog>
