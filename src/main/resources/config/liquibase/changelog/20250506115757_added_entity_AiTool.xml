<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AiTool.
    -->
    <changeSet id="20250506115757-1" author="jhipster">
        <createTable tableName="ai_tool" remarks="AI工具（AiTool）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="name" type="varchar(32)" remarks="工具名称">
                <constraints nullable="false" />
            </column>
            <column name="tool_key" type="varchar(32)" remarks="工具关键词">
                <constraints nullable="false" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="api_url" type="varchar(256)" remarks="API 地址">
                <constraints nullable="false" />
            </column>
            <column name="api_key" type="varchar(256)" remarks="API 密钥">
                <constraints nullable="false" />
            </column>
            <column name="auth_type" type="varchar(32)" remarks="鉴权类型">
                <constraints nullable="true" />
            </column>
            <column name="path" type="varchar(128)" remarks="接口路径">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="工具状态">
                <constraints nullable="false" />
            </column>
            <column name="weight" type="integer" remarks="路由权重">
                <constraints nullable="false" />
            </column>
            <column name="max_concurrent_calls" type="integer" remarks="并发许可数">
                <constraints nullable="false" />
            </column>
            <column name="is_model" type="boolean" remarks="是否为模型类型">
                <constraints nullable="true" />
            </column>
            <column name="model_category" type="varchar(32)" remarks="模型分类">
                <constraints nullable="true" />
            </column>
            <column name="model_provider" type="varchar(64)" remarks="模型提供商">
                <constraints nullable="true" />
            </column>
            <column name="remark" type="varchar(255)" remarks="备注信息">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="ai_tool" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="ai_tool" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250506115757-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/ai_tool.csv"
                  separator=";"
                  tableName="ai_tool"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="name" type="string"/>
            <column name="tool_key" type="string"/>
            <column name="version" type="numeric"/>
            <column name="api_url" type="string"/>
            <column name="api_key" type="string"/>
            <column name="auth_type" type="string"/>
            <column name="path" type="string"/>
            <column name="status" type="string"/>
            <column name="weight" type="numeric"/>
            <column name="max_concurrent_calls" type="numeric"/>
            <column name="is_model" type="boolean"/>
            <column name="model_category" type="string"/>
            <column name="model_provider" type="string"/>
            <column name="remark" type="string"/>
            <column name="metadata" type="string"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
