<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TaskStep.
    -->
    <changeSet id="20250619033813-1" author="jhipster">
        <createTable tableName="task_step" remarks="任务步骤实体\n记录Agent任务的执行步骤">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="step_name" type="varchar(100)" remarks="步骤名称">
                <constraints nullable="false" />
            </column>
            <column name="step_description" type="varchar(500)" remarks="步骤描述">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="步骤状态">
                <constraints nullable="false" />
            </column>
            <column name="step_order" type="integer" remarks="步骤顺序">
                <constraints nullable="false" />
            </column>
            <column name="input_data" type="${clobType}" remarks="输入数据">
                <constraints nullable="true" />
            </column>
            <column name="output_data" type="${clobType}" remarks="输出数据">
                <constraints nullable="true" />
            </column>
            <column name="error_message" type="varchar(1000)" remarks="错误信息">
                <constraints nullable="true" />
            </column>
            <column name="start_time" type="${datetimeType}" remarks="开始时间">
                <constraints nullable="true" />
            </column>
            <column name="end_time" type="${datetimeType}" remarks="结束时间">
                <constraints nullable="true" />
            </column>
            <column name="execution_time" type="bigint" remarks="执行时长(毫秒)">
                <constraints nullable="true" />
            </column>
            <column name="retry_count" type="integer" remarks="重试次数">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="agent_task_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="task_step" columnName="start_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="task_step" columnName="end_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="task_step" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="task_step" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619033813-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/task_step.csv"
                  separator=";"
                  tableName="task_step"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="step_name" type="string"/>
            <column name="step_description" type="string"/>
            <column name="status" type="string"/>
            <column name="step_order" type="numeric"/>
            <column name="input_data" type="clob"/>
            <column name="output_data" type="clob"/>
            <column name="error_message" type="string"/>
            <column name="start_time" type="date"/>
            <column name="end_time" type="date"/>
            <column name="execution_time" type="numeric"/>
            <column name="retry_count" type="numeric"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <column name="agent_task_id" type="numeric"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
