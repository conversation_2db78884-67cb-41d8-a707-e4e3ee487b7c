<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PromptTemplate.
    -->
    <changeSet id="20250616000000-1" author="jhipster">
        <createTable tableName="prompt_template">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="template_key" type="varchar(100)">
                <constraints nullable="false" unique="true" />
            </column>
            <column name="name" type="varchar(200)">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="template_type" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="content" type="${clobType}">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="version" type="integer">
                <constraints nullable="false" />
            </column>
            <column name="is_system_default" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="created_by_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="last_modified_by_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="usage_count" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="last_used_at" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="${datetimeType}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="${datetimeType}"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template" columnName="last_used_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template" columnName="created_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template" columnName="last_modified_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!--
        Added the entity PromptTemplateVariable.
    -->
    <changeSet id="20250616000000-2" author="jhipster">
        <createTable tableName="prompt_template_variable">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="variable_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="display_name" type="varchar(200)">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="variable_type" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="default_value" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="is_required" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="validation_rule" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="example_value" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="sort_order" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="is_enabled" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="prompt_template_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="${datetimeType}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="${datetimeType}"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template_variable" columnName="created_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_variable" columnName="last_modified_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!--
        Added the entity PromptTemplateVersion.
    -->
    <changeSet id="20250616000000-3" author="jhipster">
        <createTable tableName="prompt_template_version">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="version_number" type="integer">
                <constraints nullable="false" />
            </column>
            <column name="version_name" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="description" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="content" type="${clobType}">
                <constraints nullable="false" />
            </column>
            <column name="variables_definition" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="is_active" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="created_by_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="change_log" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="usage_count" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="last_used_at" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="performance_score" type="decimal(3,2)">
                <constraints nullable="true" />
            </column>
            <column name="prompt_template_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="${datetimeType}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="${datetimeType}"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template_version" columnName="last_used_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_version" columnName="created_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template_version" columnName="last_modified_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!--
        Added the entity TenantPromptConfig.
    -->
    <changeSet id="20250616000000-4" author="jhipster">
        <createTable tableName="tenant_prompt_config">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="config_key" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="config_value" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="config_type" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="is_enabled" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="priority" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="effective_from" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="effective_to" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="created_by_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="last_modified_by_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="${datetimeType}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="${datetimeType}"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="tenant_prompt_config" columnName="effective_from" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="tenant_prompt_config" columnName="effective_to" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="tenant_prompt_config" columnName="created_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="tenant_prompt_config" columnName="last_modified_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250616000000-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/prompt_template.csv"
                  separator=";"
                  tableName="prompt_template"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="template_key" type="string"/>
            <column name="name" type="string"/>
            <column name="description" type="string"/>
            <column name="template_type" type="string"/>
            <column name="content" type="clob"/>
            <column name="status" type="string"/>
            <column name="version" type="numeric"/>
            <column name="is_system_default" type="boolean"/>
            <column name="tenant_id" type="numeric"/>
            <column name="created_by_id" type="numeric"/>
            <column name="last_modified_by_id" type="numeric"/>
            <column name="usage_count" type="numeric"/>
            <column name="last_used_at" type="date"/>
            <column name="created_by" type="string"/>
            <column name="created_date" type="date"/>
            <column name="last_modified_by" type="string"/>
            <column name="last_modified_date" type="date"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
