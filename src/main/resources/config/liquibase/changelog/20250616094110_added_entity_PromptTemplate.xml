<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PromptTemplate.
    -->
    <changeSet id="20250616094110-1" author="jhipster">
        <createTable tableName="prompt_template" remarks="提示词模板（PromptTemplate）实体\n存储提示词模板的基本信息和内容">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="true" />
            </column>
            <column name="template_key" type="varchar(100)" remarks="模板唯一标识键">
                <constraints nullable="false" />
            </column>
            <column name="name" type="varchar(200)" remarks="模板名称">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(1000)" remarks="模板描述">
                <constraints nullable="true" />
            </column>
            <column name="template_type" type="varchar(255)" remarks="模板类型">
                <constraints nullable="false" />
            </column>
            <column name="content" type="${clobType}" remarks="模板内容">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)" remarks="模板状态">
                <constraints nullable="false" />
            </column>
            <column name="template_version" type="integer" remarks="版本号">
                <constraints nullable="false" />
            </column>
            <column name="is_system_default" type="boolean" remarks="是否为系统默认模板">
                <constraints nullable="false" />
            </column>
            <column name="created_by_id" type="bigint" remarks="创建者ID">
                <constraints nullable="true" />
            </column>
            <column name="last_modified_by_id" type="bigint" remarks="最后修改者ID">
                <constraints nullable="true" />
            </column>
            <column name="usage_count" type="bigint" remarks="使用次数统计">
                <constraints nullable="true" />
            </column>
            <column name="last_used_at" type="${datetimeType}" remarks="最后使用时间">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="prompt_template" columnName="last_used_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="prompt_template" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250616094110-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/prompt_template.csv"
                  separator=";"
                  tableName="prompt_template"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="template_key" type="string"/>
            <column name="name" type="string"/>
            <column name="description" type="string"/>
            <column name="template_type" type="string"/>
            <column name="content" type="clob"/>
            <column name="status" type="string"/>
            <column name="template_version" type="numeric"/>
            <column name="is_system_default" type="boolean"/>
            <column name="created_by_id" type="numeric"/>
            <column name="last_modified_by_id" type="numeric"/>
            <column name="usage_count" type="numeric"/>
            <column name="last_used_at" type="date"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
