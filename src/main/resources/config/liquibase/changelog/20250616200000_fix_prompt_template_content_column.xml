<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Fix prompt_template content column type to support large text content
        修复 prompt_template 表的 content 字段类型以支持大文本内容
    -->
    <changeSet id="20250616200000-1" author="system">
        <comment>Fix prompt_template content column to support large text</comment>
        
        <!-- 修改 content 字段类型为 LONGTEXT -->
        <modifyDataType 
            tableName="prompt_template" 
            columnName="content" 
            newDataType="LONGTEXT"/>
            
        <!-- 添加备注 -->
        <setColumnRemarks
            tableName="prompt_template"
            columnName="content"
            columnDataType="LONGTEXT"
            remarks="模板内容 - 支持大文本"/>
    </changeSet>

</databaseChangeLog>
