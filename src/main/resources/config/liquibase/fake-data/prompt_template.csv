id;tenant_id;template_key;name;description;template_type;content;status;template_version;is_system_default;created_by_id;last_modified_by_id;usage_count;last_used_at;version;created_by;created_at;updated_by;updated_at;is_deleted
1;CONTRACT_COMPREHENSIVE_REVIEW;合同综合审查模板;用于合同全面审查的标准模板，包含法律合规、风险评估、条款分析等功能;CONTRACT_COMPREHENSIVE_REVIEW;# 合同智能审查任务

请对以下合同进行全面的智能审查分析：

## 合同基本信息
- 合同类型：{{CONTRACT_TYPE}}
- 合同内容：{{CONTRACT_CONTENT}}

## 企业信息
{{COMPANY_INFO}}

## 审查要求
请从以下维度进行分析：
1. **法律合规性分析** - 检查合同条款是否符合相关法律法规
2. **风险识别与评估** - 识别潜在的法律风险和商业风险
3. **条款完整性检查** - 评估合同条款的完整性和合理性
4. **财务条款审查** - 分析付款条件、违约责任等财务相关条款

## 审查重点
{{REVIEW_FOCUS}}

## 输出要求
请严格按照JSON格式返回结果，包含：
- 整体风险等级评估
- 具体风险点列表
- 条款问题分析
- 修改建议
- 法律依据说明;PUBLISHED;1;true;;1;1;0;2024-06-16 10:00:00;1;system;2024-06-16 10:00:00;system;2024-06-16 10:00:00;false
2;POLICY_REGULATORY_CONVERSION;法规转制度模板;将法规条文转换为企业内部管理制度的模板;POLICY_REGULATORY_CONVERSION;# 法规到内部管理制度转换任务

请根据以下法规内容，为企业制定相应的内部管理制度：

## 法规内容
{{LEGAL_TEXT}}

## 企业信息
- 企业名称：{{COMPANY_NAME}}
- 行业类型：{{INDUSTRY}}
- 企业规模：{{COMPANY_SIZE}}

## 转换要求
1. **结合企业实际情况** - 根据企业规模和行业特点调整制度内容
2. **确保制度的可操作性** - 制度条款应具体明确，便于执行
3. **体现法规的核心要求** - 确保制度符合法规的基本要求
4. **考虑企业的管理特点** - 结合企业现有管理体系

## 输出要求
请生成完整的内部管理制度文档，包含制度目的、适用范围、具体条款、执行程序等。;PUBLISHED;1;true;;1;1;0;2024-06-16 10:00:00;1;system;2024-06-16 10:00:00;system;2024-06-16 10:00:00;false
3;POLICY_INTERNAL_REVIEW;内部制度审查模板;用于审查企业内部制度的合规性和完整性;POLICY_INTERNAL_REVIEW;# 内部制度智能审查任务

请对以下内部制度进行全面的智能审查分析：

## 制度基本信息
- 制度类型：{{POLICY_TYPE}}
- 制定部门：{{DEPARTMENT}}

## 制度内容
{{POLICY_CONTENT}}

## 审查维度
1. **合规性检查** - 检查制度是否符合相关法律法规
2. **可操作性分析** - 评估制度条款的可执行性
3. **风险识别** - 识别制度执行中的潜在风险
4. **完整性评估** - 评估制度内容的完整性和逻辑性

## 输出要求
请提供详细的审查报告和改进建议，确保制度的合规性和有效性。;PUBLISHED;1;true;;1;1;0;2024-06-16 10:00:00;1;system;2024-06-16 10:00:00;system;2024-06-16 10:00:00;false
1;19421;pfft 稍微;up;ouch 举 没;GENERAL_GENERATION;../fake-data/blob/hipster.txt;DISABLED;15976;false;4679;31454;18914;2025-06-16T04:43:35;3797;坚固;2025-06-16T00:43:19;坚固 播 um;2025-06-15T13:58:21;true
2;18860;整 拆;until;huzzah given 都;GENERAL_ANALYSIS;../fake-data/blob/hipster.txt;DRAFT;9889;true;28021;25336;15921;2025-06-15T15:35:36;13725;disadvantage 跑;2025-06-16T06:29:54;撬 抚;2025-06-15T18:36:53;false
3;12345;ice-cream 捣;or;驾驶 没有 极;CONTRACT_LEGAL_COMPLIANCE;../fake-data/blob/hipster.txt;DRAFT;13891;true;11306;10995;3251;2025-06-15T20:08:23;17706;furthermore request 平坦;2025-06-15T12:25:01;仿佛;2025-06-15T11:29:53;false
4;1312;拎 打 整齐;cafe 滑;平坦 supposing 何尝;CONTRACT_RISK_ASSESSMENT;../fake-data/blob/hipster.txt;PUBLISHED;5284;true;22470;17037;374;2025-06-15T18:23:54;17735;yowza 搔;2025-06-15T12:32:05;小 热 劈;2025-06-15T09:44:08;true
5;18674;蠕动 apud;contractor;though amongst 多么;CONTRACT_RISK_ASSESSMENT;../fake-data/blob/hipster.txt;DISABLED;17403;false;12289;24568;28134;2025-06-16T03:19:10;23861;按 全 毅然;2025-06-15T11:45:25;sans 推;2025-06-15T23:26:28;false
6;29697;gadzooks;轻松 摸;settler 苦 许多;CONTRACT_RISK_ASSESSMENT;../fake-data/blob/hipster.txt;DRAFT;15100;false;4310;24122;16840;2025-06-15T13:39:08;27564;拍;2025-06-15T16:07:31;graffiti;2025-06-16T00:15:14;true
7;20510;画;cricket 绿;scenario 整;POLICY_REGULATORY_CONVERSION;../fake-data/blob/hipster.txt;DISABLED;4400;false;9786;15116;2159;2025-06-16T05:42:45;18379;gadzooks;2025-06-16T07:45:57;攥 整 生动;2025-06-15T17:40:24;false
8;26084;about;一概;幸亏 相当 咀;CONTRACT_COMPREHENSIVE_REVIEW;../fake-data/blob/hipster.txt;PUBLISHED;2627;true;13537;20000;8693;2025-06-15T14:48:20;20289;cow 大方 果然;2025-06-15T16:38:18;passport whether aside;2025-06-15T19:33:18;true
9;22372;unless ghost 清楚;round;beside 转动 aha;GENERAL_ANALYSIS;../fake-data/blob/hipster.txt;DISABLED;10430;true;11;29638;30163;2025-06-15T22:03:53;5535;撬 一道;2025-06-15T15:04:15;provided around;2025-06-15T13:11:14;false
10;20138;摇 多;which 少;软 porter;GENERAL_ANALYSIS;../fake-data/blob/hipster.txt;TESTING;29526;true;19121;8217;641;2025-06-16T07:15:28;5267;举 轻松;2025-06-16T03:55:55;beneath geez 劈;2025-06-16T00:50:38;true
