# ===================================================================
# Spring Cloud Consul Config bootstrap configuration for the "prod" profile
# ===================================================================

spring:
  cloud:
    consul:
      config:
        fail-fast: true
        format: yaml # set this to "files" if using git2consul
        profile-separator: '-'
      host: dev.consule.mbbhg.com
      port: 8500
      retry:
        initial-interval: 1000
        max-interval: 2000
        max-attempts: 100
