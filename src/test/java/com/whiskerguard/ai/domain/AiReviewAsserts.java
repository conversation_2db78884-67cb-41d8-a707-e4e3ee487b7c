package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AiReviewAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiReviewAllPropertiesEquals(AiReview expected, AiReview actual) {
        assertAiReviewAutoGeneratedPropertiesEquals(expected, actual);
        assertAiReviewAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiReviewAllUpdatablePropertiesEquals(AiReview expected, AiReview actual) {
        assertAiReviewUpdatableFieldsEquals(expected, actual);
        assertAiReviewUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiReviewAutoGeneratedPropertiesEquals(AiReview expected, AiReview actual) {
        assertThat(actual)
            .as("Verify AiReview auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiReviewUpdatableFieldsEquals(AiReview expected, AiReview actual) {
        assertThat(actual)
            .as("Verify AiReview relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getReviewContent()).as("check reviewContent").isEqualTo(expected.getReviewContent()))
            .satisfies(a -> assertThat(a.getReviewResult()).as("check reviewResult").isEqualTo(expected.getReviewResult()))
            .satisfies(a -> assertThat(a.getReviewDate()).as("check reviewDate").isEqualTo(expected.getReviewDate()))
            .satisfies(a -> assertThat(a.getReviewer()).as("check reviewer").isEqualTo(expected.getReviewer()))
            .satisfies(a -> assertThat(a.getComments()).as("check comments").isEqualTo(expected.getComments()))
            .satisfies(a -> assertThat(a.getFeedback()).as("check feedback").isEqualTo(expected.getFeedback()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiReviewUpdatableRelationshipsEquals(AiReview expected, AiReview actual) {
        assertThat(actual)
            .as("Verify AiReview relationships")
            .satisfies(a -> assertThat(a.getRequest()).as("check request").isEqualTo(expected.getRequest()));
    }
}
