package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AgentConfigTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentConfigTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentConfig.class);
        AgentConfig agentConfig1 = getAgentConfigSample1();
        AgentConfig agentConfig2 = new AgentConfig();
        assertThat(agentConfig1).isNotEqualTo(agentConfig2);

        agentConfig2.setId(agentConfig1.getId());
        assertThat(agentConfig1).isEqualTo(agentConfig2);

        agentConfig2 = getAgentConfigSample2();
        assertThat(agentConfig1).isNotEqualTo(agentConfig2);
    }
}
