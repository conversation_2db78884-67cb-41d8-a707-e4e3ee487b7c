package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AiToolMetricsTestSamples.*;
import static com.whiskerguard.ai.domain.AiToolTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiToolMetricsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiToolMetrics.class);
        AiToolMetrics aiToolMetrics1 = getAiToolMetricsSample1();
        AiToolMetrics aiToolMetrics2 = new AiToolMetrics();
        assertThat(aiToolMetrics1).isNotEqualTo(aiToolMetrics2);

        aiToolMetrics2.setId(aiToolMetrics1.getId());
        assertThat(aiToolMetrics1).isEqualTo(aiToolMetrics2);

        aiToolMetrics2 = getAiToolMetricsSample2();
        assertThat(aiToolMetrics1).isNotEqualTo(aiToolMetrics2);
    }

    @Test
    void toolTest() {
        AiToolMetrics aiToolMetrics = getAiToolMetricsRandomSampleGenerator();
        AiTool aiToolBack = getAiToolRandomSampleGenerator();

        aiToolMetrics.setTool(aiToolBack);
        assertThat(aiToolMetrics.getTool()).isEqualTo(aiToolBack);

        aiToolMetrics.tool(null);
        assertThat(aiToolMetrics.getTool()).isNull();
    }
}
