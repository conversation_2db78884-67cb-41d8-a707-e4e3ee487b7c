package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AgentConfigAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentConfigAllPropertiesEquals(AgentConfig expected, AgentConfig actual) {
        assertAgentConfigAutoGeneratedPropertiesEquals(expected, actual);
        assertAgentConfigAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentConfigAllUpdatablePropertiesEquals(AgentConfig expected, AgentConfig actual) {
        assertAgentConfigUpdatableFieldsEquals(expected, actual);
        assertAgentConfigUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentConfigAutoGeneratedPropertiesEquals(AgentConfig expected, AgentConfig actual) {
        assertThat(actual)
            .as("Verify AgentConfig auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentConfigUpdatableFieldsEquals(AgentConfig expected, AgentConfig actual) {
        assertThat(actual)
            .as("Verify AgentConfig relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getConfigKey()).as("check configKey").isEqualTo(expected.getConfigKey()))
            .satisfies(a -> assertThat(a.getConfigValue()).as("check configValue").isEqualTo(expected.getConfigValue()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getConfigGroup()).as("check configGroup").isEqualTo(expected.getConfigGroup()))
            .satisfies(a -> assertThat(a.getEnabled()).as("check enabled").isEqualTo(expected.getEnabled()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentConfigUpdatableRelationshipsEquals(AgentConfig expected, AgentConfig actual) {
        // empty method
    }
}
