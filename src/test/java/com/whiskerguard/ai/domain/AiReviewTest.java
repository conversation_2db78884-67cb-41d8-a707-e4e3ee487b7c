package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AiRequestTestSamples.*;
import static com.whiskerguard.ai.domain.AiReviewTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiReviewTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiReview.class);
        AiReview aiReview1 = getAiReviewSample1();
        AiReview aiReview2 = new AiReview();
        assertThat(aiReview1).isNotEqualTo(aiReview2);

        aiReview2.setId(aiReview1.getId());
        assertThat(aiReview1).isEqualTo(aiReview2);

        aiReview2 = getAiReviewSample2();
        assertThat(aiReview1).isNotEqualTo(aiReview2);
    }

    @Test
    void requestTest() {
        AiReview aiReview = getAiReviewRandomSampleGenerator();
        AiRequest aiRequestBack = getAiRequestRandomSampleGenerator();

        aiReview.setRequest(aiRequestBack);
        assertThat(aiReview.getRequest()).isEqualTo(aiRequestBack);

        aiReview.request(null);
        assertThat(aiReview.getRequest()).isNull();
    }
}
