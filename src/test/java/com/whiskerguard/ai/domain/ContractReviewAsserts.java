package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ContractReviewAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractReviewAllPropertiesEquals(ContractReview expected, ContractReview actual) {
        assertContractReviewAutoGeneratedPropertiesEquals(expected, actual);
        assertContractReviewAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractReviewAllUpdatablePropertiesEquals(ContractReview expected, ContractReview actual) {
        assertContractReviewUpdatableFieldsEquals(expected, actual);
        assertContractReviewUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractReviewAutoGeneratedPropertiesEquals(ContractReview expected, ContractReview actual) {
        assertThat(actual)
            .as("Verify ContractReview auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractReviewUpdatableFieldsEquals(ContractReview expected, ContractReview actual) {
        assertThat(actual)
            .as("Verify ContractReview relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getContractType()).as("check contractType").isEqualTo(expected.getContractType()))
            .satisfies(a -> assertThat(a.getContractTitle()).as("check contractTitle").isEqualTo(expected.getContractTitle()))
            .satisfies(a -> assertThat(a.getContractContent()).as("check contractContent").isEqualTo(expected.getContractContent()))
            .satisfies(a -> assertThat(a.getReviewResult()).as("check reviewResult").isEqualTo(expected.getReviewResult()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getOverallRiskLevel()).as("check overallRiskLevel").isEqualTo(expected.getOverallRiskLevel()))
            .satisfies(a -> assertThat(a.getRiskScore()).as("check riskScore").isEqualTo(expected.getRiskScore()))
            .satisfies(a -> assertThat(a.getRiskSummary()).as("check riskSummary").isEqualTo(expected.getRiskSummary()))
            .satisfies(a -> assertThat(a.getAiRequestId()).as("check aiRequestId").isEqualTo(expected.getAiRequestId()))
            .satisfies(a -> assertThat(a.getReviewStartTime()).as("check reviewStartTime").isEqualTo(expected.getReviewStartTime()))
            .satisfies(a -> assertThat(a.getReviewEndTime()).as("check reviewEndTime").isEqualTo(expected.getReviewEndTime()))
            .satisfies(a -> assertThat(a.getReviewDuration()).as("check reviewDuration").isEqualTo(expected.getReviewDuration()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractReviewUpdatableRelationshipsEquals(ContractReview expected, ContractReview actual) {
        // empty method
    }
}
