package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AgentContextTestSamples.*;
import static com.whiskerguard.ai.domain.AgentTaskTestSamples.*;
import static com.whiskerguard.ai.domain.TaskStepTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class AgentTaskTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentTask.class);
        AgentTask agentTask1 = getAgentTaskSample1();
        AgentTask agentTask2 = new AgentTask();
        assertThat(agentTask1).isNotEqualTo(agentTask2);

        agentTask2.setId(agentTask1.getId());
        assertThat(agentTask1).isEqualTo(agentTask2);

        agentTask2 = getAgentTaskSample2();
        assertThat(agentTask1).isNotEqualTo(agentTask2);
    }

    @Test
    void taskStepsTest() {
        AgentTask agentTask = getAgentTaskRandomSampleGenerator();
        TaskStep taskStepBack = getTaskStepRandomSampleGenerator();

        agentTask.addTaskSteps(taskStepBack);
        assertThat(agentTask.getTaskSteps()).containsOnly(taskStepBack);
        assertThat(taskStepBack.getAgentTask()).isEqualTo(agentTask);

        agentTask.removeTaskSteps(taskStepBack);
        assertThat(agentTask.getTaskSteps()).doesNotContain(taskStepBack);
        assertThat(taskStepBack.getAgentTask()).isNull();

        agentTask.taskSteps(new HashSet<>(Set.of(taskStepBack)));
        assertThat(agentTask.getTaskSteps()).containsOnly(taskStepBack);
        assertThat(taskStepBack.getAgentTask()).isEqualTo(agentTask);

        agentTask.setTaskSteps(new HashSet<>());
        assertThat(agentTask.getTaskSteps()).doesNotContain(taskStepBack);
        assertThat(taskStepBack.getAgentTask()).isNull();
    }

    @Test
    void contextsTest() {
        AgentTask agentTask = getAgentTaskRandomSampleGenerator();
        AgentContext agentContextBack = getAgentContextRandomSampleGenerator();

        agentTask.addContexts(agentContextBack);
        assertThat(agentTask.getContexts()).containsOnly(agentContextBack);
        assertThat(agentContextBack.getAgentTask()).isEqualTo(agentTask);

        agentTask.removeContexts(agentContextBack);
        assertThat(agentTask.getContexts()).doesNotContain(agentContextBack);
        assertThat(agentContextBack.getAgentTask()).isNull();

        agentTask.contexts(new HashSet<>(Set.of(agentContextBack)));
        assertThat(agentTask.getContexts()).containsOnly(agentContextBack);
        assertThat(agentContextBack.getAgentTask()).isEqualTo(agentTask);

        agentTask.setContexts(new HashSet<>());
        assertThat(agentTask.getContexts()).doesNotContain(agentContextBack);
        assertThat(agentContextBack.getAgentTask()).isNull();
    }
}
