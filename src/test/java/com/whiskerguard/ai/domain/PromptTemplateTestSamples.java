package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class PromptTemplateTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static PromptTemplate getPromptTemplateSample1() {
        return new PromptTemplate()
            .id(1L)
            .tenantId(1L)
            .templateKey("templateKey1")
            .name("name1")
            .description("description1")
            .templateVersion(1)
            .createdById(1L)
            .lastModifiedById(1L)
            .usageCount(1L)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static PromptTemplate getPromptTemplateSample2() {
        return new PromptTemplate()
            .id(2L)
            .tenantId(2L)
            .templateKey("templateKey2")
            .name("name2")
            .description("description2")
            .templateVersion(2)
            .createdById(2L)
            .lastModifiedById(2L)
            .usageCount(2L)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static PromptTemplate getPromptTemplateRandomSampleGenerator() {
        return new PromptTemplate()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .templateKey(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .templateVersion(intCount.incrementAndGet())
            .createdById(longCount.incrementAndGet())
            .lastModifiedById(longCount.incrementAndGet())
            .usageCount(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
