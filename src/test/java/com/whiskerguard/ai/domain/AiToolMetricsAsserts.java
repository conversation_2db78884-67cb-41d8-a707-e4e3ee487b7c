package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AiToolMetricsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolMetricsAllPropertiesEquals(AiToolMetrics expected, AiToolMetrics actual) {
        assertAiToolMetricsAutoGeneratedPropertiesEquals(expected, actual);
        assertAiToolMetricsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolMetricsAllUpdatablePropertiesEquals(AiToolMetrics expected, AiToolMetrics actual) {
        assertAiToolMetricsUpdatableFieldsEquals(expected, actual);
        assertAiToolMetricsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolMetricsAutoGeneratedPropertiesEquals(AiToolMetrics expected, AiToolMetrics actual) {
        assertThat(actual)
            .as("Verify AiToolMetrics auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolMetricsUpdatableFieldsEquals(AiToolMetrics expected, AiToolMetrics actual) {
        assertThat(actual)
            .as("Verify AiToolMetrics relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getPeriod()).as("check period").isEqualTo(expected.getPeriod()))
            .satisfies(a -> assertThat(a.getResponseTime()).as("check responseTime").isEqualTo(expected.getResponseTime()))
            .satisfies(a -> assertThat(a.getSuccessCount()).as("check successCount").isEqualTo(expected.getSuccessCount()))
            .satisfies(a -> assertThat(a.getFailureCount()).as("check failureCount").isEqualTo(expected.getFailureCount()))
            .satisfies(a -> assertThat(a.getTotalRequests()).as("check totalRequests").isEqualTo(expected.getTotalRequests()))
            .satisfies(a -> assertThat(a.getErrorRate()).as("check errorRate").isEqualTo(expected.getErrorRate()))
            .satisfies(a -> assertThat(a.getCollectDate()).as("check collectDate").isEqualTo(expected.getCollectDate()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolMetricsUpdatableRelationshipsEquals(AiToolMetrics expected, AiToolMetrics actual) {
        assertThat(actual)
            .as("Verify AiToolMetrics relationships")
            .satisfies(a -> assertThat(a.getTool()).as("check tool").isEqualTo(expected.getTool()));
    }
}
