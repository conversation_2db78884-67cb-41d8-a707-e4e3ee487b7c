package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TaskStepAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTaskStepAllPropertiesEquals(TaskStep expected, TaskStep actual) {
        assertTaskStepAutoGeneratedPropertiesEquals(expected, actual);
        assertTaskStepAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTaskStepAllUpdatablePropertiesEquals(TaskStep expected, TaskStep actual) {
        assertTaskStepUpdatableFieldsEquals(expected, actual);
        assertTaskStepUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTaskStepAutoGeneratedPropertiesEquals(TaskStep expected, TaskStep actual) {
        assertThat(actual)
            .as("Verify TaskStep auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTaskStepUpdatableFieldsEquals(TaskStep expected, TaskStep actual) {
        assertThat(actual)
            .as("Verify TaskStep relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getStepName()).as("check stepName").isEqualTo(expected.getStepName()))
            .satisfies(a -> assertThat(a.getStepDescription()).as("check stepDescription").isEqualTo(expected.getStepDescription()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getStepOrder()).as("check stepOrder").isEqualTo(expected.getStepOrder()))
            .satisfies(a -> assertThat(a.getInputData()).as("check inputData").isEqualTo(expected.getInputData()))
            .satisfies(a -> assertThat(a.getOutputData()).as("check outputData").isEqualTo(expected.getOutputData()))
            .satisfies(a -> assertThat(a.getErrorMessage()).as("check errorMessage").isEqualTo(expected.getErrorMessage()))
            .satisfies(a -> assertThat(a.getStartTime()).as("check startTime").isEqualTo(expected.getStartTime()))
            .satisfies(a -> assertThat(a.getEndTime()).as("check endTime").isEqualTo(expected.getEndTime()))
            .satisfies(a -> assertThat(a.getExecutionTime()).as("check executionTime").isEqualTo(expected.getExecutionTime()))
            .satisfies(a -> assertThat(a.getRetryCount()).as("check retryCount").isEqualTo(expected.getRetryCount()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTaskStepUpdatableRelationshipsEquals(TaskStep expected, TaskStep actual) {
        assertThat(actual)
            .as("Verify TaskStep relationships")
            .satisfies(a -> assertThat(a.getAgentTask()).as("check agentTask").isEqualTo(expected.getAgentTask()));
    }
}
