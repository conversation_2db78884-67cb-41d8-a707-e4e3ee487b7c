package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AgentConfigTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AgentConfig getAgentConfigSample1() {
        return new AgentConfig()
            .id(1L)
            .tenantId(1L)
            .configKey("configKey1")
            .configValue("configValue1")
            .description("description1")
            .configGroup("configGroup1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AgentConfig getAgentConfigSample2() {
        return new AgentConfig()
            .id(2L)
            .tenantId(2L)
            .configKey("configKey2")
            .configValue("configValue2")
            .description("description2")
            .configGroup("configGroup2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AgentConfig getAgentConfigRandomSampleGenerator() {
        return new AgentConfig()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .configKey(UUID.randomUUID().toString())
            .configValue(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .configGroup(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
