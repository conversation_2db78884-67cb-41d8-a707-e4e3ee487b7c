package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PromptTemplateVariableAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVariableAllPropertiesEquals(PromptTemplateVariable expected, PromptTemplateVariable actual) {
        assertPromptTemplateVariableAutoGeneratedPropertiesEquals(expected, actual);
        assertPromptTemplateVariableAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVariableAllUpdatablePropertiesEquals(
        PromptTemplateVariable expected,
        PromptTemplateVariable actual
    ) {
        assertPromptTemplateVariableUpdatableFieldsEquals(expected, actual);
        assertPromptTemplateVariableUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVariableAutoGeneratedPropertiesEquals(
        PromptTemplateVariable expected,
        PromptTemplateVariable actual
    ) {
        assertThat(actual)
            .as("Verify PromptTemplateVariable auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVariableUpdatableFieldsEquals(PromptTemplateVariable expected, PromptTemplateVariable actual) {
        assertThat(actual)
            .as("Verify PromptTemplateVariable relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getVariableName()).as("check variableName").isEqualTo(expected.getVariableName()))
            .satisfies(a -> assertThat(a.getDisplayName()).as("check displayName").isEqualTo(expected.getDisplayName()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getVariableType()).as("check variableType").isEqualTo(expected.getVariableType()))
            .satisfies(a -> assertThat(a.getDefaultValue()).as("check defaultValue").isEqualTo(expected.getDefaultValue()))
            .satisfies(a -> assertThat(a.getIsRequired()).as("check isRequired").isEqualTo(expected.getIsRequired()))
            .satisfies(a -> assertThat(a.getValidationRule()).as("check validationRule").isEqualTo(expected.getValidationRule()))
            .satisfies(a -> assertThat(a.getExampleValue()).as("check exampleValue").isEqualTo(expected.getExampleValue()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getIsEnabled()).as("check isEnabled").isEqualTo(expected.getIsEnabled()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVariableUpdatableRelationshipsEquals(
        PromptTemplateVariable expected,
        PromptTemplateVariable actual
    ) {
        assertThat(actual)
            .as("Verify PromptTemplateVariable relationships")
            .satisfies(a -> assertThat(a.getPromptTemplate()).as("check promptTemplate").isEqualTo(expected.getPromptTemplate()));
    }
}
