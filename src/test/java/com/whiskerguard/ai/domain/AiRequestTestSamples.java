package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AiRequestTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AiRequest getAiRequestSample1() {
        return new AiRequest()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .toolType("toolType1")
            .prompt("prompt1")
            .response("response1")
            .errorMessage("errorMessage1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AiRequest getAiRequestSample2() {
        return new AiRequest()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .toolType("toolType2")
            .prompt("prompt2")
            .response("response2")
            .errorMessage("errorMessage2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AiRequest getAiRequestRandomSampleGenerator() {
        return new AiRequest()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .toolType(UUID.randomUUID().toString())
            .prompt(UUID.randomUUID().toString())
            .response(UUID.randomUUID().toString())
            .errorMessage(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
