package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PromptTemplateVersionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVersionAllPropertiesEquals(PromptTemplateVersion expected, PromptTemplateVersion actual) {
        assertPromptTemplateVersionAutoGeneratedPropertiesEquals(expected, actual);
        assertPromptTemplateVersionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVersionAllUpdatablePropertiesEquals(
        PromptTemplateVersion expected,
        PromptTemplateVersion actual
    ) {
        assertPromptTemplateVersionUpdatableFieldsEquals(expected, actual);
        assertPromptTemplateVersionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVersionAutoGeneratedPropertiesEquals(
        PromptTemplateVersion expected,
        PromptTemplateVersion actual
    ) {
        assertThat(actual)
            .as("Verify PromptTemplateVersion auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVersionUpdatableFieldsEquals(PromptTemplateVersion expected, PromptTemplateVersion actual) {
        assertThat(actual)
            .as("Verify PromptTemplateVersion relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getVersionNumber()).as("check versionNumber").isEqualTo(expected.getVersionNumber()))
            .satisfies(a -> assertThat(a.getVersionName()).as("check versionName").isEqualTo(expected.getVersionName()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a ->
                assertThat(a.getVariablesDefinition()).as("check variablesDefinition").isEqualTo(expected.getVariablesDefinition())
            )
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getIsActive()).as("check isActive").isEqualTo(expected.getIsActive()))
            .satisfies(a -> assertThat(a.getCreatedById()).as("check createdById").isEqualTo(expected.getCreatedById()))
            .satisfies(a -> assertThat(a.getChangeLog()).as("check changeLog").isEqualTo(expected.getChangeLog()))
            .satisfies(a -> assertThat(a.getUsageCount()).as("check usageCount").isEqualTo(expected.getUsageCount()))
            .satisfies(a -> assertThat(a.getLastUsedAt()).as("check lastUsedAt").isEqualTo(expected.getLastUsedAt()))
            .satisfies(a -> assertThat(a.getPerformanceScore()).as("check performanceScore").isEqualTo(expected.getPerformanceScore()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateVersionUpdatableRelationshipsEquals(
        PromptTemplateVersion expected,
        PromptTemplateVersion actual
    ) {
        assertThat(actual)
            .as("Verify PromptTemplateVersion relationships")
            .satisfies(a -> assertThat(a.getPromptTemplate()).as("check promptTemplate").isEqualTo(expected.getPromptTemplate()));
    }
}
