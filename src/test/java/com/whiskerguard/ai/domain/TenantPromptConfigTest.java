package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.TenantPromptConfigTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantPromptConfigTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantPromptConfig.class);
        TenantPromptConfig tenantPromptConfig1 = getTenantPromptConfigSample1();
        TenantPromptConfig tenantPromptConfig2 = new TenantPromptConfig();
        assertThat(tenantPromptConfig1).isNotEqualTo(tenantPromptConfig2);

        tenantPromptConfig2.setId(tenantPromptConfig1.getId());
        assertThat(tenantPromptConfig1).isEqualTo(tenantPromptConfig2);

        tenantPromptConfig2 = getTenantPromptConfigSample2();
        assertThat(tenantPromptConfig1).isNotEqualTo(tenantPromptConfig2);
    }
}
