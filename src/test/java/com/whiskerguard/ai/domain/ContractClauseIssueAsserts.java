package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ContractClauseIssueAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractClauseIssueAllPropertiesEquals(ContractClauseIssue expected, ContractClauseIssue actual) {
        assertContractClauseIssueAutoGeneratedPropertiesEquals(expected, actual);
        assertContractClauseIssueAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractClauseIssueAllUpdatablePropertiesEquals(ContractClauseIssue expected, ContractClauseIssue actual) {
        assertContractClauseIssueUpdatableFieldsEquals(expected, actual);
        assertContractClauseIssueUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractClauseIssueAutoGeneratedPropertiesEquals(ContractClauseIssue expected, ContractClauseIssue actual) {
        assertThat(actual)
            .as("Verify ContractClauseIssue auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractClauseIssueUpdatableFieldsEquals(ContractClauseIssue expected, ContractClauseIssue actual) {
        assertThat(actual)
            .as("Verify ContractClauseIssue relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getClauseText()).as("check clauseText").isEqualTo(expected.getClauseText()))
            .satisfies(a -> assertThat(a.getClauseNumber()).as("check clauseNumber").isEqualTo(expected.getClauseNumber()))
            .satisfies(a -> assertThat(a.getIssueType()).as("check issueType").isEqualTo(expected.getIssueType()))
            .satisfies(a -> assertThat(a.getIssueDescription()).as("check issueDescription").isEqualTo(expected.getIssueDescription()))
            .satisfies(a -> assertThat(a.getSeverity()).as("check severity").isEqualTo(expected.getSeverity()))
            .satisfies(a -> assertThat(a.getLegalRisk()).as("check legalRisk").isEqualTo(expected.getLegalRisk()))
            .satisfies(a -> assertThat(a.getSuggestions()).as("check suggestions").isEqualTo(expected.getSuggestions()))
            .satisfies(a -> assertThat(a.getReferenceLaws()).as("check referenceLaws").isEqualTo(expected.getReferenceLaws()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractClauseIssueUpdatableRelationshipsEquals(ContractClauseIssue expected, ContractClauseIssue actual) {
        assertThat(actual)
            .as("Verify ContractClauseIssue relationships")
            .satisfies(a -> assertThat(a.getReview()).as("check review").isEqualTo(expected.getReview()));
    }
}
