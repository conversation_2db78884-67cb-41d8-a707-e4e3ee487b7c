package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AgentTaskTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AgentTask getAgentTaskSample1() {
        return new AgentTask()
            .id(1L)
            .tenantId(1L)
            .title("title1")
            .description("description1")
            .errorMessage("errorMessage1")
            .executionTime(1L)
            .progress(1)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AgentTask getAgentTaskSample2() {
        return new AgentTask()
            .id(2L)
            .tenantId(2L)
            .title("title2")
            .description("description2")
            .errorMessage("errorMessage2")
            .executionTime(2L)
            .progress(2)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AgentTask getAgentTaskRandomSampleGenerator() {
        return new AgentTask()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .title(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .errorMessage(UUID.randomUUID().toString())
            .executionTime(longCount.incrementAndGet())
            .progress(intCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
