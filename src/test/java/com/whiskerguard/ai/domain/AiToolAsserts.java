package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AiToolAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolAllPropertiesEquals(AiTool expected, AiTool actual) {
        assertAiToolAutoGeneratedPropertiesEquals(expected, actual);
        assertAiToolAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolAllUpdatablePropertiesEquals(AiTool expected, AiTool actual) {
        assertAiToolUpdatableFieldsEquals(expected, actual);
        assertAiToolUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolAutoGeneratedPropertiesEquals(AiTool expected, AiTool actual) {
        assertThat(actual)
            .as("Verify AiTool auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolUpdatableFieldsEquals(AiTool expected, AiTool actual) {
        assertThat(actual)
            .as("Verify AiTool relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getToolKey()).as("check toolKey").isEqualTo(expected.getToolKey()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getApiUrl()).as("check apiUrl").isEqualTo(expected.getApiUrl()))
            .satisfies(a -> assertThat(a.getApiKey()).as("check apiKey").isEqualTo(expected.getApiKey()))
            .satisfies(a -> assertThat(a.getAuthType()).as("check authType").isEqualTo(expected.getAuthType()))
            .satisfies(a -> assertThat(a.getPath()).as("check path").isEqualTo(expected.getPath()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getWeight()).as("check weight").isEqualTo(expected.getWeight()))
            .satisfies(a -> assertThat(a.getMaxConcurrentCalls()).as("check maxConcurrentCalls").isEqualTo(expected.getMaxConcurrentCalls())
            )
            .satisfies(a -> assertThat(a.getIsModel()).as("check isModel").isEqualTo(expected.getIsModel()))
            .satisfies(a -> assertThat(a.getModelCategory()).as("check modelCategory").isEqualTo(expected.getModelCategory()))
            .satisfies(a -> assertThat(a.getModelProvider()).as("check modelProvider").isEqualTo(expected.getModelProvider()))
            .satisfies(a -> assertThat(a.getRemark()).as("check remark").isEqualTo(expected.getRemark()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiToolUpdatableRelationshipsEquals(AiTool expected, AiTool actual) {
        // empty method
    }
}
