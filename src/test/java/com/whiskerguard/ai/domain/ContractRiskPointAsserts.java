package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ContractRiskPointAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractRiskPointAllPropertiesEquals(ContractRiskPoint expected, ContractRiskPoint actual) {
        assertContractRiskPointAutoGeneratedPropertiesEquals(expected, actual);
        assertContractRiskPointAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractRiskPointAllUpdatablePropertiesEquals(ContractRiskPoint expected, ContractRiskPoint actual) {
        assertContractRiskPointUpdatableFieldsEquals(expected, actual);
        assertContractRiskPointUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractRiskPointAutoGeneratedPropertiesEquals(ContractRiskPoint expected, ContractRiskPoint actual) {
        assertThat(actual)
            .as("Verify ContractRiskPoint auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractRiskPointUpdatableFieldsEquals(ContractRiskPoint expected, ContractRiskPoint actual) {
        assertThat(actual)
            .as("Verify ContractRiskPoint relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getRiskCategory()).as("check riskCategory").isEqualTo(expected.getRiskCategory()))
            .satisfies(a -> assertThat(a.getRiskDescription()).as("check riskDescription").isEqualTo(expected.getRiskDescription()))
            .satisfies(a -> assertThat(a.getSeverity()).as("check severity").isEqualTo(expected.getSeverity()))
            .satisfies(a -> assertThat(a.getAffectedClauses()).as("check affectedClauses").isEqualTo(expected.getAffectedClauses()))
            .satisfies(a -> assertThat(a.getLegalBasis()).as("check legalBasis").isEqualTo(expected.getLegalBasis()))
            .satisfies(a -> assertThat(a.getSuggestions()).as("check suggestions").isEqualTo(expected.getSuggestions()))
            .satisfies(a -> assertThat(a.getRiskScore()).as("check riskScore").isEqualTo(expected.getRiskScore()))
            .satisfies(a -> assertThat(a.getIsCritical()).as("check isCritical").isEqualTo(expected.getIsCritical()))
            .satisfies(a -> assertThat(a.getRiskSource()).as("check riskSource").isEqualTo(expected.getRiskSource()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertContractRiskPointUpdatableRelationshipsEquals(ContractRiskPoint expected, ContractRiskPoint actual) {
        assertThat(actual)
            .as("Verify ContractRiskPoint relationships")
            .satisfies(a -> assertThat(a.getReview()).as("check review").isEqualTo(expected.getReview()));
    }
}
