package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AgentContextTestSamples.*;
import static com.whiskerguard.ai.domain.AgentTaskTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentContextTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentContext.class);
        AgentContext agentContext1 = getAgentContextSample1();
        AgentContext agentContext2 = new AgentContext();
        assertThat(agentContext1).isNotEqualTo(agentContext2);

        agentContext2.setId(agentContext1.getId());
        assertThat(agentContext1).isEqualTo(agentContext2);

        agentContext2 = getAgentContextSample2();
        assertThat(agentContext1).isNotEqualTo(agentContext2);
    }

    @Test
    void agentTaskTest() {
        AgentContext agentContext = getAgentContextRandomSampleGenerator();
        AgentTask agentTaskBack = getAgentTaskRandomSampleGenerator();

        agentContext.setAgentTask(agentTaskBack);
        assertThat(agentContext.getAgentTask()).isEqualTo(agentTaskBack);

        agentContext.agentTask(null);
        assertThat(agentContext.getAgentTask()).isNull();
    }
}
