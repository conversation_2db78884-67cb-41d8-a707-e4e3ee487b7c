package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class KnowledgeCacheAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertKnowledgeCacheAllPropertiesEquals(KnowledgeCache expected, KnowledgeCache actual) {
        assertKnowledgeCacheAutoGeneratedPropertiesEquals(expected, actual);
        assertKnowledgeCacheAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertKnowledgeCacheAllUpdatablePropertiesEquals(KnowledgeCache expected, KnowledgeCache actual) {
        assertKnowledgeCacheUpdatableFieldsEquals(expected, actual);
        assertKnowledgeCacheUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertKnowledgeCacheAutoGeneratedPropertiesEquals(KnowledgeCache expected, KnowledgeCache actual) {
        assertThat(actual)
            .as("Verify KnowledgeCache auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertKnowledgeCacheUpdatableFieldsEquals(KnowledgeCache expected, KnowledgeCache actual) {
        assertThat(actual)
            .as("Verify KnowledgeCache relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getKnowledgeType()).as("check knowledgeType").isEqualTo(expected.getKnowledgeType()))
            .satisfies(a -> assertThat(a.getQueryKey()).as("check queryKey").isEqualTo(expected.getQueryKey()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a -> assertThat(a.getSimilarityScore()).as("check similarityScore").isEqualTo(expected.getSimilarityScore()))
            .satisfies(a -> assertThat(a.getSourceService()).as("check sourceService").isEqualTo(expected.getSourceService()))
            .satisfies(a -> assertThat(a.getExpireTime()).as("check expireTime").isEqualTo(expected.getExpireTime()))
            .satisfies(a -> assertThat(a.getAccessCount()).as("check accessCount").isEqualTo(expected.getAccessCount()))
            .satisfies(a -> assertThat(a.getLastAccessTime()).as("check lastAccessTime").isEqualTo(expected.getLastAccessTime()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertKnowledgeCacheUpdatableRelationshipsEquals(KnowledgeCache expected, KnowledgeCache actual) {
        // empty method
    }
}
