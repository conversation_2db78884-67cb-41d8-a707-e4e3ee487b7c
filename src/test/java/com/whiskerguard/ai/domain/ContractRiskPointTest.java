package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.ContractReviewTestSamples.*;
import static com.whiskerguard.ai.domain.ContractRiskPointTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractRiskPointTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractRiskPoint.class);
        ContractRiskPoint contractRiskPoint1 = getContractRiskPointSample1();
        ContractRiskPoint contractRiskPoint2 = new ContractRiskPoint();
        assertThat(contractRiskPoint1).isNotEqualTo(contractRiskPoint2);

        contractRiskPoint2.setId(contractRiskPoint1.getId());
        assertThat(contractRiskPoint1).isEqualTo(contractRiskPoint2);

        contractRiskPoint2 = getContractRiskPointSample2();
        assertThat(contractRiskPoint1).isNotEqualTo(contractRiskPoint2);
    }

    @Test
    void reviewTest() {
        ContractRiskPoint contractRiskPoint = getContractRiskPointRandomSampleGenerator();
        ContractReview contractReviewBack = getContractReviewRandomSampleGenerator();

        contractRiskPoint.setReview(contractReviewBack);
        assertThat(contractRiskPoint.getReview()).isEqualTo(contractReviewBack);

        contractRiskPoint.review(null);
        assertThat(contractRiskPoint.getReview()).isNull();
    }
}
