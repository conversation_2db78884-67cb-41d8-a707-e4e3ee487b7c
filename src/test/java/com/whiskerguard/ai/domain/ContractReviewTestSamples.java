package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ContractReviewTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ContractReview getContractReviewSample1() {
        return new ContractReview()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .contractType("contractType1")
            .contractTitle("contractTitle1")
            .riskScore(1)
            .aiRequestId(1L)
            .reviewDuration(1L)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static ContractReview getContractReviewSample2() {
        return new ContractReview()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .contractType("contractType2")
            .contractTitle("contractTitle2")
            .riskScore(2)
            .aiRequestId(2L)
            .reviewDuration(2L)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static ContractReview getContractReviewRandomSampleGenerator() {
        return new ContractReview()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .contractType(UUID.randomUUID().toString())
            .contractTitle(UUID.randomUUID().toString())
            .riskScore(intCount.incrementAndGet())
            .aiRequestId(longCount.incrementAndGet())
            .reviewDuration(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
