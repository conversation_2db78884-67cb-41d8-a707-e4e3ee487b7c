package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AiReviewTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AiReview getAiReviewSample1() {
        return new AiReview()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .reviewContent("reviewContent1")
            .reviewer("reviewer1")
            .comments("comments1")
            .feedback("feedback1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AiReview getAiReviewSample2() {
        return new AiReview()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .reviewContent("reviewContent2")
            .reviewer("reviewer2")
            .comments("comments2")
            .feedback("feedback2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AiReview getAiReviewRandomSampleGenerator() {
        return new AiReview()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .reviewContent(UUID.randomUUID().toString())
            .reviewer(UUID.randomUUID().toString())
            .comments(UUID.randomUUID().toString())
            .feedback(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
