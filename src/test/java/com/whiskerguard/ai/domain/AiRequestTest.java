package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AiRequestTestSamples.*;
import static com.whiskerguard.ai.domain.AiToolTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiRequestTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiRequest.class);
        AiRequest aiRequest1 = getAiRequestSample1();
        AiRequest aiRequest2 = new AiRequest();
        assertThat(aiRequest1).isNotEqualTo(aiRequest2);

        aiRequest2.setId(aiRequest1.getId());
        assertThat(aiRequest1).isEqualTo(aiRequest2);

        aiRequest2 = getAiRequestSample2();
        assertThat(aiRequest1).isNotEqualTo(aiRequest2);
    }

    @Test
    void toolTest() {
        AiRequest aiRequest = getAiRequestRandomSampleGenerator();
        AiTool aiToolBack = getAiToolRandomSampleGenerator();

        aiRequest.setTool(aiToolBack);
        assertThat(aiRequest.getTool()).isEqualTo(aiToolBack);

        aiRequest.tool(null);
        assertThat(aiRequest.getTool()).isNull();
    }
}
