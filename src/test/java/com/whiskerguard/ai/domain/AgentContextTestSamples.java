package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AgentContextTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AgentContext getAgentContextSample1() {
        return new AgentContext()
            .id(1L)
            .tenantId(1L)
            .contextKey("contextKey1")
            .contextType("contextType1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AgentContext getAgentContextSample2() {
        return new AgentContext()
            .id(2L)
            .tenantId(2L)
            .contextKey("contextKey2")
            .contextType("contextType2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AgentContext getAgentContextRandomSampleGenerator() {
        return new AgentContext()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .contextKey(UUID.randomUUID().toString())
            .contextType(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
