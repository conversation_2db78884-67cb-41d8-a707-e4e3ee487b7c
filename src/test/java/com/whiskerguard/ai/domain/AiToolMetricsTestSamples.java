package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AiToolMetricsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AiToolMetrics getAiToolMetricsSample1() {
        return new AiToolMetrics()
            .id(1L)
            .tenantId(1L)
            .responseTime(1)
            .successCount(1L)
            .failureCount(1L)
            .totalRequests(1L)
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AiToolMetrics getAiToolMetricsSample2() {
        return new AiToolMetrics()
            .id(2L)
            .tenantId(2L)
            .responseTime(2)
            .successCount(2L)
            .failureCount(2L)
            .totalRequests(2L)
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AiToolMetrics getAiToolMetricsRandomSampleGenerator() {
        return new AiToolMetrics()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .responseTime(intCount.incrementAndGet())
            .successCount(longCount.incrementAndGet())
            .failureCount(longCount.incrementAndGet())
            .totalRequests(longCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
