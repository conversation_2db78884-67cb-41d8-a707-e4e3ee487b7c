package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.PromptTemplateTestSamples.*;
import static com.whiskerguard.ai.domain.PromptTemplateVersionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromptTemplateVersionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplateVersion.class);
        PromptTemplateVersion promptTemplateVersion1 = getPromptTemplateVersionSample1();
        PromptTemplateVersion promptTemplateVersion2 = new PromptTemplateVersion();
        assertThat(promptTemplateVersion1).isNotEqualTo(promptTemplateVersion2);

        promptTemplateVersion2.setId(promptTemplateVersion1.getId());
        assertThat(promptTemplateVersion1).isEqualTo(promptTemplateVersion2);

        promptTemplateVersion2 = getPromptTemplateVersionSample2();
        assertThat(promptTemplateVersion1).isNotEqualTo(promptTemplateVersion2);
    }

    @Test
    void promptTemplateTest() {
        PromptTemplateVersion promptTemplateVersion = getPromptTemplateVersionRandomSampleGenerator();
        PromptTemplate promptTemplateBack = getPromptTemplateRandomSampleGenerator();

        promptTemplateVersion.setPromptTemplate(promptTemplateBack);
        assertThat(promptTemplateVersion.getPromptTemplate()).isEqualTo(promptTemplateBack);

        promptTemplateVersion.promptTemplate(null);
        assertThat(promptTemplateVersion.getPromptTemplate()).isNull();
    }
}
