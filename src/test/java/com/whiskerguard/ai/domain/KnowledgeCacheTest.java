package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.KnowledgeCacheTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class KnowledgeCacheTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(KnowledgeCache.class);
        KnowledgeCache knowledgeCache1 = getKnowledgeCacheSample1();
        KnowledgeCache knowledgeCache2 = new KnowledgeCache();
        assertThat(knowledgeCache1).isNotEqualTo(knowledgeCache2);

        knowledgeCache2.setId(knowledgeCache1.getId());
        assertThat(knowledgeCache1).isEqualTo(knowledgeCache2);

        knowledgeCache2 = getKnowledgeCacheSample2();
        assertThat(knowledgeCache1).isNotEqualTo(knowledgeCache2);
    }
}
