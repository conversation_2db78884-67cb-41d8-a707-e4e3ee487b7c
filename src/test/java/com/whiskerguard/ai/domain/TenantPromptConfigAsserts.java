package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TenantPromptConfigAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantPromptConfigAllPropertiesEquals(TenantPromptConfig expected, TenantPromptConfig actual) {
        assertTenantPromptConfigAutoGeneratedPropertiesEquals(expected, actual);
        assertTenantPromptConfigAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantPromptConfigAllUpdatablePropertiesEquals(TenantPromptConfig expected, TenantPromptConfig actual) {
        assertTenantPromptConfigUpdatableFieldsEquals(expected, actual);
        assertTenantPromptConfigUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantPromptConfigAutoGeneratedPropertiesEquals(TenantPromptConfig expected, TenantPromptConfig actual) {
        assertThat(actual)
            .as("Verify TenantPromptConfig auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantPromptConfigUpdatableFieldsEquals(TenantPromptConfig expected, TenantPromptConfig actual) {
        assertThat(actual)
            .as("Verify TenantPromptConfig relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getConfigKey()).as("check configKey").isEqualTo(expected.getConfigKey()))
            .satisfies(a -> assertThat(a.getConfigValue()).as("check configValue").isEqualTo(expected.getConfigValue()))
            .satisfies(a -> assertThat(a.getConfigType()).as("check configType").isEqualTo(expected.getConfigType()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getIsEnabled()).as("check isEnabled").isEqualTo(expected.getIsEnabled()))
            .satisfies(a -> assertThat(a.getPriority()).as("check priority").isEqualTo(expected.getPriority()))
            .satisfies(a -> assertThat(a.getEffectiveFrom()).as("check effectiveFrom").isEqualTo(expected.getEffectiveFrom()))
            .satisfies(a -> assertThat(a.getEffectiveTo()).as("check effectiveTo").isEqualTo(expected.getEffectiveTo()))
            .satisfies(a -> assertThat(a.getCreatedById()).as("check createdById").isEqualTo(expected.getCreatedById()))
            .satisfies(a -> assertThat(a.getLastModifiedById()).as("check lastModifiedById").isEqualTo(expected.getLastModifiedById()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantPromptConfigUpdatableRelationshipsEquals(TenantPromptConfig expected, TenantPromptConfig actual) {
        // empty method
    }
}
