package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AgentTaskTestSamples.*;
import static com.whiskerguard.ai.domain.TaskStepTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TaskStepTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TaskStep.class);
        TaskStep taskStep1 = getTaskStepSample1();
        TaskStep taskStep2 = new TaskStep();
        assertThat(taskStep1).isNotEqualTo(taskStep2);

        taskStep2.setId(taskStep1.getId());
        assertThat(taskStep1).isEqualTo(taskStep2);

        taskStep2 = getTaskStepSample2();
        assertThat(taskStep1).isNotEqualTo(taskStep2);
    }

    @Test
    void agentTaskTest() {
        TaskStep taskStep = getTaskStepRandomSampleGenerator();
        AgentTask agentTaskBack = getAgentTaskRandomSampleGenerator();

        taskStep.setAgentTask(agentTaskBack);
        assertThat(taskStep.getAgentTask()).isEqualTo(agentTaskBack);

        taskStep.agentTask(null);
        assertThat(taskStep.getAgentTask()).isNull();
    }
}
