package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AiToolTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static AiTool getAiToolSample1() {
        return new AiTool()
            .id(1L)
            .tenantId(1L)
            .name("name1")
            .toolKey("toolKey1")
            .version(1)
            .apiUrl("apiUrl1")
            .apiKey("apiKey1")
            .authType("authType1")
            .path("path1")
            .weight(1)
            .maxConcurrentCalls(1)
            .modelCategory("modelCategory1")
            .modelProvider("modelProvider1")
            .remark("remark1")
            .metadata("metadata1")
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static AiTool getAiToolSample2() {
        return new AiTool()
            .id(2L)
            .tenantId(2L)
            .name("name2")
            .toolKey("toolKey2")
            .version(2)
            .apiUrl("apiUrl2")
            .apiKey("apiKey2")
            .authType("authType2")
            .path("path2")
            .weight(2)
            .maxConcurrentCalls(2)
            .modelCategory("modelCategory2")
            .modelProvider("modelProvider2")
            .remark("remark2")
            .metadata("metadata2")
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static AiTool getAiToolRandomSampleGenerator() {
        return new AiTool()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .toolKey(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .apiUrl(UUID.randomUUID().toString())
            .apiKey(UUID.randomUUID().toString())
            .authType(UUID.randomUUID().toString())
            .path(UUID.randomUUID().toString())
            .weight(intCount.incrementAndGet())
            .maxConcurrentCalls(intCount.incrementAndGet())
            .modelCategory(UUID.randomUUID().toString())
            .modelProvider(UUID.randomUUID().toString())
            .remark(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
