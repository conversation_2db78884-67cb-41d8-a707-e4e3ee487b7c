package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PromptTemplateAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateAllPropertiesEquals(PromptTemplate expected, PromptTemplate actual) {
        assertPromptTemplateAutoGeneratedPropertiesEquals(expected, actual);
        assertPromptTemplateAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateAllUpdatablePropertiesEquals(PromptTemplate expected, PromptTemplate actual) {
        assertPromptTemplateUpdatableFieldsEquals(expected, actual);
        assertPromptTemplateUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateAutoGeneratedPropertiesEquals(PromptTemplate expected, PromptTemplate actual) {
        assertThat(actual)
            .as("Verify PromptTemplate auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateUpdatableFieldsEquals(PromptTemplate expected, PromptTemplate actual) {
        assertThat(actual)
            .as("Verify PromptTemplate relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getTemplateKey()).as("check templateKey").isEqualTo(expected.getTemplateKey()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getTemplateType()).as("check templateType").isEqualTo(expected.getTemplateType()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getTemplateVersion()).as("check templateVersion").isEqualTo(expected.getTemplateVersion()))
            .satisfies(a -> assertThat(a.getIsSystemDefault()).as("check isSystemDefault").isEqualTo(expected.getIsSystemDefault()))
            .satisfies(a -> assertThat(a.getCreatedById()).as("check createdById").isEqualTo(expected.getCreatedById()))
            .satisfies(a -> assertThat(a.getLastModifiedById()).as("check lastModifiedById").isEqualTo(expected.getLastModifiedById()))
            .satisfies(a -> assertThat(a.getUsageCount()).as("check usageCount").isEqualTo(expected.getUsageCount()))
            .satisfies(a -> assertThat(a.getLastUsedAt()).as("check lastUsedAt").isEqualTo(expected.getLastUsedAt()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromptTemplateUpdatableRelationshipsEquals(PromptTemplate expected, PromptTemplate actual) {
        // empty method
    }
}
