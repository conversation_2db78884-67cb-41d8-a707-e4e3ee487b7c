package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AgentTaskAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentTaskAllPropertiesEquals(AgentTask expected, AgentTask actual) {
        assertAgentTaskAutoGeneratedPropertiesEquals(expected, actual);
        assertAgentTaskAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentTaskAllUpdatablePropertiesEquals(AgentTask expected, AgentTask actual) {
        assertAgentTaskUpdatableFieldsEquals(expected, actual);
        assertAgentTaskUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentTaskAutoGeneratedPropertiesEquals(AgentTask expected, AgentTask actual) {
        assertThat(actual)
            .as("Verify AgentTask auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentTaskUpdatableFieldsEquals(AgentTask expected, AgentTask actual) {
        assertThat(actual)
            .as("Verify AgentTask relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getTaskType()).as("check taskType").isEqualTo(expected.getTaskType()))
            .satisfies(a -> assertThat(a.getTitle()).as("check title").isEqualTo(expected.getTitle()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getPriority()).as("check priority").isEqualTo(expected.getPriority()))
            .satisfies(a -> assertThat(a.getRequestData()).as("check requestData").isEqualTo(expected.getRequestData()))
            .satisfies(a -> assertThat(a.getResponseData()).as("check responseData").isEqualTo(expected.getResponseData()))
            .satisfies(a -> assertThat(a.getErrorMessage()).as("check errorMessage").isEqualTo(expected.getErrorMessage()))
            .satisfies(a -> assertThat(a.getStartTime()).as("check startTime").isEqualTo(expected.getStartTime()))
            .satisfies(a -> assertThat(a.getEndTime()).as("check endTime").isEqualTo(expected.getEndTime()))
            .satisfies(a -> assertThat(a.getExecutionTime()).as("check executionTime").isEqualTo(expected.getExecutionTime()))
            .satisfies(a -> assertThat(a.getProgress()).as("check progress").isEqualTo(expected.getProgress()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentTaskUpdatableRelationshipsEquals(AgentTask expected, AgentTask actual) {
        // empty method
    }
}
