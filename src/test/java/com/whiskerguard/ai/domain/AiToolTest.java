package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AiToolTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiToolTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiTool.class);
        AiTool aiTool1 = getAiToolSample1();
        AiTool aiTool2 = new AiTool();
        assertThat(aiTool1).isNotEqualTo(aiTool2);

        aiTool2.setId(aiTool1.getId());
        assertThat(aiTool1).isEqualTo(aiTool2);

        aiTool2 = getAiToolSample2();
        assertThat(aiTool1).isNotEqualTo(aiTool2);
    }
}
