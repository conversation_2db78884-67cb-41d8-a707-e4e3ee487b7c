package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AgentContextAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentContextAllPropertiesEquals(AgentContext expected, AgentContext actual) {
        assertAgentContextAutoGeneratedPropertiesEquals(expected, actual);
        assertAgentContextAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentContextAllUpdatablePropertiesEquals(AgentContext expected, AgentContext actual) {
        assertAgentContextUpdatableFieldsEquals(expected, actual);
        assertAgentContextUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentContextAutoGeneratedPropertiesEquals(AgentContext expected, AgentContext actual) {
        assertThat(actual)
            .as("Verify AgentContext auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentContextUpdatableFieldsEquals(AgentContext expected, AgentContext actual) {
        assertThat(actual)
            .as("Verify AgentContext relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getContextKey()).as("check contextKey").isEqualTo(expected.getContextKey()))
            .satisfies(a -> assertThat(a.getContextValue()).as("check contextValue").isEqualTo(expected.getContextValue()))
            .satisfies(a -> assertThat(a.getContextType()).as("check contextType").isEqualTo(expected.getContextType()))
            .satisfies(a -> assertThat(a.getExpireTime()).as("check expireTime").isEqualTo(expected.getExpireTime()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAgentContextUpdatableRelationshipsEquals(AgentContext expected, AgentContext actual) {
        assertThat(actual)
            .as("Verify AgentContext relationships")
            .satisfies(a -> assertThat(a.getAgentTask()).as("check agentTask").isEqualTo(expected.getAgentTask()));
    }
}
