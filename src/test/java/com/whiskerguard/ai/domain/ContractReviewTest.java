package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.ContractClauseIssueTestSamples.*;
import static com.whiskerguard.ai.domain.ContractPartyTestSamples.*;
import static com.whiskerguard.ai.domain.ContractReviewTestSamples.*;
import static com.whiskerguard.ai.domain.ContractRiskPointTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class ContractReviewTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractReview.class);
        ContractReview contractReview1 = getContractReviewSample1();
        ContractReview contractReview2 = new ContractReview();
        assertThat(contractReview1).isNotEqualTo(contractReview2);

        contractReview2.setId(contractReview1.getId());
        assertThat(contractReview1).isEqualTo(contractReview2);

        contractReview2 = getContractReviewSample2();
        assertThat(contractReview1).isNotEqualTo(contractReview2);
    }

    @Test
    void contractPartyTest() {
        ContractReview contractReview = getContractReviewRandomSampleGenerator();
        ContractParty contractPartyBack = getContractPartyRandomSampleGenerator();

        contractReview.addContractParty(contractPartyBack);
        assertThat(contractReview.getContractParties()).containsOnly(contractPartyBack);
        assertThat(contractPartyBack.getReview()).isEqualTo(contractReview);

        contractReview.removeContractParty(contractPartyBack);
        assertThat(contractReview.getContractParties()).doesNotContain(contractPartyBack);
        assertThat(contractPartyBack.getReview()).isNull();

        contractReview.contractParties(new HashSet<>(Set.of(contractPartyBack)));
        assertThat(contractReview.getContractParties()).containsOnly(contractPartyBack);
        assertThat(contractPartyBack.getReview()).isEqualTo(contractReview);

        contractReview.setContractParties(new HashSet<>());
        assertThat(contractReview.getContractParties()).doesNotContain(contractPartyBack);
        assertThat(contractPartyBack.getReview()).isNull();
    }

    @Test
    void contractRiskPointTest() {
        ContractReview contractReview = getContractReviewRandomSampleGenerator();
        ContractRiskPoint contractRiskPointBack = getContractRiskPointRandomSampleGenerator();

        contractReview.addContractRiskPoint(contractRiskPointBack);
        assertThat(contractReview.getContractRiskPoints()).containsOnly(contractRiskPointBack);
        assertThat(contractRiskPointBack.getReview()).isEqualTo(contractReview);

        contractReview.removeContractRiskPoint(contractRiskPointBack);
        assertThat(contractReview.getContractRiskPoints()).doesNotContain(contractRiskPointBack);
        assertThat(contractRiskPointBack.getReview()).isNull();

        contractReview.contractRiskPoints(new HashSet<>(Set.of(contractRiskPointBack)));
        assertThat(contractReview.getContractRiskPoints()).containsOnly(contractRiskPointBack);
        assertThat(contractRiskPointBack.getReview()).isEqualTo(contractReview);

        contractReview.setContractRiskPoints(new HashSet<>());
        assertThat(contractReview.getContractRiskPoints()).doesNotContain(contractRiskPointBack);
        assertThat(contractRiskPointBack.getReview()).isNull();
    }

    @Test
    void contractClauseIssueTest() {
        ContractReview contractReview = getContractReviewRandomSampleGenerator();
        ContractClauseIssue contractClauseIssueBack = getContractClauseIssueRandomSampleGenerator();

        contractReview.addContractClauseIssue(contractClauseIssueBack);
        assertThat(contractReview.getContractClauseIssues()).containsOnly(contractClauseIssueBack);
        assertThat(contractClauseIssueBack.getReview()).isEqualTo(contractReview);

        contractReview.removeContractClauseIssue(contractClauseIssueBack);
        assertThat(contractReview.getContractClauseIssues()).doesNotContain(contractClauseIssueBack);
        assertThat(contractClauseIssueBack.getReview()).isNull();

        contractReview.contractClauseIssues(new HashSet<>(Set.of(contractClauseIssueBack)));
        assertThat(contractReview.getContractClauseIssues()).containsOnly(contractClauseIssueBack);
        assertThat(contractClauseIssueBack.getReview()).isEqualTo(contractReview);

        contractReview.setContractClauseIssues(new HashSet<>());
        assertThat(contractReview.getContractClauseIssues()).doesNotContain(contractClauseIssueBack);
        assertThat(contractClauseIssueBack.getReview()).isNull();
    }
}
