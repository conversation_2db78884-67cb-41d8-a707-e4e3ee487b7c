package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class PromptTemplateVersionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static PromptTemplateVersion getPromptTemplateVersionSample1() {
        return new PromptTemplateVersion()
            .id(1L)
            .tenantId(1L)
            .versionNumber(1)
            .versionName("versionName1")
            .description("description1")
            .createdById(1L)
            .changeLog("changeLog1")
            .usageCount(1L)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static PromptTemplateVersion getPromptTemplateVersionSample2() {
        return new PromptTemplateVersion()
            .id(2L)
            .tenantId(2L)
            .versionNumber(2)
            .versionName("versionName2")
            .description("description2")
            .createdById(2L)
            .changeLog("changeLog2")
            .usageCount(2L)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static PromptTemplateVersion getPromptTemplateVersionRandomSampleGenerator() {
        return new PromptTemplateVersion()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .versionNumber(intCount.incrementAndGet())
            .versionName(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .createdById(longCount.incrementAndGet())
            .changeLog(UUID.randomUUID().toString())
            .usageCount(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
