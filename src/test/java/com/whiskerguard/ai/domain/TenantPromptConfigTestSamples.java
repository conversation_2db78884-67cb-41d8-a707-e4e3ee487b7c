package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TenantPromptConfigTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static TenantPromptConfig getTenantPromptConfigSample1() {
        return new TenantPromptConfig()
            .id(1L)
            .tenantId(1L)
            .configKey("configKey1")
            .configType("configType1")
            .description("description1")
            .priority(1)
            .createdById(1L)
            .lastModifiedById(1L)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static TenantPromptConfig getTenantPromptConfigSample2() {
        return new TenantPromptConfig()
            .id(2L)
            .tenantId(2L)
            .configKey("configKey2")
            .configType("configType2")
            .description("description2")
            .priority(2)
            .createdById(2L)
            .lastModifiedById(2L)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static TenantPromptConfig getTenantPromptConfigRandomSampleGenerator() {
        return new TenantPromptConfig()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .configKey(UUID.randomUUID().toString())
            .configType(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .priority(intCount.incrementAndGet())
            .createdById(longCount.incrementAndGet())
            .lastModifiedById(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
