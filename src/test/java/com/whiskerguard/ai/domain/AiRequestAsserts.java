package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AiRequestAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiRequestAllPropertiesEquals(AiRequest expected, AiRequest actual) {
        assertAiRequestAutoGeneratedPropertiesEquals(expected, actual);
        assertAiRequestAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiRequestAllUpdatablePropertiesEquals(AiRequest expected, AiRequest actual) {
        assertAiRequestUpdatableFieldsEquals(expected, actual);
        assertAiRequestUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiRequestAutoGeneratedPropertiesEquals(AiRequest expected, AiRequest actual) {
        assertThat(actual)
            .as("Verify AiRequest auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiRequestUpdatableFieldsEquals(AiRequest expected, AiRequest actual) {
        assertThat(actual)
            .as("Verify AiRequest relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getToolType()).as("check toolType").isEqualTo(expected.getToolType()))
            .satisfies(a -> assertThat(a.getPrompt()).as("check prompt").isEqualTo(expected.getPrompt()))
            .satisfies(a -> assertThat(a.getResponse()).as("check response").isEqualTo(expected.getResponse()))
            .satisfies(a -> assertThat(a.getRequestTime()).as("check requestTime").isEqualTo(expected.getRequestTime()))
            .satisfies(a -> assertThat(a.getResponseTime()).as("check responseTime").isEqualTo(expected.getResponseTime()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getErrorMessage()).as("check errorMessage").isEqualTo(expected.getErrorMessage()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAiRequestUpdatableRelationshipsEquals(AiRequest expected, AiRequest actual) {
        assertThat(actual)
            .as("Verify AiRequest relationships")
            .satisfies(a -> assertThat(a.getTool()).as("check tool").isEqualTo(expected.getTool()));
    }
}
