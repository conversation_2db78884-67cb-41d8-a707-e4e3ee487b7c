package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ContractClauseIssueTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ContractClauseIssue getContractClauseIssueSample1() {
        return new ContractClauseIssue().id(1L).tenantId(1L).clauseNumber("clauseNumber1").issueType("issueType1").version(1);
    }

    public static ContractClauseIssue getContractClauseIssueSample2() {
        return new ContractClauseIssue().id(2L).tenantId(2L).clauseNumber("clauseNumber2").issueType("issueType2").version(2);
    }

    public static ContractClauseIssue getContractClauseIssueRandomSampleGenerator() {
        return new ContractClauseIssue()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .clauseNumber(UUID.randomUUID().toString())
            .issueType(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet());
    }
}
