package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class PromptTemplateVariableTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static PromptTemplateVariable getPromptTemplateVariableSample1() {
        return new PromptTemplateVariable()
            .id(1L)
            .tenantId(1L)
            .variableName("variableName1")
            .displayName("displayName1")
            .description("description1")
            .validationRule("validationRule1")
            .sortOrder(1)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static PromptTemplateVariable getPromptTemplateVariableSample2() {
        return new PromptTemplateVariable()
            .id(2L)
            .tenantId(2L)
            .variableName("variableName2")
            .displayName("displayName2")
            .description("description2")
            .validationRule("validationRule2")
            .sortOrder(2)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static PromptTemplateVariable getPromptTemplateVariableRandomSampleGenerator() {
        return new PromptTemplateVariable()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .variableName(UUID.randomUUID().toString())
            .displayName(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .validationRule(UUID.randomUUID().toString())
            .sortOrder(intCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
