package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.ContractPartyTestSamples.*;
import static com.whiskerguard.ai.domain.ContractReviewTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractPartyTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractParty.class);
        ContractParty contractParty1 = getContractPartySample1();
        ContractParty contractParty2 = new ContractParty();
        assertThat(contractParty1).isNotEqualTo(contractParty2);

        contractParty2.setId(contractParty1.getId());
        assertThat(contractParty1).isEqualTo(contractParty2);

        contractParty2 = getContractPartySample2();
        assertThat(contractParty1).isNotEqualTo(contractParty2);
    }

    @Test
    void reviewTest() {
        ContractParty contractParty = getContractPartyRandomSampleGenerator();
        ContractReview contractReviewBack = getContractReviewRandomSampleGenerator();

        contractParty.setReview(contractReviewBack);
        assertThat(contractParty.getReview()).isEqualTo(contractReviewBack);

        contractParty.review(null);
        assertThat(contractParty.getReview()).isNull();
    }
}
