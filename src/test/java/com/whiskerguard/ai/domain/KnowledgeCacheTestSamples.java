package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class KnowledgeCacheTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static KnowledgeCache getKnowledgeCacheSample1() {
        return new KnowledgeCache()
            .id(1L)
            .tenantId(1L)
            .knowledgeType("knowledgeType1")
            .queryKey("queryKey1")
            .sourceService("sourceService1")
            .accessCount(1L)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static KnowledgeCache getKnowledgeCacheSample2() {
        return new KnowledgeCache()
            .id(2L)
            .tenantId(2L)
            .knowledgeType("knowledgeType2")
            .queryKey("queryKey2")
            .sourceService("sourceService2")
            .accessCount(2L)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static KnowledgeCache getKnowledgeCacheRandomSampleGenerator() {
        return new KnowledgeCache()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .knowledgeType(UUID.randomUUID().toString())
            .queryKey(UUID.randomUUID().toString())
            .sourceService(UUID.randomUUID().toString())
            .accessCount(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
