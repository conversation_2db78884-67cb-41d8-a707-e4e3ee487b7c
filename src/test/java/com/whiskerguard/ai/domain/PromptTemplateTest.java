package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.PromptTemplateTestSamples.*;
import static com.whiskerguard.ai.domain.PromptTemplateVariableTestSamples.*;
import static com.whiskerguard.ai.domain.PromptTemplateVersionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class PromptTemplateTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplate.class);
        PromptTemplate promptTemplate1 = getPromptTemplateSample1();
        PromptTemplate promptTemplate2 = new PromptTemplate();
        assertThat(promptTemplate1).isNotEqualTo(promptTemplate2);

        promptTemplate2.setId(promptTemplate1.getId());
        assertThat(promptTemplate1).isEqualTo(promptTemplate2);

        promptTemplate2 = getPromptTemplateSample2();
        assertThat(promptTemplate1).isNotEqualTo(promptTemplate2);
    }

    @Test
    void promptTemplateVariableTest() {
        PromptTemplate promptTemplate = getPromptTemplateRandomSampleGenerator();
        PromptTemplateVariable promptTemplateVariableBack = getPromptTemplateVariableRandomSampleGenerator();

        promptTemplate.addPromptTemplateVariable(promptTemplateVariableBack);
        assertThat(promptTemplate.getPromptTemplateVariables()).containsOnly(promptTemplateVariableBack);
        assertThat(promptTemplateVariableBack.getPromptTemplate()).isEqualTo(promptTemplate);

        promptTemplate.removePromptTemplateVariable(promptTemplateVariableBack);
        assertThat(promptTemplate.getPromptTemplateVariables()).doesNotContain(promptTemplateVariableBack);
        assertThat(promptTemplateVariableBack.getPromptTemplate()).isNull();

        promptTemplate.promptTemplateVariables(new HashSet<>(Set.of(promptTemplateVariableBack)));
        assertThat(promptTemplate.getPromptTemplateVariables()).containsOnly(promptTemplateVariableBack);
        assertThat(promptTemplateVariableBack.getPromptTemplate()).isEqualTo(promptTemplate);

        promptTemplate.setPromptTemplateVariables(new HashSet<>());
        assertThat(promptTemplate.getPromptTemplateVariables()).doesNotContain(promptTemplateVariableBack);
        assertThat(promptTemplateVariableBack.getPromptTemplate()).isNull();
    }

    @Test
    void promptTemplateVersionTest() {
        PromptTemplate promptTemplate = getPromptTemplateRandomSampleGenerator();
        PromptTemplateVersion promptTemplateVersionBack = getPromptTemplateVersionRandomSampleGenerator();

        promptTemplate.addPromptTemplateVersion(promptTemplateVersionBack);
        assertThat(promptTemplate.getPromptTemplateVersions()).containsOnly(promptTemplateVersionBack);
        assertThat(promptTemplateVersionBack.getPromptTemplate()).isEqualTo(promptTemplate);

        promptTemplate.removePromptTemplateVersion(promptTemplateVersionBack);
        assertThat(promptTemplate.getPromptTemplateVersions()).doesNotContain(promptTemplateVersionBack);
        assertThat(promptTemplateVersionBack.getPromptTemplate()).isNull();

        promptTemplate.promptTemplateVersions(new HashSet<>(Set.of(promptTemplateVersionBack)));
        assertThat(promptTemplate.getPromptTemplateVersions()).containsOnly(promptTemplateVersionBack);
        assertThat(promptTemplateVersionBack.getPromptTemplate()).isEqualTo(promptTemplate);

        promptTemplate.setPromptTemplateVersions(new HashSet<>());
        assertThat(promptTemplate.getPromptTemplateVersions()).doesNotContain(promptTemplateVersionBack);
        assertThat(promptTemplateVersionBack.getPromptTemplate()).isNull();
    }
}
