package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.ContractClauseIssueTestSamples.*;
import static com.whiskerguard.ai.domain.ContractReviewTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractClauseIssueTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractClauseIssue.class);
        ContractClauseIssue contractClauseIssue1 = getContractClauseIssueSample1();
        ContractClauseIssue contractClauseIssue2 = new ContractClauseIssue();
        assertThat(contractClauseIssue1).isNotEqualTo(contractClauseIssue2);

        contractClauseIssue2.setId(contractClauseIssue1.getId());
        assertThat(contractClauseIssue1).isEqualTo(contractClauseIssue2);

        contractClauseIssue2 = getContractClauseIssueSample2();
        assertThat(contractClauseIssue1).isNotEqualTo(contractClauseIssue2);
    }

    @Test
    void reviewTest() {
        ContractClauseIssue contractClauseIssue = getContractClauseIssueRandomSampleGenerator();
        ContractReview contractReviewBack = getContractReviewRandomSampleGenerator();

        contractClauseIssue.setReview(contractReviewBack);
        assertThat(contractClauseIssue.getReview()).isEqualTo(contractReviewBack);

        contractClauseIssue.review(null);
        assertThat(contractClauseIssue.getReview()).isNull();
    }
}
