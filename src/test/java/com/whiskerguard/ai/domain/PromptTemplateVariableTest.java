package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.PromptTemplateTestSamples.*;
import static com.whiskerguard.ai.domain.PromptTemplateVariableTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromptTemplateVariableTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplateVariable.class);
        PromptTemplateVariable promptTemplateVariable1 = getPromptTemplateVariableSample1();
        PromptTemplateVariable promptTemplateVariable2 = new PromptTemplateVariable();
        assertThat(promptTemplateVariable1).isNotEqualTo(promptTemplateVariable2);

        promptTemplateVariable2.setId(promptTemplateVariable1.getId());
        assertThat(promptTemplateVariable1).isEqualTo(promptTemplateVariable2);

        promptTemplateVariable2 = getPromptTemplateVariableSample2();
        assertThat(promptTemplateVariable1).isNotEqualTo(promptTemplateVariable2);
    }

    @Test
    void promptTemplateTest() {
        PromptTemplateVariable promptTemplateVariable = getPromptTemplateVariableRandomSampleGenerator();
        PromptTemplate promptTemplateBack = getPromptTemplateRandomSampleGenerator();

        promptTemplateVariable.setPromptTemplate(promptTemplateBack);
        assertThat(promptTemplateVariable.getPromptTemplate()).isEqualTo(promptTemplateBack);

        promptTemplateVariable.promptTemplate(null);
        assertThat(promptTemplateVariable.getPromptTemplate()).isNull();
    }
}
