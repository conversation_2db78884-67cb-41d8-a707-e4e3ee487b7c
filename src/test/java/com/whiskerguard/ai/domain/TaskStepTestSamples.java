package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TaskStepTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static TaskStep getTaskStepSample1() {
        return new TaskStep()
            .id(1L)
            .tenantId(1L)
            .stepName("stepName1")
            .stepDescription("stepDescription1")
            .stepOrder(1)
            .errorMessage("errorMessage1")
            .executionTime(1L)
            .retryCount(1)
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static TaskStep getTaskStepSample2() {
        return new TaskStep()
            .id(2L)
            .tenantId(2L)
            .stepName("stepName2")
            .stepDescription("stepDescription2")
            .stepOrder(2)
            .errorMessage("errorMessage2")
            .executionTime(2L)
            .retryCount(2)
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static TaskStep getTaskStepRandomSampleGenerator() {
        return new TaskStep()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .stepName(UUID.randomUUID().toString())
            .stepDescription(UUID.randomUUID().toString())
            .stepOrder(intCount.incrementAndGet())
            .errorMessage(UUID.randomUUID().toString())
            .executionTime(longCount.incrementAndGet())
            .retryCount(intCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
