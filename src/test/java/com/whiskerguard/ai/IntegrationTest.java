package com.whiskerguard.ai;

import com.whiskerguard.ai.config.AsyncSyncConfiguration;
import com.whiskerguard.ai.config.EmbeddedRedis;
import com.whiskerguard.ai.config.EmbeddedSQL;
import com.whiskerguard.ai.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = { WhiskerguardAiServiceApp.class, JacksonConfiguration.class, AsyncSyncConfiguration.class })
@EmbeddedRedis
@EmbeddedSQL
public @interface IntegrationTest {
}
