package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.TenantPromptConfigAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.TenantPromptConfig;
import com.whiskerguard.ai.repository.TenantPromptConfigRepository;
import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import com.whiskerguard.ai.service.mapper.TenantPromptConfigMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TenantPromptConfigResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TenantPromptConfigResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_CONFIG_KEY = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_KEY = "BBBBBBBBBB";

    private static final String DEFAULT_CONFIG_VALUE = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_VALUE = "BBBBBBBBBB";

    private static final String DEFAULT_CONFIG_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Boolean DEFAULT_IS_ENABLED = false;
    private static final Boolean UPDATED_IS_ENABLED = true;

    private static final Integer DEFAULT_PRIORITY = 1;
    private static final Integer UPDATED_PRIORITY = 2;

    private static final Instant DEFAULT_EFFECTIVE_FROM = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EFFECTIVE_FROM = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_EFFECTIVE_TO = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EFFECTIVE_TO = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_CREATED_BY_ID = 1L;
    private static final Long UPDATED_CREATED_BY_ID = 2L;

    private static final Long DEFAULT_LAST_MODIFIED_BY_ID = 1L;
    private static final Long UPDATED_LAST_MODIFIED_BY_ID = 2L;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/tenant-prompt-configs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TenantPromptConfigRepository tenantPromptConfigRepository;

    @Autowired
    private TenantPromptConfigMapper tenantPromptConfigMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTenantPromptConfigMockMvc;

    private TenantPromptConfig tenantPromptConfig;

    private TenantPromptConfig insertedTenantPromptConfig;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantPromptConfig createEntity() {
        return new TenantPromptConfig()
            .tenantId(DEFAULT_TENANT_ID)
            .configKey(DEFAULT_CONFIG_KEY)
            .configValue(DEFAULT_CONFIG_VALUE)
            .configType(DEFAULT_CONFIG_TYPE)
            .description(DEFAULT_DESCRIPTION)
            .isEnabled(DEFAULT_IS_ENABLED)
            .priority(DEFAULT_PRIORITY)
            .effectiveFrom(DEFAULT_EFFECTIVE_FROM)
            .effectiveTo(DEFAULT_EFFECTIVE_TO)
            .createdById(DEFAULT_CREATED_BY_ID)
            .lastModifiedById(DEFAULT_LAST_MODIFIED_BY_ID)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantPromptConfig createUpdatedEntity() {
        return new TenantPromptConfig()
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .configType(UPDATED_CONFIG_TYPE)
            .description(UPDATED_DESCRIPTION)
            .isEnabled(UPDATED_IS_ENABLED)
            .priority(UPDATED_PRIORITY)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        tenantPromptConfig = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTenantPromptConfig != null) {
            tenantPromptConfigRepository.delete(insertedTenantPromptConfig);
            insertedTenantPromptConfig = null;
        }
    }

    @Test
    @Transactional
    void createTenantPromptConfig() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);
        var returnedTenantPromptConfigDTO = om.readValue(
            restTenantPromptConfigMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TenantPromptConfigDTO.class
        );

        // Validate the TenantPromptConfig in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTenantPromptConfig = tenantPromptConfigMapper.toEntity(returnedTenantPromptConfigDTO);
        assertTenantPromptConfigUpdatableFieldsEquals(
            returnedTenantPromptConfig,
            getPersistedTenantPromptConfig(returnedTenantPromptConfig)
        );

        insertedTenantPromptConfig = returnedTenantPromptConfig;
    }

    @Test
    @Transactional
    void createTenantPromptConfigWithExistingId() throws Exception {
        // Create the TenantPromptConfig with an existing ID
        tenantPromptConfig.setId(1L);
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setTenantId(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkConfigKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setConfigKey(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkConfigTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setConfigType(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsEnabledIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setIsEnabled(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setVersion(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setCreatedAt(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantPromptConfig.setIsDeleted(null);

        // Create the TenantPromptConfig, which fails.
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTenantPromptConfigs() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        // Get all the tenantPromptConfigList
        restTenantPromptConfigMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(tenantPromptConfig.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].configKey").value(hasItem(DEFAULT_CONFIG_KEY)))
            .andExpect(jsonPath("$.[*].configValue").value(hasItem(DEFAULT_CONFIG_VALUE)))
            .andExpect(jsonPath("$.[*].configType").value(hasItem(DEFAULT_CONFIG_TYPE)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].isEnabled").value(hasItem(DEFAULT_IS_ENABLED)))
            .andExpect(jsonPath("$.[*].priority").value(hasItem(DEFAULT_PRIORITY)))
            .andExpect(jsonPath("$.[*].effectiveFrom").value(hasItem(DEFAULT_EFFECTIVE_FROM.toString())))
            .andExpect(jsonPath("$.[*].effectiveTo").value(hasItem(DEFAULT_EFFECTIVE_TO.toString())))
            .andExpect(jsonPath("$.[*].createdById").value(hasItem(DEFAULT_CREATED_BY_ID.intValue())))
            .andExpect(jsonPath("$.[*].lastModifiedById").value(hasItem(DEFAULT_LAST_MODIFIED_BY_ID.intValue())))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getTenantPromptConfig() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        // Get the tenantPromptConfig
        restTenantPromptConfigMockMvc
            .perform(get(ENTITY_API_URL_ID, tenantPromptConfig.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(tenantPromptConfig.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.configKey").value(DEFAULT_CONFIG_KEY))
            .andExpect(jsonPath("$.configValue").value(DEFAULT_CONFIG_VALUE))
            .andExpect(jsonPath("$.configType").value(DEFAULT_CONFIG_TYPE))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.isEnabled").value(DEFAULT_IS_ENABLED))
            .andExpect(jsonPath("$.priority").value(DEFAULT_PRIORITY))
            .andExpect(jsonPath("$.effectiveFrom").value(DEFAULT_EFFECTIVE_FROM.toString()))
            .andExpect(jsonPath("$.effectiveTo").value(DEFAULT_EFFECTIVE_TO.toString()))
            .andExpect(jsonPath("$.createdById").value(DEFAULT_CREATED_BY_ID.intValue()))
            .andExpect(jsonPath("$.lastModifiedById").value(DEFAULT_LAST_MODIFIED_BY_ID.intValue()))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTenantPromptConfig() throws Exception {
        // Get the tenantPromptConfig
        restTenantPromptConfigMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTenantPromptConfig() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantPromptConfig
        TenantPromptConfig updatedTenantPromptConfig = tenantPromptConfigRepository.findById(tenantPromptConfig.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTenantPromptConfig are not directly saved in db
        em.detach(updatedTenantPromptConfig);
        updatedTenantPromptConfig
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .configType(UPDATED_CONFIG_TYPE)
            .description(UPDATED_DESCRIPTION)
            .isEnabled(UPDATED_IS_ENABLED)
            .priority(UPDATED_PRIORITY)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(updatedTenantPromptConfig);

        restTenantPromptConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantPromptConfigDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantPromptConfigDTO))
            )
            .andExpect(status().isOk());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTenantPromptConfigToMatchAllProperties(updatedTenantPromptConfig);
    }

    @Test
    @Transactional
    void putNonExistingTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantPromptConfigDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantPromptConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantPromptConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTenantPromptConfigWithPatch() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantPromptConfig using partial update
        TenantPromptConfig partialUpdatedTenantPromptConfig = new TenantPromptConfig();
        partialUpdatedTenantPromptConfig.setId(tenantPromptConfig.getId());

        partialUpdatedTenantPromptConfig
            .tenantId(UPDATED_TENANT_ID)
            .configValue(UPDATED_CONFIG_VALUE)
            .description(UPDATED_DESCRIPTION)
            .priority(UPDATED_PRIORITY)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .createdById(UPDATED_CREATED_BY_ID)
            .version(UPDATED_VERSION)
            .isDeleted(UPDATED_IS_DELETED);

        restTenantPromptConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantPromptConfig.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantPromptConfig))
            )
            .andExpect(status().isOk());

        // Validate the TenantPromptConfig in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantPromptConfigUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTenantPromptConfig, tenantPromptConfig),
            getPersistedTenantPromptConfig(tenantPromptConfig)
        );
    }

    @Test
    @Transactional
    void fullUpdateTenantPromptConfigWithPatch() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantPromptConfig using partial update
        TenantPromptConfig partialUpdatedTenantPromptConfig = new TenantPromptConfig();
        partialUpdatedTenantPromptConfig.setId(tenantPromptConfig.getId());

        partialUpdatedTenantPromptConfig
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .configType(UPDATED_CONFIG_TYPE)
            .description(UPDATED_DESCRIPTION)
            .isEnabled(UPDATED_IS_ENABLED)
            .priority(UPDATED_PRIORITY)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTenantPromptConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantPromptConfig.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantPromptConfig))
            )
            .andExpect(status().isOk());

        // Validate the TenantPromptConfig in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantPromptConfigUpdatableFieldsEquals(
            partialUpdatedTenantPromptConfig,
            getPersistedTenantPromptConfig(partialUpdatedTenantPromptConfig)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, tenantPromptConfigDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantPromptConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantPromptConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTenantPromptConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantPromptConfig.setId(longCount.incrementAndGet());

        // Create the TenantPromptConfig
        TenantPromptConfigDTO tenantPromptConfigDTO = tenantPromptConfigMapper.toDto(tenantPromptConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantPromptConfigMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(tenantPromptConfigDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantPromptConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTenantPromptConfig() throws Exception {
        // Initialize the database
        insertedTenantPromptConfig = tenantPromptConfigRepository.saveAndFlush(tenantPromptConfig);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the tenantPromptConfig
        restTenantPromptConfigMockMvc
            .perform(delete(ENTITY_API_URL_ID, tenantPromptConfig.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return tenantPromptConfigRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TenantPromptConfig getPersistedTenantPromptConfig(TenantPromptConfig tenantPromptConfig) {
        return tenantPromptConfigRepository.findById(tenantPromptConfig.getId()).orElseThrow();
    }

    protected void assertPersistedTenantPromptConfigToMatchAllProperties(TenantPromptConfig expectedTenantPromptConfig) {
        assertTenantPromptConfigAllPropertiesEquals(expectedTenantPromptConfig, getPersistedTenantPromptConfig(expectedTenantPromptConfig));
    }

    protected void assertPersistedTenantPromptConfigToMatchUpdatableProperties(TenantPromptConfig expectedTenantPromptConfig) {
        assertTenantPromptConfigAllUpdatablePropertiesEquals(
            expectedTenantPromptConfig,
            getPersistedTenantPromptConfig(expectedTenantPromptConfig)
        );
    }
}
