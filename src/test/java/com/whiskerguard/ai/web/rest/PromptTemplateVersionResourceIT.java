package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.PromptTemplateVersionAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PromptTemplateVersion;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.repository.PromptTemplateVersionRepository;
import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateVersionMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PromptTemplateVersionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PromptTemplateVersionResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Integer DEFAULT_VERSION_NUMBER = 1;
    private static final Integer UPDATED_VERSION_NUMBER = 2;

    private static final String DEFAULT_VERSION_NAME = "AAAAAAAAAA";
    private static final String UPDATED_VERSION_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final String DEFAULT_VARIABLES_DEFINITION = "AAAAAAAAAA";
    private static final String UPDATED_VARIABLES_DEFINITION = "BBBBBBBBBB";

    private static final PromptTemplateStatus DEFAULT_STATUS = PromptTemplateStatus.DRAFT;
    private static final PromptTemplateStatus UPDATED_STATUS = PromptTemplateStatus.TESTING;

    private static final Boolean DEFAULT_IS_ACTIVE = false;
    private static final Boolean UPDATED_IS_ACTIVE = true;

    private static final Long DEFAULT_CREATED_BY_ID = 1L;
    private static final Long UPDATED_CREATED_BY_ID = 2L;

    private static final String DEFAULT_CHANGE_LOG = "AAAAAAAAAA";
    private static final String UPDATED_CHANGE_LOG = "BBBBBBBBBB";

    private static final Long DEFAULT_USAGE_COUNT = 1L;
    private static final Long UPDATED_USAGE_COUNT = 2L;

    private static final Instant DEFAULT_LAST_USED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_LAST_USED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Double DEFAULT_PERFORMANCE_SCORE = 1D;
    private static final Double UPDATED_PERFORMANCE_SCORE = 2D;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/prompt-template-versions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PromptTemplateVersionRepository promptTemplateVersionRepository;

    @Autowired
    private PromptTemplateVersionMapper promptTemplateVersionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPromptTemplateVersionMockMvc;

    private PromptTemplateVersion promptTemplateVersion;

    private PromptTemplateVersion insertedPromptTemplateVersion;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplateVersion createEntity() {
        return new PromptTemplateVersion()
            .tenantId(DEFAULT_TENANT_ID)
            .versionNumber(DEFAULT_VERSION_NUMBER)
            .versionName(DEFAULT_VERSION_NAME)
            .description(DEFAULT_DESCRIPTION)
            .content(DEFAULT_CONTENT)
            .variablesDefinition(DEFAULT_VARIABLES_DEFINITION)
            .status(DEFAULT_STATUS)
            .isActive(DEFAULT_IS_ACTIVE)
            .createdById(DEFAULT_CREATED_BY_ID)
            .changeLog(DEFAULT_CHANGE_LOG)
            .usageCount(DEFAULT_USAGE_COUNT)
            .lastUsedAt(DEFAULT_LAST_USED_AT)
            .performanceScore(DEFAULT_PERFORMANCE_SCORE)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplateVersion createUpdatedEntity() {
        return new PromptTemplateVersion()
            .tenantId(UPDATED_TENANT_ID)
            .versionNumber(UPDATED_VERSION_NUMBER)
            .versionName(UPDATED_VERSION_NAME)
            .description(UPDATED_DESCRIPTION)
            .content(UPDATED_CONTENT)
            .variablesDefinition(UPDATED_VARIABLES_DEFINITION)
            .status(UPDATED_STATUS)
            .isActive(UPDATED_IS_ACTIVE)
            .createdById(UPDATED_CREATED_BY_ID)
            .changeLog(UPDATED_CHANGE_LOG)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .performanceScore(UPDATED_PERFORMANCE_SCORE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        promptTemplateVersion = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPromptTemplateVersion != null) {
            promptTemplateVersionRepository.delete(insertedPromptTemplateVersion);
            insertedPromptTemplateVersion = null;
        }
    }

    @Test
    @Transactional
    void createPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);
        var returnedPromptTemplateVersionDTO = om.readValue(
            restPromptTemplateVersionMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PromptTemplateVersionDTO.class
        );

        // Validate the PromptTemplateVersion in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPromptTemplateVersion = promptTemplateVersionMapper.toEntity(returnedPromptTemplateVersionDTO);
        assertPromptTemplateVersionUpdatableFieldsEquals(
            returnedPromptTemplateVersion,
            getPersistedPromptTemplateVersion(returnedPromptTemplateVersion)
        );

        insertedPromptTemplateVersion = returnedPromptTemplateVersion;
    }

    @Test
    @Transactional
    void createPromptTemplateVersionWithExistingId() throws Exception {
        // Create the PromptTemplateVersion with an existing ID
        promptTemplateVersion.setId(1L);
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setTenantId(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionNumberIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setVersionNumber(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setStatus(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsActiveIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setIsActive(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setVersion(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setCreatedAt(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVersion.setIsDeleted(null);

        // Create the PromptTemplateVersion, which fails.
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPromptTemplateVersions() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        // Get all the promptTemplateVersionList
        restPromptTemplateVersionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(promptTemplateVersion.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].versionNumber").value(hasItem(DEFAULT_VERSION_NUMBER)))
            .andExpect(jsonPath("$.[*].versionName").value(hasItem(DEFAULT_VERSION_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].variablesDefinition").value(hasItem(DEFAULT_VARIABLES_DEFINITION)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].isActive").value(hasItem(DEFAULT_IS_ACTIVE)))
            .andExpect(jsonPath("$.[*].createdById").value(hasItem(DEFAULT_CREATED_BY_ID.intValue())))
            .andExpect(jsonPath("$.[*].changeLog").value(hasItem(DEFAULT_CHANGE_LOG)))
            .andExpect(jsonPath("$.[*].usageCount").value(hasItem(DEFAULT_USAGE_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].lastUsedAt").value(hasItem(DEFAULT_LAST_USED_AT.toString())))
            .andExpect(jsonPath("$.[*].performanceScore").value(hasItem(DEFAULT_PERFORMANCE_SCORE)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getPromptTemplateVersion() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        // Get the promptTemplateVersion
        restPromptTemplateVersionMockMvc
            .perform(get(ENTITY_API_URL_ID, promptTemplateVersion.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(promptTemplateVersion.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.versionNumber").value(DEFAULT_VERSION_NUMBER))
            .andExpect(jsonPath("$.versionName").value(DEFAULT_VERSION_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.variablesDefinition").value(DEFAULT_VARIABLES_DEFINITION))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.isActive").value(DEFAULT_IS_ACTIVE))
            .andExpect(jsonPath("$.createdById").value(DEFAULT_CREATED_BY_ID.intValue()))
            .andExpect(jsonPath("$.changeLog").value(DEFAULT_CHANGE_LOG))
            .andExpect(jsonPath("$.usageCount").value(DEFAULT_USAGE_COUNT.intValue()))
            .andExpect(jsonPath("$.lastUsedAt").value(DEFAULT_LAST_USED_AT.toString()))
            .andExpect(jsonPath("$.performanceScore").value(DEFAULT_PERFORMANCE_SCORE))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingPromptTemplateVersion() throws Exception {
        // Get the promptTemplateVersion
        restPromptTemplateVersionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPromptTemplateVersion() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVersion
        PromptTemplateVersion updatedPromptTemplateVersion = promptTemplateVersionRepository
            .findById(promptTemplateVersion.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedPromptTemplateVersion are not directly saved in db
        em.detach(updatedPromptTemplateVersion);
        updatedPromptTemplateVersion
            .tenantId(UPDATED_TENANT_ID)
            .versionNumber(UPDATED_VERSION_NUMBER)
            .versionName(UPDATED_VERSION_NAME)
            .description(UPDATED_DESCRIPTION)
            .content(UPDATED_CONTENT)
            .variablesDefinition(UPDATED_VARIABLES_DEFINITION)
            .status(UPDATED_STATUS)
            .isActive(UPDATED_IS_ACTIVE)
            .createdById(UPDATED_CREATED_BY_ID)
            .changeLog(UPDATED_CHANGE_LOG)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .performanceScore(UPDATED_PERFORMANCE_SCORE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(updatedPromptTemplateVersion);

        restPromptTemplateVersionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateVersionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPromptTemplateVersionToMatchAllProperties(updatedPromptTemplateVersion);
    }

    @Test
    @Transactional
    void putNonExistingPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateVersionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVersionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePromptTemplateVersionWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVersion using partial update
        PromptTemplateVersion partialUpdatedPromptTemplateVersion = new PromptTemplateVersion();
        partialUpdatedPromptTemplateVersion.setId(promptTemplateVersion.getId());

        partialUpdatedPromptTemplateVersion
            .versionNumber(UPDATED_VERSION_NUMBER)
            .versionName(UPDATED_VERSION_NAME)
            .description(UPDATED_DESCRIPTION)
            .variablesDefinition(UPDATED_VARIABLES_DEFINITION)
            .isActive(UPDATED_IS_ACTIVE)
            .createdById(UPDATED_CREATED_BY_ID)
            .changeLog(UPDATED_CHANGE_LOG)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .performanceScore(UPDATED_PERFORMANCE_SCORE)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT);

        restPromptTemplateVersionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplateVersion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplateVersion))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVersion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateVersionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPromptTemplateVersion, promptTemplateVersion),
            getPersistedPromptTemplateVersion(promptTemplateVersion)
        );
    }

    @Test
    @Transactional
    void fullUpdatePromptTemplateVersionWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVersion using partial update
        PromptTemplateVersion partialUpdatedPromptTemplateVersion = new PromptTemplateVersion();
        partialUpdatedPromptTemplateVersion.setId(promptTemplateVersion.getId());

        partialUpdatedPromptTemplateVersion
            .tenantId(UPDATED_TENANT_ID)
            .versionNumber(UPDATED_VERSION_NUMBER)
            .versionName(UPDATED_VERSION_NAME)
            .description(UPDATED_DESCRIPTION)
            .content(UPDATED_CONTENT)
            .variablesDefinition(UPDATED_VARIABLES_DEFINITION)
            .status(UPDATED_STATUS)
            .isActive(UPDATED_IS_ACTIVE)
            .createdById(UPDATED_CREATED_BY_ID)
            .changeLog(UPDATED_CHANGE_LOG)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .performanceScore(UPDATED_PERFORMANCE_SCORE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restPromptTemplateVersionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplateVersion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplateVersion))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVersion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateVersionUpdatableFieldsEquals(
            partialUpdatedPromptTemplateVersion,
            getPersistedPromptTemplateVersion(partialUpdatedPromptTemplateVersion)
        );
    }

    @Test
    @Transactional
    void patchNonExistingPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, promptTemplateVersionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPromptTemplateVersion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVersion.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVersion
        PromptTemplateVersionDTO promptTemplateVersionDTO = promptTemplateVersionMapper.toDto(promptTemplateVersion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVersionMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(promptTemplateVersionDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplateVersion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePromptTemplateVersion() throws Exception {
        // Initialize the database
        insertedPromptTemplateVersion = promptTemplateVersionRepository.saveAndFlush(promptTemplateVersion);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the promptTemplateVersion
        restPromptTemplateVersionMockMvc
            .perform(delete(ENTITY_API_URL_ID, promptTemplateVersion.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return promptTemplateVersionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PromptTemplateVersion getPersistedPromptTemplateVersion(PromptTemplateVersion promptTemplateVersion) {
        return promptTemplateVersionRepository.findById(promptTemplateVersion.getId()).orElseThrow();
    }

    protected void assertPersistedPromptTemplateVersionToMatchAllProperties(PromptTemplateVersion expectedPromptTemplateVersion) {
        assertPromptTemplateVersionAllPropertiesEquals(
            expectedPromptTemplateVersion,
            getPersistedPromptTemplateVersion(expectedPromptTemplateVersion)
        );
    }

    protected void assertPersistedPromptTemplateVersionToMatchUpdatableProperties(PromptTemplateVersion expectedPromptTemplateVersion) {
        assertPromptTemplateVersionAllUpdatablePropertiesEquals(
            expectedPromptTemplateVersion,
            getPersistedPromptTemplateVersion(expectedPromptTemplateVersion)
        );
    }
}
