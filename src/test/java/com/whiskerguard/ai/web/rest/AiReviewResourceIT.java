package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AiReviewAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AiReview;
import com.whiskerguard.ai.domain.enumeration.ReviewResult;
import com.whiskerguard.ai.repository.AiReviewRepository;
import com.whiskerguard.ai.service.dto.AiReviewDTO;
import com.whiskerguard.ai.service.mapper.AiReviewMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AiReviewResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AiReviewResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_REVIEW_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_REVIEW_CONTENT = "BBBBBBBBBB";

    private static final ReviewResult DEFAULT_REVIEW_RESULT = ReviewResult.APPROVED;
    private static final ReviewResult UPDATED_REVIEW_RESULT = ReviewResult.REJECTED;

    private static final Instant DEFAULT_REVIEW_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_REVIEW_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_REVIEWER = "AAAAAAAAAA";
    private static final String UPDATED_REVIEWER = "BBBBBBBBBB";

    private static final String DEFAULT_COMMENTS = "AAAAAAAAAA";
    private static final String UPDATED_COMMENTS = "BBBBBBBBBB";

    private static final String DEFAULT_FEEDBACK = "AAAAAAAAAA";
    private static final String UPDATED_FEEDBACK = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/ai-reviews";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AiReviewRepository aiReviewRepository;

    @Autowired
    private AiReviewMapper aiReviewMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAiReviewMockMvc;

    private AiReview aiReview;

    private AiReview insertedAiReview;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiReview createEntity() {
        return new AiReview()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .reviewContent(DEFAULT_REVIEW_CONTENT)
            .reviewResult(DEFAULT_REVIEW_RESULT)
            .reviewDate(DEFAULT_REVIEW_DATE)
            .reviewer(DEFAULT_REVIEWER)
            .comments(DEFAULT_COMMENTS)
            .feedback(DEFAULT_FEEDBACK)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiReview createUpdatedEntity() {
        return new AiReview()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .reviewContent(UPDATED_REVIEW_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .reviewDate(UPDATED_REVIEW_DATE)
            .reviewer(UPDATED_REVIEWER)
            .comments(UPDATED_COMMENTS)
            .feedback(UPDATED_FEEDBACK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        aiReview = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAiReview != null) {
            aiReviewRepository.delete(insertedAiReview);
            insertedAiReview = null;
        }
    }

    @Test
    @Transactional
    void createAiReview() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);
        var returnedAiReviewDTO = om.readValue(
            restAiReviewMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AiReviewDTO.class
        );

        // Validate the AiReview in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAiReview = aiReviewMapper.toEntity(returnedAiReviewDTO);
        assertAiReviewUpdatableFieldsEquals(returnedAiReview, getPersistedAiReview(returnedAiReview));

        insertedAiReview = returnedAiReview;
    }

    @Test
    @Transactional
    void createAiReviewWithExistingId() throws Exception {
        // Create the AiReview with an existing ID
        aiReview.setId(1L);
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setTenantId(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setEmployeeId(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReviewContentIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setReviewContent(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReviewResultIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setReviewResult(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReviewDateIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setReviewDate(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkReviewerIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setReviewer(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setVersion(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setCreatedAt(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setUpdatedAt(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiReview.setIsDeleted(null);

        // Create the AiReview, which fails.
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        restAiReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAiReviews() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        // Get all the aiReviewList
        restAiReviewMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(aiReview.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].reviewContent").value(hasItem(DEFAULT_REVIEW_CONTENT)))
            .andExpect(jsonPath("$.[*].reviewResult").value(hasItem(DEFAULT_REVIEW_RESULT.toString())))
            .andExpect(jsonPath("$.[*].reviewDate").value(hasItem(DEFAULT_REVIEW_DATE.toString())))
            .andExpect(jsonPath("$.[*].reviewer").value(hasItem(DEFAULT_REVIEWER)))
            .andExpect(jsonPath("$.[*].comments").value(hasItem(DEFAULT_COMMENTS)))
            .andExpect(jsonPath("$.[*].feedback").value(hasItem(DEFAULT_FEEDBACK)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAiReview() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        // Get the aiReview
        restAiReviewMockMvc
            .perform(get(ENTITY_API_URL_ID, aiReview.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(aiReview.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.reviewContent").value(DEFAULT_REVIEW_CONTENT))
            .andExpect(jsonPath("$.reviewResult").value(DEFAULT_REVIEW_RESULT.toString()))
            .andExpect(jsonPath("$.reviewDate").value(DEFAULT_REVIEW_DATE.toString()))
            .andExpect(jsonPath("$.reviewer").value(DEFAULT_REVIEWER))
            .andExpect(jsonPath("$.comments").value(DEFAULT_COMMENTS))
            .andExpect(jsonPath("$.feedback").value(DEFAULT_FEEDBACK))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAiReview() throws Exception {
        // Get the aiReview
        restAiReviewMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAiReview() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiReview
        AiReview updatedAiReview = aiReviewRepository.findById(aiReview.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAiReview are not directly saved in db
        em.detach(updatedAiReview);
        updatedAiReview
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .reviewContent(UPDATED_REVIEW_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .reviewDate(UPDATED_REVIEW_DATE)
            .reviewer(UPDATED_REVIEWER)
            .comments(UPDATED_COMMENTS)
            .feedback(UPDATED_FEEDBACK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(updatedAiReview);

        restAiReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiReviewDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiReviewDTO))
            )
            .andExpect(status().isOk());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAiReviewToMatchAllProperties(updatedAiReview);
    }

    @Test
    @Transactional
    void putNonExistingAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiReviewDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAiReviewWithPatch() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiReview using partial update
        AiReview partialUpdatedAiReview = new AiReview();
        partialUpdatedAiReview.setId(aiReview.getId());

        partialUpdatedAiReview
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .reviewContent(UPDATED_REVIEW_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .reviewDate(UPDATED_REVIEW_DATE)
            .comments(UPDATED_COMMENTS)
            .feedback(UPDATED_FEEDBACK)
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY);

        restAiReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiReview.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiReview))
            )
            .andExpect(status().isOk());

        // Validate the AiReview in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiReviewUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedAiReview, aiReview), getPersistedAiReview(aiReview));
    }

    @Test
    @Transactional
    void fullUpdateAiReviewWithPatch() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiReview using partial update
        AiReview partialUpdatedAiReview = new AiReview();
        partialUpdatedAiReview.setId(aiReview.getId());

        partialUpdatedAiReview
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .reviewContent(UPDATED_REVIEW_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .reviewDate(UPDATED_REVIEW_DATE)
            .reviewer(UPDATED_REVIEWER)
            .comments(UPDATED_COMMENTS)
            .feedback(UPDATED_FEEDBACK)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiReview.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiReview))
            )
            .andExpect(status().isOk());

        // Validate the AiReview in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiReviewUpdatableFieldsEquals(partialUpdatedAiReview, getPersistedAiReview(partialUpdatedAiReview));
    }

    @Test
    @Transactional
    void patchNonExistingAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, aiReviewDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAiReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiReview.setId(longCount.incrementAndGet());

        // Create the AiReview
        AiReviewDTO aiReviewDTO = aiReviewMapper.toDto(aiReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiReviewMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(aiReviewDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAiReview() throws Exception {
        // Initialize the database
        insertedAiReview = aiReviewRepository.saveAndFlush(aiReview);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the aiReview
        restAiReviewMockMvc
            .perform(delete(ENTITY_API_URL_ID, aiReview.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return aiReviewRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AiReview getPersistedAiReview(AiReview aiReview) {
        return aiReviewRepository.findById(aiReview.getId()).orElseThrow();
    }

    protected void assertPersistedAiReviewToMatchAllProperties(AiReview expectedAiReview) {
        assertAiReviewAllPropertiesEquals(expectedAiReview, getPersistedAiReview(expectedAiReview));
    }

    protected void assertPersistedAiReviewToMatchUpdatableProperties(AiReview expectedAiReview) {
        assertAiReviewAllUpdatablePropertiesEquals(expectedAiReview, getPersistedAiReview(expectedAiReview));
    }
}
