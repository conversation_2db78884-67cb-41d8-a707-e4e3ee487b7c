package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.PromptTemplateAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PromptTemplateResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PromptTemplateResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TEMPLATE_KEY = "AAAAAAAAAA";
    private static final String UPDATED_TEMPLATE_KEY = "BBBBBBBBBB";

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final PromptTemplateType DEFAULT_TEMPLATE_TYPE = PromptTemplateType.CONTRACT_REVIEW;
    private static final PromptTemplateType UPDATED_TEMPLATE_TYPE = PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW;

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final PromptTemplateStatus DEFAULT_STATUS = PromptTemplateStatus.DRAFT;
    private static final PromptTemplateStatus UPDATED_STATUS = PromptTemplateStatus.TESTING;

    private static final Integer DEFAULT_TEMPLATE_VERSION = 1;
    private static final Integer UPDATED_TEMPLATE_VERSION = 2;

    private static final Boolean DEFAULT_IS_SYSTEM_DEFAULT = false;
    private static final Boolean UPDATED_IS_SYSTEM_DEFAULT = true;

    private static final Long DEFAULT_CREATED_BY_ID = 1L;
    private static final Long UPDATED_CREATED_BY_ID = 2L;

    private static final Long DEFAULT_LAST_MODIFIED_BY_ID = 1L;
    private static final Long UPDATED_LAST_MODIFIED_BY_ID = 2L;

    private static final Long DEFAULT_USAGE_COUNT = 1L;
    private static final Long UPDATED_USAGE_COUNT = 2L;

    private static final Instant DEFAULT_LAST_USED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_LAST_USED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/prompt-templates";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PromptTemplateRepository promptTemplateRepository;

    @Autowired
    private PromptTemplateMapper promptTemplateMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPromptTemplateMockMvc;

    private PromptTemplate promptTemplate;

    private PromptTemplate insertedPromptTemplate;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplate createEntity() {
        return new PromptTemplate()
            .tenantId(DEFAULT_TENANT_ID)
            .templateKey(DEFAULT_TEMPLATE_KEY)
            .name(DEFAULT_NAME)
            .description(DEFAULT_DESCRIPTION)
            .templateType(DEFAULT_TEMPLATE_TYPE)
            .content(DEFAULT_CONTENT)
            .status(DEFAULT_STATUS)
            .templateVersion(DEFAULT_TEMPLATE_VERSION)
            .isSystemDefault(DEFAULT_IS_SYSTEM_DEFAULT)
            .createdById(DEFAULT_CREATED_BY_ID)
            .lastModifiedById(DEFAULT_LAST_MODIFIED_BY_ID)
            .usageCount(DEFAULT_USAGE_COUNT)
            .lastUsedAt(DEFAULT_LAST_USED_AT)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplate createUpdatedEntity() {
        return new PromptTemplate()
            .tenantId(UPDATED_TENANT_ID)
            .templateKey(UPDATED_TEMPLATE_KEY)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .templateType(UPDATED_TEMPLATE_TYPE)
            .content(UPDATED_CONTENT)
            .status(UPDATED_STATUS)
            .templateVersion(UPDATED_TEMPLATE_VERSION)
            .isSystemDefault(UPDATED_IS_SYSTEM_DEFAULT)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        promptTemplate = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPromptTemplate != null) {
            promptTemplateRepository.delete(insertedPromptTemplate);
            insertedPromptTemplate = null;
        }
    }

    @Test
    @Transactional
    void createPromptTemplate() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);
        var returnedPromptTemplateDTO = om.readValue(
            restPromptTemplateMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PromptTemplateDTO.class
        );

        // Validate the PromptTemplate in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPromptTemplate = promptTemplateMapper.toEntity(returnedPromptTemplateDTO);
        assertPromptTemplateUpdatableFieldsEquals(returnedPromptTemplate, getPersistedPromptTemplate(returnedPromptTemplate));

        insertedPromptTemplate = returnedPromptTemplate;
    }

    @Test
    @Transactional
    void createPromptTemplateWithExistingId() throws Exception {
        // Create the PromptTemplate with an existing ID
        promptTemplate.setId(1L);
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTemplateKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setTemplateKey(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setName(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTemplateTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setTemplateType(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setStatus(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTemplateVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setTemplateVersion(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsSystemDefaultIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setIsSystemDefault(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setVersion(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setCreatedAt(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplate.setIsDeleted(null);

        // Create the PromptTemplate, which fails.
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        restPromptTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPromptTemplates() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        // Get all the promptTemplateList
        restPromptTemplateMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(promptTemplate.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].templateKey").value(hasItem(DEFAULT_TEMPLATE_KEY)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].templateType").value(hasItem(DEFAULT_TEMPLATE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].templateVersion").value(hasItem(DEFAULT_TEMPLATE_VERSION)))
            .andExpect(jsonPath("$.[*].isSystemDefault").value(hasItem(DEFAULT_IS_SYSTEM_DEFAULT)))
            .andExpect(jsonPath("$.[*].createdById").value(hasItem(DEFAULT_CREATED_BY_ID.intValue())))
            .andExpect(jsonPath("$.[*].lastModifiedById").value(hasItem(DEFAULT_LAST_MODIFIED_BY_ID.intValue())))
            .andExpect(jsonPath("$.[*].usageCount").value(hasItem(DEFAULT_USAGE_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].lastUsedAt").value(hasItem(DEFAULT_LAST_USED_AT.toString())))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getPromptTemplate() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        // Get the promptTemplate
        restPromptTemplateMockMvc
            .perform(get(ENTITY_API_URL_ID, promptTemplate.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(promptTemplate.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.templateKey").value(DEFAULT_TEMPLATE_KEY))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.templateType").value(DEFAULT_TEMPLATE_TYPE.toString()))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.templateVersion").value(DEFAULT_TEMPLATE_VERSION))
            .andExpect(jsonPath("$.isSystemDefault").value(DEFAULT_IS_SYSTEM_DEFAULT))
            .andExpect(jsonPath("$.createdById").value(DEFAULT_CREATED_BY_ID.intValue()))
            .andExpect(jsonPath("$.lastModifiedById").value(DEFAULT_LAST_MODIFIED_BY_ID.intValue()))
            .andExpect(jsonPath("$.usageCount").value(DEFAULT_USAGE_COUNT.intValue()))
            .andExpect(jsonPath("$.lastUsedAt").value(DEFAULT_LAST_USED_AT.toString()))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingPromptTemplate() throws Exception {
        // Get the promptTemplate
        restPromptTemplateMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPromptTemplate() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplate
        PromptTemplate updatedPromptTemplate = promptTemplateRepository.findById(promptTemplate.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedPromptTemplate are not directly saved in db
        em.detach(updatedPromptTemplate);
        updatedPromptTemplate
            .tenantId(UPDATED_TENANT_ID)
            .templateKey(UPDATED_TEMPLATE_KEY)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .templateType(UPDATED_TEMPLATE_TYPE)
            .content(UPDATED_CONTENT)
            .status(UPDATED_STATUS)
            .templateVersion(UPDATED_TEMPLATE_VERSION)
            .isSystemDefault(UPDATED_IS_SYSTEM_DEFAULT)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(updatedPromptTemplate);

        restPromptTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateDTO))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPromptTemplateToMatchAllProperties(updatedPromptTemplate);
    }

    @Test
    @Transactional
    void putNonExistingPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePromptTemplateWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplate using partial update
        PromptTemplate partialUpdatedPromptTemplate = new PromptTemplate();
        partialUpdatedPromptTemplate.setId(promptTemplate.getId());

        partialUpdatedPromptTemplate
            .tenantId(UPDATED_TENANT_ID)
            .description(UPDATED_DESCRIPTION)
            .templateType(UPDATED_TEMPLATE_TYPE)
            .content(UPDATED_CONTENT)
            .status(UPDATED_STATUS)
            .isSystemDefault(UPDATED_IS_SYSTEM_DEFAULT)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restPromptTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplate.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplate))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplate in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPromptTemplate, promptTemplate),
            getPersistedPromptTemplate(promptTemplate)
        );
    }

    @Test
    @Transactional
    void fullUpdatePromptTemplateWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplate using partial update
        PromptTemplate partialUpdatedPromptTemplate = new PromptTemplate();
        partialUpdatedPromptTemplate.setId(promptTemplate.getId());

        partialUpdatedPromptTemplate
            .tenantId(UPDATED_TENANT_ID)
            .templateKey(UPDATED_TEMPLATE_KEY)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .templateType(UPDATED_TEMPLATE_TYPE)
            .content(UPDATED_CONTENT)
            .status(UPDATED_STATUS)
            .templateVersion(UPDATED_TEMPLATE_VERSION)
            .isSystemDefault(UPDATED_IS_SYSTEM_DEFAULT)
            .createdById(UPDATED_CREATED_BY_ID)
            .lastModifiedById(UPDATED_LAST_MODIFIED_BY_ID)
            .usageCount(UPDATED_USAGE_COUNT)
            .lastUsedAt(UPDATED_LAST_USED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restPromptTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplate.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplate))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplate in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateUpdatableFieldsEquals(partialUpdatedPromptTemplate, getPersistedPromptTemplate(partialUpdatedPromptTemplate));
    }

    @Test
    @Transactional
    void patchNonExistingPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, promptTemplateDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPromptTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplate.setId(longCount.incrementAndGet());

        // Create the PromptTemplate
        PromptTemplateDTO promptTemplateDTO = promptTemplateMapper.toDto(promptTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(promptTemplateDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePromptTemplate() throws Exception {
        // Initialize the database
        insertedPromptTemplate = promptTemplateRepository.saveAndFlush(promptTemplate);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the promptTemplate
        restPromptTemplateMockMvc
            .perform(delete(ENTITY_API_URL_ID, promptTemplate.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return promptTemplateRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PromptTemplate getPersistedPromptTemplate(PromptTemplate promptTemplate) {
        return promptTemplateRepository.findById(promptTemplate.getId()).orElseThrow();
    }

    protected void assertPersistedPromptTemplateToMatchAllProperties(PromptTemplate expectedPromptTemplate) {
        assertPromptTemplateAllPropertiesEquals(expectedPromptTemplate, getPersistedPromptTemplate(expectedPromptTemplate));
    }

    protected void assertPersistedPromptTemplateToMatchUpdatableProperties(PromptTemplate expectedPromptTemplate) {
        assertPromptTemplateAllUpdatablePropertiesEquals(expectedPromptTemplate, getPersistedPromptTemplate(expectedPromptTemplate));
    }
}
