package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.KnowledgeCacheAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.KnowledgeCache;
import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.service.dto.KnowledgeCacheDTO;
import com.whiskerguard.ai.service.mapper.KnowledgeCacheMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link KnowledgeCacheResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class KnowledgeCacheResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_KNOWLEDGE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_KNOWLEDGE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_QUERY_KEY = "AAAAAAAAAA";
    private static final String UPDATED_QUERY_KEY = "BBBBBBBBBB";

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final Double DEFAULT_SIMILARITY_SCORE = 1D;
    private static final Double UPDATED_SIMILARITY_SCORE = 2D;

    private static final String DEFAULT_SOURCE_SERVICE = "AAAAAAAAAA";
    private static final String UPDATED_SOURCE_SERVICE = "BBBBBBBBBB";

    private static final Instant DEFAULT_EXPIRE_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EXPIRE_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_ACCESS_COUNT = 0L;
    private static final Long UPDATED_ACCESS_COUNT = 1L;

    private static final Instant DEFAULT_LAST_ACCESS_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_LAST_ACCESS_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/knowledge-caches";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private KnowledgeCacheRepository knowledgeCacheRepository;

    @Autowired
    private KnowledgeCacheMapper knowledgeCacheMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restKnowledgeCacheMockMvc;

    private KnowledgeCache knowledgeCache;

    private KnowledgeCache insertedKnowledgeCache;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static KnowledgeCache createEntity() {
        return new KnowledgeCache()
            .tenantId(DEFAULT_TENANT_ID)
            .knowledgeType(DEFAULT_KNOWLEDGE_TYPE)
            .queryKey(DEFAULT_QUERY_KEY)
            .content(DEFAULT_CONTENT)
            .similarityScore(DEFAULT_SIMILARITY_SCORE)
            .sourceService(DEFAULT_SOURCE_SERVICE)
            .expireTime(DEFAULT_EXPIRE_TIME)
            .accessCount(DEFAULT_ACCESS_COUNT)
            .lastAccessTime(DEFAULT_LAST_ACCESS_TIME)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static KnowledgeCache createUpdatedEntity() {
        return new KnowledgeCache()
            .tenantId(UPDATED_TENANT_ID)
            .knowledgeType(UPDATED_KNOWLEDGE_TYPE)
            .queryKey(UPDATED_QUERY_KEY)
            .content(UPDATED_CONTENT)
            .similarityScore(UPDATED_SIMILARITY_SCORE)
            .sourceService(UPDATED_SOURCE_SERVICE)
            .expireTime(UPDATED_EXPIRE_TIME)
            .accessCount(UPDATED_ACCESS_COUNT)
            .lastAccessTime(UPDATED_LAST_ACCESS_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        knowledgeCache = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedKnowledgeCache != null) {
            knowledgeCacheRepository.delete(insertedKnowledgeCache);
            insertedKnowledgeCache = null;
        }
    }

    @Test
    @Transactional
    void createKnowledgeCache() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);
        var returnedKnowledgeCacheDTO = om.readValue(
            restKnowledgeCacheMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            KnowledgeCacheDTO.class
        );

        // Validate the KnowledgeCache in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedKnowledgeCache = knowledgeCacheMapper.toEntity(returnedKnowledgeCacheDTO);
        assertKnowledgeCacheUpdatableFieldsEquals(returnedKnowledgeCache, getPersistedKnowledgeCache(returnedKnowledgeCache));

        insertedKnowledgeCache = returnedKnowledgeCache;
    }

    @Test
    @Transactional
    void createKnowledgeCacheWithExistingId() throws Exception {
        // Create the KnowledgeCache with an existing ID
        knowledgeCache.setId(1L);
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setTenantId(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkKnowledgeTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setKnowledgeType(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkQueryKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setQueryKey(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkExpireTimeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setExpireTime(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setVersion(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setCreatedAt(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setUpdatedAt(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        knowledgeCache.setIsDeleted(null);

        // Create the KnowledgeCache, which fails.
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllKnowledgeCaches() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        // Get all the knowledgeCacheList
        restKnowledgeCacheMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(knowledgeCache.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].knowledgeType").value(hasItem(DEFAULT_KNOWLEDGE_TYPE)))
            .andExpect(jsonPath("$.[*].queryKey").value(hasItem(DEFAULT_QUERY_KEY)))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].similarityScore").value(hasItem(DEFAULT_SIMILARITY_SCORE)))
            .andExpect(jsonPath("$.[*].sourceService").value(hasItem(DEFAULT_SOURCE_SERVICE)))
            .andExpect(jsonPath("$.[*].expireTime").value(hasItem(DEFAULT_EXPIRE_TIME.toString())))
            .andExpect(jsonPath("$.[*].accessCount").value(hasItem(DEFAULT_ACCESS_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].lastAccessTime").value(hasItem(DEFAULT_LAST_ACCESS_TIME.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getKnowledgeCache() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        // Get the knowledgeCache
        restKnowledgeCacheMockMvc
            .perform(get(ENTITY_API_URL_ID, knowledgeCache.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(knowledgeCache.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.knowledgeType").value(DEFAULT_KNOWLEDGE_TYPE))
            .andExpect(jsonPath("$.queryKey").value(DEFAULT_QUERY_KEY))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.similarityScore").value(DEFAULT_SIMILARITY_SCORE))
            .andExpect(jsonPath("$.sourceService").value(DEFAULT_SOURCE_SERVICE))
            .andExpect(jsonPath("$.expireTime").value(DEFAULT_EXPIRE_TIME.toString()))
            .andExpect(jsonPath("$.accessCount").value(DEFAULT_ACCESS_COUNT.intValue()))
            .andExpect(jsonPath("$.lastAccessTime").value(DEFAULT_LAST_ACCESS_TIME.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingKnowledgeCache() throws Exception {
        // Get the knowledgeCache
        restKnowledgeCacheMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingKnowledgeCache() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the knowledgeCache
        KnowledgeCache updatedKnowledgeCache = knowledgeCacheRepository.findById(knowledgeCache.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedKnowledgeCache are not directly saved in db
        em.detach(updatedKnowledgeCache);
        updatedKnowledgeCache
            .tenantId(UPDATED_TENANT_ID)
            .knowledgeType(UPDATED_KNOWLEDGE_TYPE)
            .queryKey(UPDATED_QUERY_KEY)
            .content(UPDATED_CONTENT)
            .similarityScore(UPDATED_SIMILARITY_SCORE)
            .sourceService(UPDATED_SOURCE_SERVICE)
            .expireTime(UPDATED_EXPIRE_TIME)
            .accessCount(UPDATED_ACCESS_COUNT)
            .lastAccessTime(UPDATED_LAST_ACCESS_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(updatedKnowledgeCache);

        restKnowledgeCacheMockMvc
            .perform(
                put(ENTITY_API_URL_ID, knowledgeCacheDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(knowledgeCacheDTO))
            )
            .andExpect(status().isOk());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedKnowledgeCacheToMatchAllProperties(updatedKnowledgeCache);
    }

    @Test
    @Transactional
    void putNonExistingKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(
                put(ENTITY_API_URL_ID, knowledgeCacheDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(knowledgeCacheDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(knowledgeCacheDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateKnowledgeCacheWithPatch() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the knowledgeCache using partial update
        KnowledgeCache partialUpdatedKnowledgeCache = new KnowledgeCache();
        partialUpdatedKnowledgeCache.setId(knowledgeCache.getId());

        partialUpdatedKnowledgeCache
            .knowledgeType(UPDATED_KNOWLEDGE_TYPE)
            .similarityScore(UPDATED_SIMILARITY_SCORE)
            .sourceService(UPDATED_SOURCE_SERVICE)
            .expireTime(UPDATED_EXPIRE_TIME)
            .accessCount(UPDATED_ACCESS_COUNT)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restKnowledgeCacheMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedKnowledgeCache.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedKnowledgeCache))
            )
            .andExpect(status().isOk());

        // Validate the KnowledgeCache in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertKnowledgeCacheUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedKnowledgeCache, knowledgeCache),
            getPersistedKnowledgeCache(knowledgeCache)
        );
    }

    @Test
    @Transactional
    void fullUpdateKnowledgeCacheWithPatch() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the knowledgeCache using partial update
        KnowledgeCache partialUpdatedKnowledgeCache = new KnowledgeCache();
        partialUpdatedKnowledgeCache.setId(knowledgeCache.getId());

        partialUpdatedKnowledgeCache
            .tenantId(UPDATED_TENANT_ID)
            .knowledgeType(UPDATED_KNOWLEDGE_TYPE)
            .queryKey(UPDATED_QUERY_KEY)
            .content(UPDATED_CONTENT)
            .similarityScore(UPDATED_SIMILARITY_SCORE)
            .sourceService(UPDATED_SOURCE_SERVICE)
            .expireTime(UPDATED_EXPIRE_TIME)
            .accessCount(UPDATED_ACCESS_COUNT)
            .lastAccessTime(UPDATED_LAST_ACCESS_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restKnowledgeCacheMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedKnowledgeCache.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedKnowledgeCache))
            )
            .andExpect(status().isOk());

        // Validate the KnowledgeCache in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertKnowledgeCacheUpdatableFieldsEquals(partialUpdatedKnowledgeCache, getPersistedKnowledgeCache(partialUpdatedKnowledgeCache));
    }

    @Test
    @Transactional
    void patchNonExistingKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, knowledgeCacheDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(knowledgeCacheDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(knowledgeCacheDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamKnowledgeCache() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        knowledgeCache.setId(longCount.incrementAndGet());

        // Create the KnowledgeCache
        KnowledgeCacheDTO knowledgeCacheDTO = knowledgeCacheMapper.toDto(knowledgeCache);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restKnowledgeCacheMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(knowledgeCacheDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the KnowledgeCache in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteKnowledgeCache() throws Exception {
        // Initialize the database
        insertedKnowledgeCache = knowledgeCacheRepository.saveAndFlush(knowledgeCache);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the knowledgeCache
        restKnowledgeCacheMockMvc
            .perform(delete(ENTITY_API_URL_ID, knowledgeCache.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return knowledgeCacheRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected KnowledgeCache getPersistedKnowledgeCache(KnowledgeCache knowledgeCache) {
        return knowledgeCacheRepository.findById(knowledgeCache.getId()).orElseThrow();
    }

    protected void assertPersistedKnowledgeCacheToMatchAllProperties(KnowledgeCache expectedKnowledgeCache) {
        assertKnowledgeCacheAllPropertiesEquals(expectedKnowledgeCache, getPersistedKnowledgeCache(expectedKnowledgeCache));
    }

    protected void assertPersistedKnowledgeCacheToMatchUpdatableProperties(KnowledgeCache expectedKnowledgeCache) {
        assertKnowledgeCacheAllUpdatablePropertiesEquals(expectedKnowledgeCache, getPersistedKnowledgeCache(expectedKnowledgeCache));
    }
}
