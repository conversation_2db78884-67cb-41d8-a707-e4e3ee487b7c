package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.ContractPartyAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.ContractParty;
import com.whiskerguard.ai.domain.enumeration.PartyType;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractPartyRepository;
import com.whiskerguard.ai.service.dto.ContractPartyDTO;
import com.whiskerguard.ai.service.mapper.ContractPartyMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContractPartyResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContractPartyResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_PARTY_NAME = "AAAAAAAAAA";
    private static final String UPDATED_PARTY_NAME = "BBBBBBBBBB";

    private static final PartyType DEFAULT_PARTY_TYPE = PartyType.COMPANY;
    private static final PartyType UPDATED_PARTY_TYPE = PartyType.INDIVIDUAL;

    private static final String DEFAULT_PARTY_ROLE = "AAAAAAAAAA";
    private static final String UPDATED_PARTY_ROLE = "BBBBBBBBBB";

    private static final String DEFAULT_CREDIT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CREDIT_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_REGISTERED_ADDRESS = "AAAAAAAAAA";
    private static final String UPDATED_REGISTERED_ADDRESS = "BBBBBBBBBB";

    private static final String DEFAULT_LEGAL_REPRESENTATIVE = "AAAAAAAAAA";
    private static final String UPDATED_LEGAL_REPRESENTATIVE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTACT_INFO = "AAAAAAAAAA";
    private static final String UPDATED_CONTACT_INFO = "BBBBBBBBBB";

    private static final RiskLevel DEFAULT_RISK_LEVEL = RiskLevel.HIGH;
    private static final RiskLevel UPDATED_RISK_LEVEL = RiskLevel.MEDIUM;

    private static final String DEFAULT_RISK_FACTORS = "AAAAAAAAAA";
    private static final String UPDATED_RISK_FACTORS = "BBBBBBBBBB";

    private static final String DEFAULT_COMPLIANCE_ISSUES = "AAAAAAAAAA";
    private static final String UPDATED_COMPLIANCE_ISSUES = "BBBBBBBBBB";

    private static final String DEFAULT_TIANYANCHA_INFO = "AAAAAAAAAA";
    private static final String UPDATED_TIANYANCHA_INFO = "BBBBBBBBBB";

    private static final String DEFAULT_ADDITIONAL_INFO = "AAAAAAAAAA";
    private static final String UPDATED_ADDITIONAL_INFO = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/contract-parties";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContractPartyRepository contractPartyRepository;

    @Autowired
    private ContractPartyMapper contractPartyMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContractPartyMockMvc;

    private ContractParty contractParty;

    private ContractParty insertedContractParty;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractParty createEntity() {
        return new ContractParty()
            .tenantId(DEFAULT_TENANT_ID)
            .partyName(DEFAULT_PARTY_NAME)
            .partyType(DEFAULT_PARTY_TYPE)
            .partyRole(DEFAULT_PARTY_ROLE)
            .creditCode(DEFAULT_CREDIT_CODE)
            .registeredAddress(DEFAULT_REGISTERED_ADDRESS)
            .legalRepresentative(DEFAULT_LEGAL_REPRESENTATIVE)
            .contactInfo(DEFAULT_CONTACT_INFO)
            .riskLevel(DEFAULT_RISK_LEVEL)
            .riskFactors(DEFAULT_RISK_FACTORS)
            .complianceIssues(DEFAULT_COMPLIANCE_ISSUES)
            .tianyanchaInfo(DEFAULT_TIANYANCHA_INFO)
            .additionalInfo(DEFAULT_ADDITIONAL_INFO)
            .version(DEFAULT_VERSION)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractParty createUpdatedEntity() {
        return new ContractParty()
            .tenantId(UPDATED_TENANT_ID)
            .partyName(UPDATED_PARTY_NAME)
            .partyType(UPDATED_PARTY_TYPE)
            .partyRole(UPDATED_PARTY_ROLE)
            .creditCode(UPDATED_CREDIT_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .legalRepresentative(UPDATED_LEGAL_REPRESENTATIVE)
            .contactInfo(UPDATED_CONTACT_INFO)
            .riskLevel(UPDATED_RISK_LEVEL)
            .riskFactors(UPDATED_RISK_FACTORS)
            .complianceIssues(UPDATED_COMPLIANCE_ISSUES)
            .tianyanchaInfo(UPDATED_TIANYANCHA_INFO)
            .additionalInfo(UPDATED_ADDITIONAL_INFO)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        contractParty = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContractParty != null) {
            contractPartyRepository.delete(insertedContractParty);
            insertedContractParty = null;
        }
    }

    @Test
    @Transactional
    void createContractParty() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);
        var returnedContractPartyDTO = om.readValue(
            restContractPartyMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContractPartyDTO.class
        );

        // Validate the ContractParty in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContractParty = contractPartyMapper.toEntity(returnedContractPartyDTO);
        assertContractPartyUpdatableFieldsEquals(returnedContractParty, getPersistedContractParty(returnedContractParty));

        insertedContractParty = returnedContractParty;
    }

    @Test
    @Transactional
    void createContractPartyWithExistingId() throws Exception {
        // Create the ContractParty with an existing ID
        contractParty.setId(1L);
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setTenantId(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPartyNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setPartyName(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPartyTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setPartyType(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setVersion(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setCreatedAt(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractParty.setIsDeleted(null);

        // Create the ContractParty, which fails.
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        restContractPartyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContractParties() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        // Get all the contractPartyList
        restContractPartyMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(contractParty.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].partyName").value(hasItem(DEFAULT_PARTY_NAME)))
            .andExpect(jsonPath("$.[*].partyType").value(hasItem(DEFAULT_PARTY_TYPE.toString())))
            .andExpect(jsonPath("$.[*].partyRole").value(hasItem(DEFAULT_PARTY_ROLE)))
            .andExpect(jsonPath("$.[*].creditCode").value(hasItem(DEFAULT_CREDIT_CODE)))
            .andExpect(jsonPath("$.[*].registeredAddress").value(hasItem(DEFAULT_REGISTERED_ADDRESS)))
            .andExpect(jsonPath("$.[*].legalRepresentative").value(hasItem(DEFAULT_LEGAL_REPRESENTATIVE)))
            .andExpect(jsonPath("$.[*].contactInfo").value(hasItem(DEFAULT_CONTACT_INFO)))
            .andExpect(jsonPath("$.[*].riskLevel").value(hasItem(DEFAULT_RISK_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].riskFactors").value(hasItem(DEFAULT_RISK_FACTORS)))
            .andExpect(jsonPath("$.[*].complianceIssues").value(hasItem(DEFAULT_COMPLIANCE_ISSUES)))
            .andExpect(jsonPath("$.[*].tianyanchaInfo").value(hasItem(DEFAULT_TIANYANCHA_INFO)))
            .andExpect(jsonPath("$.[*].additionalInfo").value(hasItem(DEFAULT_ADDITIONAL_INFO)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContractParty() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        // Get the contractParty
        restContractPartyMockMvc
            .perform(get(ENTITY_API_URL_ID, contractParty.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(contractParty.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.partyName").value(DEFAULT_PARTY_NAME))
            .andExpect(jsonPath("$.partyType").value(DEFAULT_PARTY_TYPE.toString()))
            .andExpect(jsonPath("$.partyRole").value(DEFAULT_PARTY_ROLE))
            .andExpect(jsonPath("$.creditCode").value(DEFAULT_CREDIT_CODE))
            .andExpect(jsonPath("$.registeredAddress").value(DEFAULT_REGISTERED_ADDRESS))
            .andExpect(jsonPath("$.legalRepresentative").value(DEFAULT_LEGAL_REPRESENTATIVE))
            .andExpect(jsonPath("$.contactInfo").value(DEFAULT_CONTACT_INFO))
            .andExpect(jsonPath("$.riskLevel").value(DEFAULT_RISK_LEVEL.toString()))
            .andExpect(jsonPath("$.riskFactors").value(DEFAULT_RISK_FACTORS))
            .andExpect(jsonPath("$.complianceIssues").value(DEFAULT_COMPLIANCE_ISSUES))
            .andExpect(jsonPath("$.tianyanchaInfo").value(DEFAULT_TIANYANCHA_INFO))
            .andExpect(jsonPath("$.additionalInfo").value(DEFAULT_ADDITIONAL_INFO))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContractParty() throws Exception {
        // Get the contractParty
        restContractPartyMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContractParty() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractParty
        ContractParty updatedContractParty = contractPartyRepository.findById(contractParty.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedContractParty are not directly saved in db
        em.detach(updatedContractParty);
        updatedContractParty
            .tenantId(UPDATED_TENANT_ID)
            .partyName(UPDATED_PARTY_NAME)
            .partyType(UPDATED_PARTY_TYPE)
            .partyRole(UPDATED_PARTY_ROLE)
            .creditCode(UPDATED_CREDIT_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .legalRepresentative(UPDATED_LEGAL_REPRESENTATIVE)
            .contactInfo(UPDATED_CONTACT_INFO)
            .riskLevel(UPDATED_RISK_LEVEL)
            .riskFactors(UPDATED_RISK_FACTORS)
            .complianceIssues(UPDATED_COMPLIANCE_ISSUES)
            .tianyanchaInfo(UPDATED_TIANYANCHA_INFO)
            .additionalInfo(UPDATED_ADDITIONAL_INFO)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(updatedContractParty);

        restContractPartyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractPartyDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractPartyDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContractPartyToMatchAllProperties(updatedContractParty);
    }

    @Test
    @Transactional
    void putNonExistingContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractPartyDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractPartyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractPartyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContractPartyWithPatch() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractParty using partial update
        ContractParty partialUpdatedContractParty = new ContractParty();
        partialUpdatedContractParty.setId(contractParty.getId());

        partialUpdatedContractParty
            .tenantId(UPDATED_TENANT_ID)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .legalRepresentative(UPDATED_LEGAL_REPRESENTATIVE)
            .riskFactors(UPDATED_RISK_FACTORS)
            .complianceIssues(UPDATED_COMPLIANCE_ISSUES)
            .additionalInfo(UPDATED_ADDITIONAL_INFO)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT);

        restContractPartyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractParty.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractParty))
            )
            .andExpect(status().isOk());

        // Validate the ContractParty in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractPartyUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContractParty, contractParty),
            getPersistedContractParty(contractParty)
        );
    }

    @Test
    @Transactional
    void fullUpdateContractPartyWithPatch() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractParty using partial update
        ContractParty partialUpdatedContractParty = new ContractParty();
        partialUpdatedContractParty.setId(contractParty.getId());

        partialUpdatedContractParty
            .tenantId(UPDATED_TENANT_ID)
            .partyName(UPDATED_PARTY_NAME)
            .partyType(UPDATED_PARTY_TYPE)
            .partyRole(UPDATED_PARTY_ROLE)
            .creditCode(UPDATED_CREDIT_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .legalRepresentative(UPDATED_LEGAL_REPRESENTATIVE)
            .contactInfo(UPDATED_CONTACT_INFO)
            .riskLevel(UPDATED_RISK_LEVEL)
            .riskFactors(UPDATED_RISK_FACTORS)
            .complianceIssues(UPDATED_COMPLIANCE_ISSUES)
            .tianyanchaInfo(UPDATED_TIANYANCHA_INFO)
            .additionalInfo(UPDATED_ADDITIONAL_INFO)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractPartyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractParty.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractParty))
            )
            .andExpect(status().isOk());

        // Validate the ContractParty in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractPartyUpdatableFieldsEquals(partialUpdatedContractParty, getPersistedContractParty(partialUpdatedContractParty));
    }

    @Test
    @Transactional
    void patchNonExistingContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, contractPartyDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractPartyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractPartyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContractParty() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractParty.setId(longCount.incrementAndGet());

        // Create the ContractParty
        ContractPartyDTO contractPartyDTO = contractPartyMapper.toDto(contractParty);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractPartyMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(contractPartyDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractParty in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContractParty() throws Exception {
        // Initialize the database
        insertedContractParty = contractPartyRepository.saveAndFlush(contractParty);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the contractParty
        restContractPartyMockMvc
            .perform(delete(ENTITY_API_URL_ID, contractParty.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return contractPartyRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContractParty getPersistedContractParty(ContractParty contractParty) {
        return contractPartyRepository.findById(contractParty.getId()).orElseThrow();
    }

    protected void assertPersistedContractPartyToMatchAllProperties(ContractParty expectedContractParty) {
        assertContractPartyAllPropertiesEquals(expectedContractParty, getPersistedContractParty(expectedContractParty));
    }

    protected void assertPersistedContractPartyToMatchUpdatableProperties(ContractParty expectedContractParty) {
        assertContractPartyAllUpdatablePropertiesEquals(expectedContractParty, getPersistedContractParty(expectedContractParty));
    }
}
