package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.PromptTemplateVariableAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.domain.enumeration.VariableType;
import com.whiskerguard.ai.repository.PromptTemplateVariableRepository;
import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import com.whiskerguard.ai.service.mapper.PromptTemplateVariableMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PromptTemplateVariableResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PromptTemplateVariableResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_VARIABLE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_VARIABLE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DISPLAY_NAME = "AAAAAAAAAA";
    private static final String UPDATED_DISPLAY_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final VariableType DEFAULT_VARIABLE_TYPE = VariableType.SYSTEM;
    private static final VariableType UPDATED_VARIABLE_TYPE = VariableType.BUSINESS;

    private static final String DEFAULT_DEFAULT_VALUE = "AAAAAAAAAA";
    private static final String UPDATED_DEFAULT_VALUE = "BBBBBBBBBB";

    private static final Boolean DEFAULT_IS_REQUIRED = false;
    private static final Boolean UPDATED_IS_REQUIRED = true;

    private static final String DEFAULT_VALIDATION_RULE = "AAAAAAAAAA";
    private static final String UPDATED_VALIDATION_RULE = "BBBBBBBBBB";

    private static final String DEFAULT_EXAMPLE_VALUE = "AAAAAAAAAA";
    private static final String UPDATED_EXAMPLE_VALUE = "BBBBBBBBBB";

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final Boolean DEFAULT_IS_ENABLED = false;
    private static final Boolean UPDATED_IS_ENABLED = true;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/prompt-template-variables";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PromptTemplateVariableRepository promptTemplateVariableRepository;

    @Autowired
    private PromptTemplateVariableMapper promptTemplateVariableMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPromptTemplateVariableMockMvc;

    private PromptTemplateVariable promptTemplateVariable;

    private PromptTemplateVariable insertedPromptTemplateVariable;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplateVariable createEntity() {
        return new PromptTemplateVariable()
            .tenantId(DEFAULT_TENANT_ID)
            .variableName(DEFAULT_VARIABLE_NAME)
            .displayName(DEFAULT_DISPLAY_NAME)
            .description(DEFAULT_DESCRIPTION)
            .variableType(DEFAULT_VARIABLE_TYPE)
            .defaultValue(DEFAULT_DEFAULT_VALUE)
            .isRequired(DEFAULT_IS_REQUIRED)
            .validationRule(DEFAULT_VALIDATION_RULE)
            .exampleValue(DEFAULT_EXAMPLE_VALUE)
            .sortOrder(DEFAULT_SORT_ORDER)
            .isEnabled(DEFAULT_IS_ENABLED)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PromptTemplateVariable createUpdatedEntity() {
        return new PromptTemplateVariable()
            .tenantId(UPDATED_TENANT_ID)
            .variableName(UPDATED_VARIABLE_NAME)
            .displayName(UPDATED_DISPLAY_NAME)
            .description(UPDATED_DESCRIPTION)
            .variableType(UPDATED_VARIABLE_TYPE)
            .defaultValue(UPDATED_DEFAULT_VALUE)
            .isRequired(UPDATED_IS_REQUIRED)
            .validationRule(UPDATED_VALIDATION_RULE)
            .exampleValue(UPDATED_EXAMPLE_VALUE)
            .sortOrder(UPDATED_SORT_ORDER)
            .isEnabled(UPDATED_IS_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        promptTemplateVariable = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPromptTemplateVariable != null) {
            promptTemplateVariableRepository.delete(insertedPromptTemplateVariable);
            insertedPromptTemplateVariable = null;
        }
    }

    @Test
    @Transactional
    void createPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);
        var returnedPromptTemplateVariableDTO = om.readValue(
            restPromptTemplateVariableMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PromptTemplateVariableDTO.class
        );

        // Validate the PromptTemplateVariable in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPromptTemplateVariable = promptTemplateVariableMapper.toEntity(returnedPromptTemplateVariableDTO);
        assertPromptTemplateVariableUpdatableFieldsEquals(
            returnedPromptTemplateVariable,
            getPersistedPromptTemplateVariable(returnedPromptTemplateVariable)
        );

        insertedPromptTemplateVariable = returnedPromptTemplateVariable;
    }

    @Test
    @Transactional
    void createPromptTemplateVariableWithExistingId() throws Exception {
        // Create the PromptTemplateVariable with an existing ID
        promptTemplateVariable.setId(1L);
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setTenantId(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVariableNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setVariableName(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDisplayNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setDisplayName(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVariableTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setVariableType(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsRequiredIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setIsRequired(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsEnabledIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setIsEnabled(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setVersion(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setCreatedAt(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        promptTemplateVariable.setIsDeleted(null);

        // Create the PromptTemplateVariable, which fails.
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPromptTemplateVariables() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        // Get all the promptTemplateVariableList
        restPromptTemplateVariableMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(promptTemplateVariable.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].variableName").value(hasItem(DEFAULT_VARIABLE_NAME)))
            .andExpect(jsonPath("$.[*].displayName").value(hasItem(DEFAULT_DISPLAY_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].variableType").value(hasItem(DEFAULT_VARIABLE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].defaultValue").value(hasItem(DEFAULT_DEFAULT_VALUE)))
            .andExpect(jsonPath("$.[*].isRequired").value(hasItem(DEFAULT_IS_REQUIRED)))
            .andExpect(jsonPath("$.[*].validationRule").value(hasItem(DEFAULT_VALIDATION_RULE)))
            .andExpect(jsonPath("$.[*].exampleValue").value(hasItem(DEFAULT_EXAMPLE_VALUE)))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].isEnabled").value(hasItem(DEFAULT_IS_ENABLED)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getPromptTemplateVariable() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        // Get the promptTemplateVariable
        restPromptTemplateVariableMockMvc
            .perform(get(ENTITY_API_URL_ID, promptTemplateVariable.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(promptTemplateVariable.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.variableName").value(DEFAULT_VARIABLE_NAME))
            .andExpect(jsonPath("$.displayName").value(DEFAULT_DISPLAY_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.variableType").value(DEFAULT_VARIABLE_TYPE.toString()))
            .andExpect(jsonPath("$.defaultValue").value(DEFAULT_DEFAULT_VALUE))
            .andExpect(jsonPath("$.isRequired").value(DEFAULT_IS_REQUIRED))
            .andExpect(jsonPath("$.validationRule").value(DEFAULT_VALIDATION_RULE))
            .andExpect(jsonPath("$.exampleValue").value(DEFAULT_EXAMPLE_VALUE))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.isEnabled").value(DEFAULT_IS_ENABLED))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingPromptTemplateVariable() throws Exception {
        // Get the promptTemplateVariable
        restPromptTemplateVariableMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPromptTemplateVariable() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVariable
        PromptTemplateVariable updatedPromptTemplateVariable = promptTemplateVariableRepository
            .findById(promptTemplateVariable.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedPromptTemplateVariable are not directly saved in db
        em.detach(updatedPromptTemplateVariable);
        updatedPromptTemplateVariable
            .tenantId(UPDATED_TENANT_ID)
            .variableName(UPDATED_VARIABLE_NAME)
            .displayName(UPDATED_DISPLAY_NAME)
            .description(UPDATED_DESCRIPTION)
            .variableType(UPDATED_VARIABLE_TYPE)
            .defaultValue(UPDATED_DEFAULT_VALUE)
            .isRequired(UPDATED_IS_REQUIRED)
            .validationRule(UPDATED_VALIDATION_RULE)
            .exampleValue(UPDATED_EXAMPLE_VALUE)
            .sortOrder(UPDATED_SORT_ORDER)
            .isEnabled(UPDATED_IS_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(updatedPromptTemplateVariable);

        restPromptTemplateVariableMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateVariableDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPromptTemplateVariableToMatchAllProperties(updatedPromptTemplateVariable);
    }

    @Test
    @Transactional
    void putNonExistingPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(
                put(ENTITY_API_URL_ID, promptTemplateVariableDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(promptTemplateVariableDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePromptTemplateVariableWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVariable using partial update
        PromptTemplateVariable partialUpdatedPromptTemplateVariable = new PromptTemplateVariable();
        partialUpdatedPromptTemplateVariable.setId(promptTemplateVariable.getId());

        partialUpdatedPromptTemplateVariable
            .variableName(UPDATED_VARIABLE_NAME)
            .description(UPDATED_DESCRIPTION)
            .defaultValue(UPDATED_DEFAULT_VALUE)
            .validationRule(UPDATED_VALIDATION_RULE)
            .isEnabled(UPDATED_IS_ENABLED)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restPromptTemplateVariableMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplateVariable.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplateVariable))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVariable in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateVariableUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPromptTemplateVariable, promptTemplateVariable),
            getPersistedPromptTemplateVariable(promptTemplateVariable)
        );
    }

    @Test
    @Transactional
    void fullUpdatePromptTemplateVariableWithPatch() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the promptTemplateVariable using partial update
        PromptTemplateVariable partialUpdatedPromptTemplateVariable = new PromptTemplateVariable();
        partialUpdatedPromptTemplateVariable.setId(promptTemplateVariable.getId());

        partialUpdatedPromptTemplateVariable
            .tenantId(UPDATED_TENANT_ID)
            .variableName(UPDATED_VARIABLE_NAME)
            .displayName(UPDATED_DISPLAY_NAME)
            .description(UPDATED_DESCRIPTION)
            .variableType(UPDATED_VARIABLE_TYPE)
            .defaultValue(UPDATED_DEFAULT_VALUE)
            .isRequired(UPDATED_IS_REQUIRED)
            .validationRule(UPDATED_VALIDATION_RULE)
            .exampleValue(UPDATED_EXAMPLE_VALUE)
            .sortOrder(UPDATED_SORT_ORDER)
            .isEnabled(UPDATED_IS_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restPromptTemplateVariableMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPromptTemplateVariable.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPromptTemplateVariable))
            )
            .andExpect(status().isOk());

        // Validate the PromptTemplateVariable in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPromptTemplateVariableUpdatableFieldsEquals(
            partialUpdatedPromptTemplateVariable,
            getPersistedPromptTemplateVariable(partialUpdatedPromptTemplateVariable)
        );
    }

    @Test
    @Transactional
    void patchNonExistingPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, promptTemplateVariableDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPromptTemplateVariable() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        promptTemplateVariable.setId(longCount.incrementAndGet());

        // Create the PromptTemplateVariable
        PromptTemplateVariableDTO promptTemplateVariableDTO = promptTemplateVariableMapper.toDto(promptTemplateVariable);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPromptTemplateVariableMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(promptTemplateVariableDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the PromptTemplateVariable in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePromptTemplateVariable() throws Exception {
        // Initialize the database
        insertedPromptTemplateVariable = promptTemplateVariableRepository.saveAndFlush(promptTemplateVariable);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the promptTemplateVariable
        restPromptTemplateVariableMockMvc
            .perform(delete(ENTITY_API_URL_ID, promptTemplateVariable.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return promptTemplateVariableRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PromptTemplateVariable getPersistedPromptTemplateVariable(PromptTemplateVariable promptTemplateVariable) {
        return promptTemplateVariableRepository.findById(promptTemplateVariable.getId()).orElseThrow();
    }

    protected void assertPersistedPromptTemplateVariableToMatchAllProperties(PromptTemplateVariable expectedPromptTemplateVariable) {
        assertPromptTemplateVariableAllPropertiesEquals(
            expectedPromptTemplateVariable,
            getPersistedPromptTemplateVariable(expectedPromptTemplateVariable)
        );
    }

    protected void assertPersistedPromptTemplateVariableToMatchUpdatableProperties(PromptTemplateVariable expectedPromptTemplateVariable) {
        assertPromptTemplateVariableAllUpdatablePropertiesEquals(
            expectedPromptTemplateVariable,
            getPersistedPromptTemplateVariable(expectedPromptTemplateVariable)
        );
    }
}
