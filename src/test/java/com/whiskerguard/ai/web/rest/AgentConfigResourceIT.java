package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AgentConfigAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AgentConfig;
import com.whiskerguard.ai.repository.AgentConfigRepository;
import com.whiskerguard.ai.service.dto.AgentConfigDTO;
import com.whiskerguard.ai.service.mapper.AgentConfigMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AgentConfigResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AgentConfigResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_CONFIG_KEY = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_KEY = "BBBBBBBBBB";

    private static final String DEFAULT_CONFIG_VALUE = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_VALUE = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_CONFIG_GROUP = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_GROUP = "BBBBBBBBBB";

    private static final Boolean DEFAULT_ENABLED = false;
    private static final Boolean UPDATED_ENABLED = true;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/agent-configs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AgentConfigRepository agentConfigRepository;

    @Autowired
    private AgentConfigMapper agentConfigMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAgentConfigMockMvc;

    private AgentConfig agentConfig;

    private AgentConfig insertedAgentConfig;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentConfig createEntity() {
        return new AgentConfig()
            .tenantId(DEFAULT_TENANT_ID)
            .configKey(DEFAULT_CONFIG_KEY)
            .configValue(DEFAULT_CONFIG_VALUE)
            .description(DEFAULT_DESCRIPTION)
            .configGroup(DEFAULT_CONFIG_GROUP)
            .enabled(DEFAULT_ENABLED)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentConfig createUpdatedEntity() {
        return new AgentConfig()
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .description(UPDATED_DESCRIPTION)
            .configGroup(UPDATED_CONFIG_GROUP)
            .enabled(UPDATED_ENABLED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        agentConfig = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAgentConfig != null) {
            agentConfigRepository.delete(insertedAgentConfig);
            insertedAgentConfig = null;
        }
    }

    @Test
    @Transactional
    void createAgentConfig() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);
        var returnedAgentConfigDTO = om.readValue(
            restAgentConfigMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AgentConfigDTO.class
        );

        // Validate the AgentConfig in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAgentConfig = agentConfigMapper.toEntity(returnedAgentConfigDTO);
        assertAgentConfigUpdatableFieldsEquals(returnedAgentConfig, getPersistedAgentConfig(returnedAgentConfig));

        insertedAgentConfig = returnedAgentConfig;
    }

    @Test
    @Transactional
    void createAgentConfigWithExistingId() throws Exception {
        // Create the AgentConfig with an existing ID
        agentConfig.setId(1L);
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setTenantId(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkConfigKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setConfigKey(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkConfigValueIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setConfigValue(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEnabledIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setEnabled(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setVersion(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setCreatedAt(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setUpdatedAt(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentConfig.setIsDeleted(null);

        // Create the AgentConfig, which fails.
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        restAgentConfigMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAgentConfigs() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        // Get all the agentConfigList
        restAgentConfigMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(agentConfig.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].configKey").value(hasItem(DEFAULT_CONFIG_KEY)))
            .andExpect(jsonPath("$.[*].configValue").value(hasItem(DEFAULT_CONFIG_VALUE)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].configGroup").value(hasItem(DEFAULT_CONFIG_GROUP)))
            .andExpect(jsonPath("$.[*].enabled").value(hasItem(DEFAULT_ENABLED)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAgentConfig() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        // Get the agentConfig
        restAgentConfigMockMvc
            .perform(get(ENTITY_API_URL_ID, agentConfig.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(agentConfig.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.configKey").value(DEFAULT_CONFIG_KEY))
            .andExpect(jsonPath("$.configValue").value(DEFAULT_CONFIG_VALUE))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.configGroup").value(DEFAULT_CONFIG_GROUP))
            .andExpect(jsonPath("$.enabled").value(DEFAULT_ENABLED))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAgentConfig() throws Exception {
        // Get the agentConfig
        restAgentConfigMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAgentConfig() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentConfig
        AgentConfig updatedAgentConfig = agentConfigRepository.findById(agentConfig.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAgentConfig are not directly saved in db
        em.detach(updatedAgentConfig);
        updatedAgentConfig
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .description(UPDATED_DESCRIPTION)
            .configGroup(UPDATED_CONFIG_GROUP)
            .enabled(UPDATED_ENABLED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(updatedAgentConfig);

        restAgentConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentConfigDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentConfigDTO))
            )
            .andExpect(status().isOk());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAgentConfigToMatchAllProperties(updatedAgentConfig);
    }

    @Test
    @Transactional
    void putNonExistingAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentConfigDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAgentConfigWithPatch() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentConfig using partial update
        AgentConfig partialUpdatedAgentConfig = new AgentConfig();
        partialUpdatedAgentConfig.setId(agentConfig.getId());

        partialUpdatedAgentConfig
            .configValue(UPDATED_CONFIG_VALUE)
            .description(UPDATED_DESCRIPTION)
            .enabled(UPDATED_ENABLED)
            .updatedBy(UPDATED_UPDATED_BY);

        restAgentConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentConfig.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentConfig))
            )
            .andExpect(status().isOk());

        // Validate the AgentConfig in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentConfigUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAgentConfig, agentConfig),
            getPersistedAgentConfig(agentConfig)
        );
    }

    @Test
    @Transactional
    void fullUpdateAgentConfigWithPatch() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentConfig using partial update
        AgentConfig partialUpdatedAgentConfig = new AgentConfig();
        partialUpdatedAgentConfig.setId(agentConfig.getId());

        partialUpdatedAgentConfig
            .tenantId(UPDATED_TENANT_ID)
            .configKey(UPDATED_CONFIG_KEY)
            .configValue(UPDATED_CONFIG_VALUE)
            .description(UPDATED_DESCRIPTION)
            .configGroup(UPDATED_CONFIG_GROUP)
            .enabled(UPDATED_ENABLED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAgentConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentConfig.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentConfig))
            )
            .andExpect(status().isOk());

        // Validate the AgentConfig in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentConfigUpdatableFieldsEquals(partialUpdatedAgentConfig, getPersistedAgentConfig(partialUpdatedAgentConfig));
    }

    @Test
    @Transactional
    void patchNonExistingAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, agentConfigDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentConfigDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAgentConfig() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentConfig.setId(longCount.incrementAndGet());

        // Create the AgentConfig
        AgentConfigDTO agentConfigDTO = agentConfigMapper.toDto(agentConfig);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentConfigMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(agentConfigDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentConfig in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAgentConfig() throws Exception {
        // Initialize the database
        insertedAgentConfig = agentConfigRepository.saveAndFlush(agentConfig);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the agentConfig
        restAgentConfigMockMvc
            .perform(delete(ENTITY_API_URL_ID, agentConfig.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return agentConfigRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AgentConfig getPersistedAgentConfig(AgentConfig agentConfig) {
        return agentConfigRepository.findById(agentConfig.getId()).orElseThrow();
    }

    protected void assertPersistedAgentConfigToMatchAllProperties(AgentConfig expectedAgentConfig) {
        assertAgentConfigAllPropertiesEquals(expectedAgentConfig, getPersistedAgentConfig(expectedAgentConfig));
    }

    protected void assertPersistedAgentConfigToMatchUpdatableProperties(AgentConfig expectedAgentConfig) {
        assertAgentConfigAllUpdatablePropertiesEquals(expectedAgentConfig, getPersistedAgentConfig(expectedAgentConfig));
    }
}
