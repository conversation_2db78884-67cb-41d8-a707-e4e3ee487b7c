package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.ContractRiskPointAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.ContractRiskPoint;
import com.whiskerguard.ai.domain.enumeration.RiskCategory;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractRiskPointRepository;
import com.whiskerguard.ai.service.dto.ContractRiskPointDTO;
import com.whiskerguard.ai.service.mapper.ContractRiskPointMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContractRiskPointResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContractRiskPointResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final RiskCategory DEFAULT_RISK_CATEGORY = RiskCategory.LEGAL_COMPLIANCE;
    private static final RiskCategory UPDATED_RISK_CATEGORY = RiskCategory.BUSINESS_RISK;

    private static final String DEFAULT_RISK_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_RISK_DESCRIPTION = "BBBBBBBBBB";

    private static final RiskLevel DEFAULT_SEVERITY = RiskLevel.HIGH;
    private static final RiskLevel UPDATED_SEVERITY = RiskLevel.MEDIUM;

    private static final String DEFAULT_AFFECTED_CLAUSES = "AAAAAAAAAA";
    private static final String UPDATED_AFFECTED_CLAUSES = "BBBBBBBBBB";

    private static final String DEFAULT_LEGAL_BASIS = "AAAAAAAAAA";
    private static final String UPDATED_LEGAL_BASIS = "BBBBBBBBBB";

    private static final String DEFAULT_SUGGESTIONS = "AAAAAAAAAA";
    private static final String UPDATED_SUGGESTIONS = "BBBBBBBBBB";

    private static final Integer DEFAULT_RISK_SCORE = 0;
    private static final Integer UPDATED_RISK_SCORE = 1;

    private static final Boolean DEFAULT_IS_CRITICAL = false;
    private static final Boolean UPDATED_IS_CRITICAL = true;

    private static final String DEFAULT_RISK_SOURCE = "AAAAAAAAAA";
    private static final String UPDATED_RISK_SOURCE = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/contract-risk-points";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContractRiskPointRepository contractRiskPointRepository;

    @Autowired
    private ContractRiskPointMapper contractRiskPointMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContractRiskPointMockMvc;

    private ContractRiskPoint contractRiskPoint;

    private ContractRiskPoint insertedContractRiskPoint;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractRiskPoint createEntity() {
        return new ContractRiskPoint()
            .tenantId(DEFAULT_TENANT_ID)
            .riskCategory(DEFAULT_RISK_CATEGORY)
            .riskDescription(DEFAULT_RISK_DESCRIPTION)
            .severity(DEFAULT_SEVERITY)
            .affectedClauses(DEFAULT_AFFECTED_CLAUSES)
            .legalBasis(DEFAULT_LEGAL_BASIS)
            .suggestions(DEFAULT_SUGGESTIONS)
            .riskScore(DEFAULT_RISK_SCORE)
            .isCritical(DEFAULT_IS_CRITICAL)
            .riskSource(DEFAULT_RISK_SOURCE)
            .version(DEFAULT_VERSION)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractRiskPoint createUpdatedEntity() {
        return new ContractRiskPoint()
            .tenantId(UPDATED_TENANT_ID)
            .riskCategory(UPDATED_RISK_CATEGORY)
            .riskDescription(UPDATED_RISK_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .affectedClauses(UPDATED_AFFECTED_CLAUSES)
            .legalBasis(UPDATED_LEGAL_BASIS)
            .suggestions(UPDATED_SUGGESTIONS)
            .riskScore(UPDATED_RISK_SCORE)
            .isCritical(UPDATED_IS_CRITICAL)
            .riskSource(UPDATED_RISK_SOURCE)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        contractRiskPoint = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContractRiskPoint != null) {
            contractRiskPointRepository.delete(insertedContractRiskPoint);
            insertedContractRiskPoint = null;
        }
    }

    @Test
    @Transactional
    void createContractRiskPoint() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);
        var returnedContractRiskPointDTO = om.readValue(
            restContractRiskPointMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContractRiskPointDTO.class
        );

        // Validate the ContractRiskPoint in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContractRiskPoint = contractRiskPointMapper.toEntity(returnedContractRiskPointDTO);
        assertContractRiskPointUpdatableFieldsEquals(returnedContractRiskPoint, getPersistedContractRiskPoint(returnedContractRiskPoint));

        insertedContractRiskPoint = returnedContractRiskPoint;
    }

    @Test
    @Transactional
    void createContractRiskPointWithExistingId() throws Exception {
        // Create the ContractRiskPoint with an existing ID
        contractRiskPoint.setId(1L);
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setTenantId(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRiskCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setRiskCategory(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSeverityIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setSeverity(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setVersion(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setCreatedAt(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractRiskPoint.setIsDeleted(null);

        // Create the ContractRiskPoint, which fails.
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        restContractRiskPointMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContractRiskPoints() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        // Get all the contractRiskPointList
        restContractRiskPointMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(contractRiskPoint.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].riskCategory").value(hasItem(DEFAULT_RISK_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].riskDescription").value(hasItem(DEFAULT_RISK_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].severity").value(hasItem(DEFAULT_SEVERITY.toString())))
            .andExpect(jsonPath("$.[*].affectedClauses").value(hasItem(DEFAULT_AFFECTED_CLAUSES)))
            .andExpect(jsonPath("$.[*].legalBasis").value(hasItem(DEFAULT_LEGAL_BASIS)))
            .andExpect(jsonPath("$.[*].suggestions").value(hasItem(DEFAULT_SUGGESTIONS)))
            .andExpect(jsonPath("$.[*].riskScore").value(hasItem(DEFAULT_RISK_SCORE)))
            .andExpect(jsonPath("$.[*].isCritical").value(hasItem(DEFAULT_IS_CRITICAL)))
            .andExpect(jsonPath("$.[*].riskSource").value(hasItem(DEFAULT_RISK_SOURCE)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContractRiskPoint() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        // Get the contractRiskPoint
        restContractRiskPointMockMvc
            .perform(get(ENTITY_API_URL_ID, contractRiskPoint.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(contractRiskPoint.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.riskCategory").value(DEFAULT_RISK_CATEGORY.toString()))
            .andExpect(jsonPath("$.riskDescription").value(DEFAULT_RISK_DESCRIPTION))
            .andExpect(jsonPath("$.severity").value(DEFAULT_SEVERITY.toString()))
            .andExpect(jsonPath("$.affectedClauses").value(DEFAULT_AFFECTED_CLAUSES))
            .andExpect(jsonPath("$.legalBasis").value(DEFAULT_LEGAL_BASIS))
            .andExpect(jsonPath("$.suggestions").value(DEFAULT_SUGGESTIONS))
            .andExpect(jsonPath("$.riskScore").value(DEFAULT_RISK_SCORE))
            .andExpect(jsonPath("$.isCritical").value(DEFAULT_IS_CRITICAL))
            .andExpect(jsonPath("$.riskSource").value(DEFAULT_RISK_SOURCE))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContractRiskPoint() throws Exception {
        // Get the contractRiskPoint
        restContractRiskPointMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContractRiskPoint() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractRiskPoint
        ContractRiskPoint updatedContractRiskPoint = contractRiskPointRepository.findById(contractRiskPoint.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedContractRiskPoint are not directly saved in db
        em.detach(updatedContractRiskPoint);
        updatedContractRiskPoint
            .tenantId(UPDATED_TENANT_ID)
            .riskCategory(UPDATED_RISK_CATEGORY)
            .riskDescription(UPDATED_RISK_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .affectedClauses(UPDATED_AFFECTED_CLAUSES)
            .legalBasis(UPDATED_LEGAL_BASIS)
            .suggestions(UPDATED_SUGGESTIONS)
            .riskScore(UPDATED_RISK_SCORE)
            .isCritical(UPDATED_IS_CRITICAL)
            .riskSource(UPDATED_RISK_SOURCE)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(updatedContractRiskPoint);

        restContractRiskPointMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractRiskPointDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractRiskPointDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContractRiskPointToMatchAllProperties(updatedContractRiskPoint);
    }

    @Test
    @Transactional
    void putNonExistingContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractRiskPointDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractRiskPointDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractRiskPointDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContractRiskPointWithPatch() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractRiskPoint using partial update
        ContractRiskPoint partialUpdatedContractRiskPoint = new ContractRiskPoint();
        partialUpdatedContractRiskPoint.setId(contractRiskPoint.getId());

        partialUpdatedContractRiskPoint
            .riskDescription(UPDATED_RISK_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .affectedClauses(UPDATED_AFFECTED_CLAUSES)
            .legalBasis(UPDATED_LEGAL_BASIS)
            .suggestions(UPDATED_SUGGESTIONS)
            .version(UPDATED_VERSION)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractRiskPointMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractRiskPoint.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractRiskPoint))
            )
            .andExpect(status().isOk());

        // Validate the ContractRiskPoint in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractRiskPointUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContractRiskPoint, contractRiskPoint),
            getPersistedContractRiskPoint(contractRiskPoint)
        );
    }

    @Test
    @Transactional
    void fullUpdateContractRiskPointWithPatch() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractRiskPoint using partial update
        ContractRiskPoint partialUpdatedContractRiskPoint = new ContractRiskPoint();
        partialUpdatedContractRiskPoint.setId(contractRiskPoint.getId());

        partialUpdatedContractRiskPoint
            .tenantId(UPDATED_TENANT_ID)
            .riskCategory(UPDATED_RISK_CATEGORY)
            .riskDescription(UPDATED_RISK_DESCRIPTION)
            .severity(UPDATED_SEVERITY)
            .affectedClauses(UPDATED_AFFECTED_CLAUSES)
            .legalBasis(UPDATED_LEGAL_BASIS)
            .suggestions(UPDATED_SUGGESTIONS)
            .riskScore(UPDATED_RISK_SCORE)
            .isCritical(UPDATED_IS_CRITICAL)
            .riskSource(UPDATED_RISK_SOURCE)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractRiskPointMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractRiskPoint.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractRiskPoint))
            )
            .andExpect(status().isOk());

        // Validate the ContractRiskPoint in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractRiskPointUpdatableFieldsEquals(
            partialUpdatedContractRiskPoint,
            getPersistedContractRiskPoint(partialUpdatedContractRiskPoint)
        );
    }

    @Test
    @Transactional
    void patchNonExistingContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, contractRiskPointDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractRiskPointDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractRiskPointDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContractRiskPoint() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractRiskPoint.setId(longCount.incrementAndGet());

        // Create the ContractRiskPoint
        ContractRiskPointDTO contractRiskPointDTO = contractRiskPointMapper.toDto(contractRiskPoint);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractRiskPointMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(contractRiskPointDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractRiskPoint in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContractRiskPoint() throws Exception {
        // Initialize the database
        insertedContractRiskPoint = contractRiskPointRepository.saveAndFlush(contractRiskPoint);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the contractRiskPoint
        restContractRiskPointMockMvc
            .perform(delete(ENTITY_API_URL_ID, contractRiskPoint.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return contractRiskPointRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContractRiskPoint getPersistedContractRiskPoint(ContractRiskPoint contractRiskPoint) {
        return contractRiskPointRepository.findById(contractRiskPoint.getId()).orElseThrow();
    }

    protected void assertPersistedContractRiskPointToMatchAllProperties(ContractRiskPoint expectedContractRiskPoint) {
        assertContractRiskPointAllPropertiesEquals(expectedContractRiskPoint, getPersistedContractRiskPoint(expectedContractRiskPoint));
    }

    protected void assertPersistedContractRiskPointToMatchUpdatableProperties(ContractRiskPoint expectedContractRiskPoint) {
        assertContractRiskPointAllUpdatablePropertiesEquals(
            expectedContractRiskPoint,
            getPersistedContractRiskPoint(expectedContractRiskPoint)
        );
    }
}
