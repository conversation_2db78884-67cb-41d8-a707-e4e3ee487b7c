package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AiToolMetricsAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AiToolMetrics;
import com.whiskerguard.ai.domain.enumeration.MetricsPeriod;
import com.whiskerguard.ai.repository.AiToolMetricsRepository;
import com.whiskerguard.ai.service.dto.AiToolMetricsDTO;
import com.whiskerguard.ai.service.mapper.AiToolMetricsMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AiToolMetricsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AiToolMetricsResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final MetricsPeriod DEFAULT_PERIOD = MetricsPeriod.HOURLY;
    private static final MetricsPeriod UPDATED_PERIOD = MetricsPeriod.DAILY;

    private static final Integer DEFAULT_RESPONSE_TIME = 1;
    private static final Integer UPDATED_RESPONSE_TIME = 2;

    private static final Long DEFAULT_SUCCESS_COUNT = 1L;
    private static final Long UPDATED_SUCCESS_COUNT = 2L;

    private static final Long DEFAULT_FAILURE_COUNT = 1L;
    private static final Long UPDATED_FAILURE_COUNT = 2L;

    private static final Long DEFAULT_TOTAL_REQUESTS = 1L;
    private static final Long UPDATED_TOTAL_REQUESTS = 2L;

    private static final Float DEFAULT_ERROR_RATE = 1F;
    private static final Float UPDATED_ERROR_RATE = 2F;

    private static final Instant DEFAULT_COLLECT_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_COLLECT_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/ai-tool-metrics";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AiToolMetricsRepository aiToolMetricsRepository;

    @Autowired
    private AiToolMetricsMapper aiToolMetricsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAiToolMetricsMockMvc;

    private AiToolMetrics aiToolMetrics;

    private AiToolMetrics insertedAiToolMetrics;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiToolMetrics createEntity() {
        return new AiToolMetrics()
            .tenantId(DEFAULT_TENANT_ID)
            .period(DEFAULT_PERIOD)
            .responseTime(DEFAULT_RESPONSE_TIME)
            .successCount(DEFAULT_SUCCESS_COUNT)
            .failureCount(DEFAULT_FAILURE_COUNT)
            .totalRequests(DEFAULT_TOTAL_REQUESTS)
            .errorRate(DEFAULT_ERROR_RATE)
            .collectDate(DEFAULT_COLLECT_DATE)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiToolMetrics createUpdatedEntity() {
        return new AiToolMetrics()
            .tenantId(UPDATED_TENANT_ID)
            .period(UPDATED_PERIOD)
            .responseTime(UPDATED_RESPONSE_TIME)
            .successCount(UPDATED_SUCCESS_COUNT)
            .failureCount(UPDATED_FAILURE_COUNT)
            .totalRequests(UPDATED_TOTAL_REQUESTS)
            .errorRate(UPDATED_ERROR_RATE)
            .collectDate(UPDATED_COLLECT_DATE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        aiToolMetrics = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAiToolMetrics != null) {
            aiToolMetricsRepository.delete(insertedAiToolMetrics);
            insertedAiToolMetrics = null;
        }
    }

    @Test
    @Transactional
    void createAiToolMetrics() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);
        var returnedAiToolMetricsDTO = om.readValue(
            restAiToolMetricsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AiToolMetricsDTO.class
        );

        // Validate the AiToolMetrics in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAiToolMetrics = aiToolMetricsMapper.toEntity(returnedAiToolMetricsDTO);
        assertAiToolMetricsUpdatableFieldsEquals(returnedAiToolMetrics, getPersistedAiToolMetrics(returnedAiToolMetrics));

        insertedAiToolMetrics = returnedAiToolMetrics;
    }

    @Test
    @Transactional
    void createAiToolMetricsWithExistingId() throws Exception {
        // Create the AiToolMetrics with an existing ID
        aiToolMetrics.setId(1L);
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setTenantId(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPeriodIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setPeriod(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCollectDateIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setCollectDate(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setVersion(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setCreatedAt(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setUpdatedAt(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiToolMetrics.setIsDeleted(null);

        // Create the AiToolMetrics, which fails.
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAiToolMetrics() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        // Get all the aiToolMetricsList
        restAiToolMetricsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(aiToolMetrics.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].period").value(hasItem(DEFAULT_PERIOD.toString())))
            .andExpect(jsonPath("$.[*].responseTime").value(hasItem(DEFAULT_RESPONSE_TIME)))
            .andExpect(jsonPath("$.[*].successCount").value(hasItem(DEFAULT_SUCCESS_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].failureCount").value(hasItem(DEFAULT_FAILURE_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].totalRequests").value(hasItem(DEFAULT_TOTAL_REQUESTS.intValue())))
            .andExpect(jsonPath("$.[*].errorRate").value(hasItem(DEFAULT_ERROR_RATE.doubleValue())))
            .andExpect(jsonPath("$.[*].collectDate").value(hasItem(DEFAULT_COLLECT_DATE.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAiToolMetrics() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        // Get the aiToolMetrics
        restAiToolMetricsMockMvc
            .perform(get(ENTITY_API_URL_ID, aiToolMetrics.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(aiToolMetrics.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.period").value(DEFAULT_PERIOD.toString()))
            .andExpect(jsonPath("$.responseTime").value(DEFAULT_RESPONSE_TIME))
            .andExpect(jsonPath("$.successCount").value(DEFAULT_SUCCESS_COUNT.intValue()))
            .andExpect(jsonPath("$.failureCount").value(DEFAULT_FAILURE_COUNT.intValue()))
            .andExpect(jsonPath("$.totalRequests").value(DEFAULT_TOTAL_REQUESTS.intValue()))
            .andExpect(jsonPath("$.errorRate").value(DEFAULT_ERROR_RATE.doubleValue()))
            .andExpect(jsonPath("$.collectDate").value(DEFAULT_COLLECT_DATE.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAiToolMetrics() throws Exception {
        // Get the aiToolMetrics
        restAiToolMetricsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAiToolMetrics() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiToolMetrics
        AiToolMetrics updatedAiToolMetrics = aiToolMetricsRepository.findById(aiToolMetrics.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAiToolMetrics are not directly saved in db
        em.detach(updatedAiToolMetrics);
        updatedAiToolMetrics
            .tenantId(UPDATED_TENANT_ID)
            .period(UPDATED_PERIOD)
            .responseTime(UPDATED_RESPONSE_TIME)
            .successCount(UPDATED_SUCCESS_COUNT)
            .failureCount(UPDATED_FAILURE_COUNT)
            .totalRequests(UPDATED_TOTAL_REQUESTS)
            .errorRate(UPDATED_ERROR_RATE)
            .collectDate(UPDATED_COLLECT_DATE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(updatedAiToolMetrics);

        restAiToolMetricsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiToolMetricsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiToolMetricsDTO))
            )
            .andExpect(status().isOk());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAiToolMetricsToMatchAllProperties(updatedAiToolMetrics);
    }

    @Test
    @Transactional
    void putNonExistingAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiToolMetricsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiToolMetricsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiToolMetricsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAiToolMetricsWithPatch() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiToolMetrics using partial update
        AiToolMetrics partialUpdatedAiToolMetrics = new AiToolMetrics();
        partialUpdatedAiToolMetrics.setId(aiToolMetrics.getId());

        partialUpdatedAiToolMetrics
            .successCount(UPDATED_SUCCESS_COUNT)
            .errorRate(UPDATED_ERROR_RATE)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiToolMetricsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiToolMetrics.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiToolMetrics))
            )
            .andExpect(status().isOk());

        // Validate the AiToolMetrics in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiToolMetricsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAiToolMetrics, aiToolMetrics),
            getPersistedAiToolMetrics(aiToolMetrics)
        );
    }

    @Test
    @Transactional
    void fullUpdateAiToolMetricsWithPatch() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiToolMetrics using partial update
        AiToolMetrics partialUpdatedAiToolMetrics = new AiToolMetrics();
        partialUpdatedAiToolMetrics.setId(aiToolMetrics.getId());

        partialUpdatedAiToolMetrics
            .tenantId(UPDATED_TENANT_ID)
            .period(UPDATED_PERIOD)
            .responseTime(UPDATED_RESPONSE_TIME)
            .successCount(UPDATED_SUCCESS_COUNT)
            .failureCount(UPDATED_FAILURE_COUNT)
            .totalRequests(UPDATED_TOTAL_REQUESTS)
            .errorRate(UPDATED_ERROR_RATE)
            .collectDate(UPDATED_COLLECT_DATE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiToolMetricsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiToolMetrics.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiToolMetrics))
            )
            .andExpect(status().isOk());

        // Validate the AiToolMetrics in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiToolMetricsUpdatableFieldsEquals(partialUpdatedAiToolMetrics, getPersistedAiToolMetrics(partialUpdatedAiToolMetrics));
    }

    @Test
    @Transactional
    void patchNonExistingAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, aiToolMetricsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiToolMetricsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiToolMetricsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAiToolMetrics() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiToolMetrics.setId(longCount.incrementAndGet());

        // Create the AiToolMetrics
        AiToolMetricsDTO aiToolMetricsDTO = aiToolMetricsMapper.toDto(aiToolMetrics);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMetricsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(aiToolMetricsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiToolMetrics in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAiToolMetrics() throws Exception {
        // Initialize the database
        insertedAiToolMetrics = aiToolMetricsRepository.saveAndFlush(aiToolMetrics);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the aiToolMetrics
        restAiToolMetricsMockMvc
            .perform(delete(ENTITY_API_URL_ID, aiToolMetrics.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return aiToolMetricsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AiToolMetrics getPersistedAiToolMetrics(AiToolMetrics aiToolMetrics) {
        return aiToolMetricsRepository.findById(aiToolMetrics.getId()).orElseThrow();
    }

    protected void assertPersistedAiToolMetricsToMatchAllProperties(AiToolMetrics expectedAiToolMetrics) {
        assertAiToolMetricsAllPropertiesEquals(expectedAiToolMetrics, getPersistedAiToolMetrics(expectedAiToolMetrics));
    }

    protected void assertPersistedAiToolMetricsToMatchUpdatableProperties(AiToolMetrics expectedAiToolMetrics) {
        assertAiToolMetricsAllUpdatablePropertiesEquals(expectedAiToolMetrics, getPersistedAiToolMetrics(expectedAiToolMetrics));
    }
}
