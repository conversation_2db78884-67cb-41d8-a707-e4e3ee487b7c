package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AgentTaskAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.dto.AgentTaskDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AgentTaskResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AgentTaskResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final AgentTaskType DEFAULT_TASK_TYPE = AgentTaskType.REGULATION_INTERNALIZATION;
    private static final AgentTaskType UPDATED_TASK_TYPE = AgentTaskType.POLICY_REVIEW;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final AgentTaskStatus DEFAULT_STATUS = AgentTaskStatus.PENDING;
    private static final AgentTaskStatus UPDATED_STATUS = AgentTaskStatus.RUNNING;

    private static final TaskPriority DEFAULT_PRIORITY = TaskPriority.LOW;
    private static final TaskPriority UPDATED_PRIORITY = TaskPriority.NORMAL;

    private static final String DEFAULT_REQUEST_DATA = "AAAAAAAAAA";
    private static final String UPDATED_REQUEST_DATA = "BBBBBBBBBB";

    private static final String DEFAULT_RESPONSE_DATA = "AAAAAAAAAA";
    private static final String UPDATED_RESPONSE_DATA = "BBBBBBBBBB";

    private static final String DEFAULT_ERROR_MESSAGE = "AAAAAAAAAA";
    private static final String UPDATED_ERROR_MESSAGE = "BBBBBBBBBB";

    private static final Instant DEFAULT_START_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_START_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_END_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_END_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_EXECUTION_TIME = 1L;
    private static final Long UPDATED_EXECUTION_TIME = 2L;

    private static final Integer DEFAULT_PROGRESS = 0;
    private static final Integer UPDATED_PROGRESS = 1;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/agent-tasks";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AgentTaskRepository agentTaskRepository;

    @Autowired
    private AgentTaskMapper agentTaskMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAgentTaskMockMvc;

    private AgentTask agentTask;

    private AgentTask insertedAgentTask;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentTask createEntity() {
        return new AgentTask()
            .tenantId(DEFAULT_TENANT_ID)
            .taskType(DEFAULT_TASK_TYPE)
            .title(DEFAULT_TITLE)
            .description(DEFAULT_DESCRIPTION)
            .status(DEFAULT_STATUS)
            .priority(DEFAULT_PRIORITY)
            .requestData(DEFAULT_REQUEST_DATA)
            .responseData(DEFAULT_RESPONSE_DATA)
            .errorMessage(DEFAULT_ERROR_MESSAGE)
            .startTime(DEFAULT_START_TIME)
            .endTime(DEFAULT_END_TIME)
            .executionTime(DEFAULT_EXECUTION_TIME)
            .progress(DEFAULT_PROGRESS)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AgentTask createUpdatedEntity() {
        return new AgentTask()
            .tenantId(UPDATED_TENANT_ID)
            .taskType(UPDATED_TASK_TYPE)
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .status(UPDATED_STATUS)
            .priority(UPDATED_PRIORITY)
            .requestData(UPDATED_REQUEST_DATA)
            .responseData(UPDATED_RESPONSE_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .progress(UPDATED_PROGRESS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        agentTask = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAgentTask != null) {
            agentTaskRepository.delete(insertedAgentTask);
            insertedAgentTask = null;
        }
    }

    @Test
    @Transactional
    void createAgentTask() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);
        var returnedAgentTaskDTO = om.readValue(
            restAgentTaskMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AgentTaskDTO.class
        );

        // Validate the AgentTask in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAgentTask = agentTaskMapper.toEntity(returnedAgentTaskDTO);
        assertAgentTaskUpdatableFieldsEquals(returnedAgentTask, getPersistedAgentTask(returnedAgentTask));

        insertedAgentTask = returnedAgentTask;
    }

    @Test
    @Transactional
    void createAgentTaskWithExistingId() throws Exception {
        // Create the AgentTask with an existing ID
        agentTask.setId(1L);
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setTenantId(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTaskTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setTaskType(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTitleIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setTitle(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setStatus(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPriorityIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setPriority(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setVersion(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setCreatedAt(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setUpdatedAt(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        agentTask.setIsDeleted(null);

        // Create the AgentTask, which fails.
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        restAgentTaskMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAgentTasks() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        // Get all the agentTaskList
        restAgentTaskMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(agentTask.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].taskType").value(hasItem(DEFAULT_TASK_TYPE.toString())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].priority").value(hasItem(DEFAULT_PRIORITY.toString())))
            .andExpect(jsonPath("$.[*].requestData").value(hasItem(DEFAULT_REQUEST_DATA)))
            .andExpect(jsonPath("$.[*].responseData").value(hasItem(DEFAULT_RESPONSE_DATA)))
            .andExpect(jsonPath("$.[*].errorMessage").value(hasItem(DEFAULT_ERROR_MESSAGE)))
            .andExpect(jsonPath("$.[*].startTime").value(hasItem(DEFAULT_START_TIME.toString())))
            .andExpect(jsonPath("$.[*].endTime").value(hasItem(DEFAULT_END_TIME.toString())))
            .andExpect(jsonPath("$.[*].executionTime").value(hasItem(DEFAULT_EXECUTION_TIME.intValue())))
            .andExpect(jsonPath("$.[*].progress").value(hasItem(DEFAULT_PROGRESS)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAgentTask() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        // Get the agentTask
        restAgentTaskMockMvc
            .perform(get(ENTITY_API_URL_ID, agentTask.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(agentTask.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.taskType").value(DEFAULT_TASK_TYPE.toString()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.priority").value(DEFAULT_PRIORITY.toString()))
            .andExpect(jsonPath("$.requestData").value(DEFAULT_REQUEST_DATA))
            .andExpect(jsonPath("$.responseData").value(DEFAULT_RESPONSE_DATA))
            .andExpect(jsonPath("$.errorMessage").value(DEFAULT_ERROR_MESSAGE))
            .andExpect(jsonPath("$.startTime").value(DEFAULT_START_TIME.toString()))
            .andExpect(jsonPath("$.endTime").value(DEFAULT_END_TIME.toString()))
            .andExpect(jsonPath("$.executionTime").value(DEFAULT_EXECUTION_TIME.intValue()))
            .andExpect(jsonPath("$.progress").value(DEFAULT_PROGRESS))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAgentTask() throws Exception {
        // Get the agentTask
        restAgentTaskMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAgentTask() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentTask
        AgentTask updatedAgentTask = agentTaskRepository.findById(agentTask.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAgentTask are not directly saved in db
        em.detach(updatedAgentTask);
        updatedAgentTask
            .tenantId(UPDATED_TENANT_ID)
            .taskType(UPDATED_TASK_TYPE)
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .status(UPDATED_STATUS)
            .priority(UPDATED_PRIORITY)
            .requestData(UPDATED_REQUEST_DATA)
            .responseData(UPDATED_RESPONSE_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .progress(UPDATED_PROGRESS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(updatedAgentTask);

        restAgentTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentTaskDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentTaskDTO))
            )
            .andExpect(status().isOk());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAgentTaskToMatchAllProperties(updatedAgentTask);
    }

    @Test
    @Transactional
    void putNonExistingAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, agentTaskDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(agentTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAgentTaskWithPatch() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentTask using partial update
        AgentTask partialUpdatedAgentTask = new AgentTask();
        partialUpdatedAgentTask.setId(agentTask.getId());

        partialUpdatedAgentTask
            .tenantId(UPDATED_TENANT_ID)
            .taskType(UPDATED_TASK_TYPE)
            .requestData(UPDATED_REQUEST_DATA)
            .responseData(UPDATED_RESPONSE_DATA)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAgentTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentTask.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentTask))
            )
            .andExpect(status().isOk());

        // Validate the AgentTask in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentTaskUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAgentTask, agentTask),
            getPersistedAgentTask(agentTask)
        );
    }

    @Test
    @Transactional
    void fullUpdateAgentTaskWithPatch() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the agentTask using partial update
        AgentTask partialUpdatedAgentTask = new AgentTask();
        partialUpdatedAgentTask.setId(agentTask.getId());

        partialUpdatedAgentTask
            .tenantId(UPDATED_TENANT_ID)
            .taskType(UPDATED_TASK_TYPE)
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .status(UPDATED_STATUS)
            .priority(UPDATED_PRIORITY)
            .requestData(UPDATED_REQUEST_DATA)
            .responseData(UPDATED_RESPONSE_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .progress(UPDATED_PROGRESS)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAgentTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAgentTask.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAgentTask))
            )
            .andExpect(status().isOk());

        // Validate the AgentTask in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAgentTaskUpdatableFieldsEquals(partialUpdatedAgentTask, getPersistedAgentTask(partialUpdatedAgentTask));
    }

    @Test
    @Transactional
    void patchNonExistingAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, agentTaskDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(agentTaskDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAgentTask() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        agentTask.setId(longCount.incrementAndGet());

        // Create the AgentTask
        AgentTaskDTO agentTaskDTO = agentTaskMapper.toDto(agentTask);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAgentTaskMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(agentTaskDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AgentTask in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAgentTask() throws Exception {
        // Initialize the database
        insertedAgentTask = agentTaskRepository.saveAndFlush(agentTask);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the agentTask
        restAgentTaskMockMvc
            .perform(delete(ENTITY_API_URL_ID, agentTask.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return agentTaskRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AgentTask getPersistedAgentTask(AgentTask agentTask) {
        return agentTaskRepository.findById(agentTask.getId()).orElseThrow();
    }

    protected void assertPersistedAgentTaskToMatchAllProperties(AgentTask expectedAgentTask) {
        assertAgentTaskAllPropertiesEquals(expectedAgentTask, getPersistedAgentTask(expectedAgentTask));
    }

    protected void assertPersistedAgentTaskToMatchUpdatableProperties(AgentTask expectedAgentTask) {
        assertAgentTaskAllUpdatablePropertiesEquals(expectedAgentTask, getPersistedAgentTask(expectedAgentTask));
    }
}
