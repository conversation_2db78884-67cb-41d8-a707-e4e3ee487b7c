package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AiToolAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.dto.AiToolDTO;
import com.whiskerguard.ai.service.mapper.AiToolMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AiToolResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AiToolResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_TOOL_KEY = "AAAAAAAAAA";
    private static final String UPDATED_TOOL_KEY = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_API_URL = "AAAAAAAAAA";
    private static final String UPDATED_API_URL = "BBBBBBBBBB";

    private static final String DEFAULT_API_KEY = "AAAAAAAAAA";
    private static final String UPDATED_API_KEY = "BBBBBBBBBB";

    private static final String DEFAULT_AUTH_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_AUTH_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_PATH = "AAAAAAAAAA";
    private static final String UPDATED_PATH = "BBBBBBBBBB";

    private static final ToolStatus DEFAULT_STATUS = ToolStatus.AVAILABLE;
    private static final ToolStatus UPDATED_STATUS = ToolStatus.UNAVAILABLE;

    private static final Integer DEFAULT_WEIGHT = 1;
    private static final Integer UPDATED_WEIGHT = 2;

    private static final Integer DEFAULT_MAX_CONCURRENT_CALLS = 1;
    private static final Integer UPDATED_MAX_CONCURRENT_CALLS = 2;

    private static final Boolean DEFAULT_IS_MODEL = false;
    private static final Boolean UPDATED_IS_MODEL = true;

    private static final String DEFAULT_MODEL_CATEGORY = "AAAAAAAAAA";
    private static final String UPDATED_MODEL_CATEGORY = "BBBBBBBBBB";

    private static final String DEFAULT_MODEL_PROVIDER = "AAAAAAAAAA";
    private static final String UPDATED_MODEL_PROVIDER = "BBBBBBBBBB";

    private static final String DEFAULT_REMARK = "AAAAAAAAAA";
    private static final String UPDATED_REMARK = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/ai-tools";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AiToolRepository aiToolRepository;

    @Autowired
    private AiToolMapper aiToolMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAiToolMockMvc;

    private AiTool aiTool;

    private AiTool insertedAiTool;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiTool createEntity() {
        return new AiTool()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .toolKey(DEFAULT_TOOL_KEY)
            .version(DEFAULT_VERSION)
            .apiUrl(DEFAULT_API_URL)
            .apiKey(DEFAULT_API_KEY)
            .authType(DEFAULT_AUTH_TYPE)
            .path(DEFAULT_PATH)
            .status(DEFAULT_STATUS)
            .weight(DEFAULT_WEIGHT)
            .maxConcurrentCalls(DEFAULT_MAX_CONCURRENT_CALLS)
            .isModel(DEFAULT_IS_MODEL)
            .modelCategory(DEFAULT_MODEL_CATEGORY)
            .modelProvider(DEFAULT_MODEL_PROVIDER)
            .remark(DEFAULT_REMARK)
            .metadata(DEFAULT_METADATA)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiTool createUpdatedEntity() {
        return new AiTool()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .toolKey(UPDATED_TOOL_KEY)
            .version(UPDATED_VERSION)
            .apiUrl(UPDATED_API_URL)
            .apiKey(UPDATED_API_KEY)
            .authType(UPDATED_AUTH_TYPE)
            .path(UPDATED_PATH)
            .status(UPDATED_STATUS)
            .weight(UPDATED_WEIGHT)
            .maxConcurrentCalls(UPDATED_MAX_CONCURRENT_CALLS)
            .isModel(UPDATED_IS_MODEL)
            .modelCategory(UPDATED_MODEL_CATEGORY)
            .modelProvider(UPDATED_MODEL_PROVIDER)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        aiTool = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAiTool != null) {
            aiToolRepository.delete(insertedAiTool);
            insertedAiTool = null;
        }
    }

    @Test
    @Transactional
    void createAiTool() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);
        var returnedAiToolDTO = om.readValue(
            restAiToolMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AiToolDTO.class
        );

        // Validate the AiTool in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAiTool = aiToolMapper.toEntity(returnedAiToolDTO);
        assertAiToolUpdatableFieldsEquals(returnedAiTool, getPersistedAiTool(returnedAiTool));

        insertedAiTool = returnedAiTool;
    }

    @Test
    @Transactional
    void createAiToolWithExistingId() throws Exception {
        // Create the AiTool with an existing ID
        aiTool.setId(1L);
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setTenantId(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setName(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkToolKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setToolKey(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setVersion(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkApiUrlIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setApiUrl(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkApiKeyIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setApiKey(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setStatus(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkWeightIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setWeight(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkMaxConcurrentCallsIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setMaxConcurrentCalls(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setCreatedAt(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setUpdatedAt(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiTool.setIsDeleted(null);

        // Create the AiTool, which fails.
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        restAiToolMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAiTools() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        // Get all the aiToolList
        restAiToolMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(aiTool.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].toolKey").value(hasItem(DEFAULT_TOOL_KEY)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].apiUrl").value(hasItem(DEFAULT_API_URL)))
            .andExpect(jsonPath("$.[*].apiKey").value(hasItem(DEFAULT_API_KEY)))
            .andExpect(jsonPath("$.[*].authType").value(hasItem(DEFAULT_AUTH_TYPE)))
            .andExpect(jsonPath("$.[*].path").value(hasItem(DEFAULT_PATH)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].weight").value(hasItem(DEFAULT_WEIGHT)))
            .andExpect(jsonPath("$.[*].maxConcurrentCalls").value(hasItem(DEFAULT_MAX_CONCURRENT_CALLS)))
            .andExpect(jsonPath("$.[*].isModel").value(hasItem(DEFAULT_IS_MODEL)))
            .andExpect(jsonPath("$.[*].modelCategory").value(hasItem(DEFAULT_MODEL_CATEGORY)))
            .andExpect(jsonPath("$.[*].modelProvider").value(hasItem(DEFAULT_MODEL_PROVIDER)))
            .andExpect(jsonPath("$.[*].remark").value(hasItem(DEFAULT_REMARK)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAiTool() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        // Get the aiTool
        restAiToolMockMvc
            .perform(get(ENTITY_API_URL_ID, aiTool.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(aiTool.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.toolKey").value(DEFAULT_TOOL_KEY))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.apiUrl").value(DEFAULT_API_URL))
            .andExpect(jsonPath("$.apiKey").value(DEFAULT_API_KEY))
            .andExpect(jsonPath("$.authType").value(DEFAULT_AUTH_TYPE))
            .andExpect(jsonPath("$.path").value(DEFAULT_PATH))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.weight").value(DEFAULT_WEIGHT))
            .andExpect(jsonPath("$.maxConcurrentCalls").value(DEFAULT_MAX_CONCURRENT_CALLS))
            .andExpect(jsonPath("$.isModel").value(DEFAULT_IS_MODEL))
            .andExpect(jsonPath("$.modelCategory").value(DEFAULT_MODEL_CATEGORY))
            .andExpect(jsonPath("$.modelProvider").value(DEFAULT_MODEL_PROVIDER))
            .andExpect(jsonPath("$.remark").value(DEFAULT_REMARK))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAiTool() throws Exception {
        // Get the aiTool
        restAiToolMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAiTool() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiTool
        AiTool updatedAiTool = aiToolRepository.findById(aiTool.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAiTool are not directly saved in db
        em.detach(updatedAiTool);
        updatedAiTool
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .toolKey(UPDATED_TOOL_KEY)
            .version(UPDATED_VERSION)
            .apiUrl(UPDATED_API_URL)
            .apiKey(UPDATED_API_KEY)
            .authType(UPDATED_AUTH_TYPE)
            .path(UPDATED_PATH)
            .status(UPDATED_STATUS)
            .weight(UPDATED_WEIGHT)
            .maxConcurrentCalls(UPDATED_MAX_CONCURRENT_CALLS)
            .isModel(UPDATED_IS_MODEL)
            .modelCategory(UPDATED_MODEL_CATEGORY)
            .modelProvider(UPDATED_MODEL_PROVIDER)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AiToolDTO aiToolDTO = aiToolMapper.toDto(updatedAiTool);

        restAiToolMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiToolDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO))
            )
            .andExpect(status().isOk());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAiToolToMatchAllProperties(updatedAiTool);
    }

    @Test
    @Transactional
    void putNonExistingAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiToolDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiToolDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAiToolWithPatch() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiTool using partial update
        AiTool partialUpdatedAiTool = new AiTool();
        partialUpdatedAiTool.setId(aiTool.getId());

        partialUpdatedAiTool
            .apiKey(UPDATED_API_KEY)
            .path(UPDATED_PATH)
            .weight(UPDATED_WEIGHT)
            .isModel(UPDATED_IS_MODEL)
            .modelCategory(UPDATED_MODEL_CATEGORY)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiToolMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiTool.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiTool))
            )
            .andExpect(status().isOk());

        // Validate the AiTool in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiToolUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedAiTool, aiTool), getPersistedAiTool(aiTool));
    }

    @Test
    @Transactional
    void fullUpdateAiToolWithPatch() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiTool using partial update
        AiTool partialUpdatedAiTool = new AiTool();
        partialUpdatedAiTool.setId(aiTool.getId());

        partialUpdatedAiTool
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .toolKey(UPDATED_TOOL_KEY)
            .version(UPDATED_VERSION)
            .apiUrl(UPDATED_API_URL)
            .apiKey(UPDATED_API_KEY)
            .authType(UPDATED_AUTH_TYPE)
            .path(UPDATED_PATH)
            .status(UPDATED_STATUS)
            .weight(UPDATED_WEIGHT)
            .maxConcurrentCalls(UPDATED_MAX_CONCURRENT_CALLS)
            .isModel(UPDATED_IS_MODEL)
            .modelCategory(UPDATED_MODEL_CATEGORY)
            .modelProvider(UPDATED_MODEL_PROVIDER)
            .remark(UPDATED_REMARK)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiToolMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiTool.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiTool))
            )
            .andExpect(status().isOk());

        // Validate the AiTool in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiToolUpdatableFieldsEquals(partialUpdatedAiTool, getPersistedAiTool(partialUpdatedAiTool));
    }

    @Test
    @Transactional
    void patchNonExistingAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, aiToolDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiToolDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiToolDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAiTool() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiTool.setId(longCount.incrementAndGet());

        // Create the AiTool
        AiToolDTO aiToolDTO = aiToolMapper.toDto(aiTool);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiToolMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(aiToolDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiTool in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAiTool() throws Exception {
        // Initialize the database
        insertedAiTool = aiToolRepository.saveAndFlush(aiTool);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the aiTool
        restAiToolMockMvc
            .perform(delete(ENTITY_API_URL_ID, aiTool.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return aiToolRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AiTool getPersistedAiTool(AiTool aiTool) {
        return aiToolRepository.findById(aiTool.getId()).orElseThrow();
    }

    protected void assertPersistedAiToolToMatchAllProperties(AiTool expectedAiTool) {
        assertAiToolAllPropertiesEquals(expectedAiTool, getPersistedAiTool(expectedAiTool));
    }

    protected void assertPersistedAiToolToMatchUpdatableProperties(AiTool expectedAiTool) {
        assertAiToolAllUpdatablePropertiesEquals(expectedAiTool, getPersistedAiTool(expectedAiTool));
    }
}
