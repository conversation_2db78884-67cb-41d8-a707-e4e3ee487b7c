package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.AiRequestAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AiRequestResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AiRequestResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_TOOL_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_TOOL_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_PROMPT = "AAAAAAAAAA";
    private static final String UPDATED_PROMPT = "BBBBBBBBBB";

    private static final String DEFAULT_RESPONSE = "AAAAAAAAAA";
    private static final String UPDATED_RESPONSE = "BBBBBBBBBB";

    private static final Instant DEFAULT_REQUEST_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_REQUEST_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_RESPONSE_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_RESPONSE_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final RequestStatus DEFAULT_STATUS = RequestStatus.SUCCESS;
    private static final RequestStatus UPDATED_STATUS = RequestStatus.FAILED;

    private static final String DEFAULT_ERROR_MESSAGE = "AAAAAAAAAA";
    private static final String UPDATED_ERROR_MESSAGE = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/ai-requests";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AiRequestRepository aiRequestRepository;

    @Autowired
    private AiRequestMapper aiRequestMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAiRequestMockMvc;

    private AiRequest aiRequest;

    private AiRequest insertedAiRequest;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiRequest createEntity() {
        return new AiRequest()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .toolType(DEFAULT_TOOL_TYPE)
            .prompt(DEFAULT_PROMPT)
            .response(DEFAULT_RESPONSE)
            .requestTime(DEFAULT_REQUEST_TIME)
            .responseTime(DEFAULT_RESPONSE_TIME)
            .status(DEFAULT_STATUS)
            .errorMessage(DEFAULT_ERROR_MESSAGE)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AiRequest createUpdatedEntity() {
        return new AiRequest()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .toolType(UPDATED_TOOL_TYPE)
            .prompt(UPDATED_PROMPT)
            .response(UPDATED_RESPONSE)
            .requestTime(UPDATED_REQUEST_TIME)
            .responseTime(UPDATED_RESPONSE_TIME)
            .status(UPDATED_STATUS)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        aiRequest = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAiRequest != null) {
            aiRequestRepository.delete(insertedAiRequest);
            insertedAiRequest = null;
        }
    }

    @Test
    @Transactional
    void createAiRequest() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);
        var returnedAiRequestDTO = om.readValue(
            restAiRequestMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AiRequestDTO.class
        );

        // Validate the AiRequest in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAiRequest = aiRequestMapper.toEntity(returnedAiRequestDTO);
        assertAiRequestUpdatableFieldsEquals(returnedAiRequest, getPersistedAiRequest(returnedAiRequest));

        insertedAiRequest = returnedAiRequest;
    }

    @Test
    @Transactional
    void createAiRequestWithExistingId() throws Exception {
        // Create the AiRequest with an existing ID
        aiRequest.setId(1L);
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setTenantId(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setEmployeeId(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkToolTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setToolType(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPromptIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setPrompt(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkResponseIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setResponse(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRequestTimeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setRequestTime(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setStatus(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setVersion(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setCreatedAt(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setUpdatedAt(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        aiRequest.setIsDeleted(null);

        // Create the AiRequest, which fails.
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        restAiRequestMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAiRequests() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        // Get all the aiRequestList
        restAiRequestMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(aiRequest.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].toolType").value(hasItem(DEFAULT_TOOL_TYPE)))
            .andExpect(jsonPath("$.[*].prompt").value(hasItem(DEFAULT_PROMPT)))
            .andExpect(jsonPath("$.[*].response").value(hasItem(DEFAULT_RESPONSE)))
            .andExpect(jsonPath("$.[*].requestTime").value(hasItem(DEFAULT_REQUEST_TIME.toString())))
            .andExpect(jsonPath("$.[*].responseTime").value(hasItem(DEFAULT_RESPONSE_TIME.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].errorMessage").value(hasItem(DEFAULT_ERROR_MESSAGE)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getAiRequest() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        // Get the aiRequest
        restAiRequestMockMvc
            .perform(get(ENTITY_API_URL_ID, aiRequest.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(aiRequest.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.toolType").value(DEFAULT_TOOL_TYPE))
            .andExpect(jsonPath("$.prompt").value(DEFAULT_PROMPT))
            .andExpect(jsonPath("$.response").value(DEFAULT_RESPONSE))
            .andExpect(jsonPath("$.requestTime").value(DEFAULT_REQUEST_TIME.toString()))
            .andExpect(jsonPath("$.responseTime").value(DEFAULT_RESPONSE_TIME.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.errorMessage").value(DEFAULT_ERROR_MESSAGE))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingAiRequest() throws Exception {
        // Get the aiRequest
        restAiRequestMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAiRequest() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiRequest
        AiRequest updatedAiRequest = aiRequestRepository.findById(aiRequest.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAiRequest are not directly saved in db
        em.detach(updatedAiRequest);
        updatedAiRequest
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .toolType(UPDATED_TOOL_TYPE)
            .prompt(UPDATED_PROMPT)
            .response(UPDATED_RESPONSE)
            .requestTime(UPDATED_REQUEST_TIME)
            .responseTime(UPDATED_RESPONSE_TIME)
            .status(UPDATED_STATUS)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(updatedAiRequest);

        restAiRequestMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiRequestDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiRequestDTO))
            )
            .andExpect(status().isOk());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAiRequestToMatchAllProperties(updatedAiRequest);
    }

    @Test
    @Transactional
    void putNonExistingAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(
                put(ENTITY_API_URL_ID, aiRequestDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiRequestDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(aiRequestDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAiRequestWithPatch() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiRequest using partial update
        AiRequest partialUpdatedAiRequest = new AiRequest();
        partialUpdatedAiRequest.setId(aiRequest.getId());

        partialUpdatedAiRequest
            .response(UPDATED_RESPONSE)
            .requestTime(UPDATED_REQUEST_TIME)
            .responseTime(UPDATED_RESPONSE_TIME)
            .status(UPDATED_STATUS)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY);

        restAiRequestMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiRequest.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiRequest))
            )
            .andExpect(status().isOk());

        // Validate the AiRequest in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiRequestUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAiRequest, aiRequest),
            getPersistedAiRequest(aiRequest)
        );
    }

    @Test
    @Transactional
    void fullUpdateAiRequestWithPatch() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the aiRequest using partial update
        AiRequest partialUpdatedAiRequest = new AiRequest();
        partialUpdatedAiRequest.setId(aiRequest.getId());

        partialUpdatedAiRequest
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .toolType(UPDATED_TOOL_TYPE)
            .prompt(UPDATED_PROMPT)
            .response(UPDATED_RESPONSE)
            .requestTime(UPDATED_REQUEST_TIME)
            .responseTime(UPDATED_RESPONSE_TIME)
            .status(UPDATED_STATUS)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restAiRequestMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAiRequest.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAiRequest))
            )
            .andExpect(status().isOk());

        // Validate the AiRequest in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAiRequestUpdatableFieldsEquals(partialUpdatedAiRequest, getPersistedAiRequest(partialUpdatedAiRequest));
    }

    @Test
    @Transactional
    void patchNonExistingAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, aiRequestDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiRequestDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(aiRequestDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAiRequest() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        aiRequest.setId(longCount.incrementAndGet());

        // Create the AiRequest
        AiRequestDTO aiRequestDTO = aiRequestMapper.toDto(aiRequest);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAiRequestMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(aiRequestDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the AiRequest in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAiRequest() throws Exception {
        // Initialize the database
        insertedAiRequest = aiRequestRepository.saveAndFlush(aiRequest);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the aiRequest
        restAiRequestMockMvc
            .perform(delete(ENTITY_API_URL_ID, aiRequest.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return aiRequestRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected AiRequest getPersistedAiRequest(AiRequest aiRequest) {
        return aiRequestRepository.findById(aiRequest.getId()).orElseThrow();
    }

    protected void assertPersistedAiRequestToMatchAllProperties(AiRequest expectedAiRequest) {
        assertAiRequestAllPropertiesEquals(expectedAiRequest, getPersistedAiRequest(expectedAiRequest));
    }

    protected void assertPersistedAiRequestToMatchUpdatableProperties(AiRequest expectedAiRequest) {
        assertAiRequestAllUpdatablePropertiesEquals(expectedAiRequest, getPersistedAiRequest(expectedAiRequest));
    }

    @Test
    @Transactional
    void getAiRequestsByEmployeeId() throws Exception {
        // Initialize the database with multiple requests for different employees
        AiRequest aiRequest1 = createEntity().employeeId(1L);
        AiRequest aiRequest2 = createEntity().employeeId(1L);
        AiRequest aiRequest3 = createEntity().employeeId(2L);

        aiRequestRepository.saveAndFlush(aiRequest1);
        aiRequestRepository.saveAndFlush(aiRequest2);
        aiRequestRepository.saveAndFlush(aiRequest3);

        // Get AI requests for employee 1
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/1?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(2))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(1)));
    }

    @Test
    @Transactional
    void getAiRequestsByEmployeeIdAndStatus() throws Exception {
        // Initialize the database with requests of different statuses
        AiRequest aiRequest1 = createEntity().employeeId(1L).status(RequestStatus.SUCCESS);
        AiRequest aiRequest2 = createEntity().employeeId(1L).status(RequestStatus.FAILED);
        AiRequest aiRequest3 = createEntity().employeeId(1L).status(RequestStatus.SUCCESS);

        aiRequestRepository.saveAndFlush(aiRequest1);
        aiRequestRepository.saveAndFlush(aiRequest2);
        aiRequestRepository.saveAndFlush(aiRequest3);

        // Get successful AI requests for employee 1
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/1/status/SUCCESS?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(2))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(1)))
            .andExpect(jsonPath("$.[*].status").value(hasItem("SUCCESS")));
    }

    @Test
    @Transactional
    void getAiRequestsByEmployeeIdAndToolType() throws Exception {
        // Initialize the database with requests of different tool types
        AiRequest aiRequest1 = createEntity().employeeId(1L).toolType("CHAT_GPT");
        AiRequest aiRequest2 = createEntity().employeeId(1L).toolType("CLAUDE");
        AiRequest aiRequest3 = createEntity().employeeId(1L).toolType("CHAT_GPT");

        aiRequestRepository.saveAndFlush(aiRequest1);
        aiRequestRepository.saveAndFlush(aiRequest2);
        aiRequestRepository.saveAndFlush(aiRequest3);

        // Get CHAT_GPT AI requests for employee 1
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/1/tool-type/CHAT_GPT?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(2))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(1)))
            .andExpect(jsonPath("$.[*].toolType").value(hasItem("CHAT_GPT")));
    }

    @Test
    @Transactional
    void getAiRequestsByEmployeeIdWithPagination() throws Exception {
        // Initialize the database with multiple requests
        for (int i = 0; i < 25; i++) {
            AiRequest request = createEntity().employeeId(1L);
            aiRequestRepository.saveAndFlush(request);
        }

        // Get first page (size=20)
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/1?page=0&size=20&sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(20))
            .andExpect(header().string("X-Total-Count", "25"));

        // Get second page (size=20)
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/1?page=1&size=20&sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(5))
            .andExpect(header().string("X-Total-Count", "25"));
    }

    @Test
    @Transactional
    void getAiRequestsByNonExistentEmployeeId() throws Exception {
        // Get AI requests for non-existent employee
        restAiRequestMockMvc
            .perform(get("/api/ai-requests/employee/999?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(0));
    }
}
