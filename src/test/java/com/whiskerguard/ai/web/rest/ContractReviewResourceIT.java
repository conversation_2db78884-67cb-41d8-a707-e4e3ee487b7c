package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.ContractReviewAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.domain.enumeration.ReviewStatus;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractReviewRepository;
import com.whiskerguard.ai.service.dto.ContractReviewDTO;
import com.whiskerguard.ai.service.mapper.ContractReviewMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ContractReviewResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ContractReviewResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_CONTRACT_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_CONTRACT_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTRACT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_CONTRACT_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTRACT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTRACT_CONTENT = "BBBBBBBBBB";

    private static final String DEFAULT_REVIEW_RESULT = "AAAAAAAAAA";
    private static final String UPDATED_REVIEW_RESULT = "BBBBBBBBBB";

    private static final ReviewStatus DEFAULT_STATUS = ReviewStatus.PENDING;
    private static final ReviewStatus UPDATED_STATUS = ReviewStatus.PROCESSING;

    private static final RiskLevel DEFAULT_OVERALL_RISK_LEVEL = RiskLevel.HIGH;
    private static final RiskLevel UPDATED_OVERALL_RISK_LEVEL = RiskLevel.MEDIUM;

    private static final Integer DEFAULT_RISK_SCORE = 0;
    private static final Integer UPDATED_RISK_SCORE = 1;

    private static final String DEFAULT_RISK_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_RISK_SUMMARY = "BBBBBBBBBB";

    private static final Long DEFAULT_AI_REQUEST_ID = 1L;
    private static final Long UPDATED_AI_REQUEST_ID = 2L;

    private static final Instant DEFAULT_REVIEW_START_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_REVIEW_START_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_REVIEW_END_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_REVIEW_END_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_REVIEW_DURATION = 1L;
    private static final Long UPDATED_REVIEW_DURATION = 2L;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/contract-reviews";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ContractReviewRepository contractReviewRepository;

    @Autowired
    private ContractReviewMapper contractReviewMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restContractReviewMockMvc;

    private ContractReview contractReview;

    private ContractReview insertedContractReview;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractReview createEntity() {
        return new ContractReview()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .contractType(DEFAULT_CONTRACT_TYPE)
            .contractTitle(DEFAULT_CONTRACT_TITLE)
            .contractContent(DEFAULT_CONTRACT_CONTENT)
            .reviewResult(DEFAULT_REVIEW_RESULT)
            .status(DEFAULT_STATUS)
            .overallRiskLevel(DEFAULT_OVERALL_RISK_LEVEL)
            .riskScore(DEFAULT_RISK_SCORE)
            .riskSummary(DEFAULT_RISK_SUMMARY)
            .aiRequestId(DEFAULT_AI_REQUEST_ID)
            .reviewStartTime(DEFAULT_REVIEW_START_TIME)
            .reviewEndTime(DEFAULT_REVIEW_END_TIME)
            .reviewDuration(DEFAULT_REVIEW_DURATION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ContractReview createUpdatedEntity() {
        return new ContractReview()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .contractType(UPDATED_CONTRACT_TYPE)
            .contractTitle(UPDATED_CONTRACT_TITLE)
            .contractContent(UPDATED_CONTRACT_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .status(UPDATED_STATUS)
            .overallRiskLevel(UPDATED_OVERALL_RISK_LEVEL)
            .riskScore(UPDATED_RISK_SCORE)
            .riskSummary(UPDATED_RISK_SUMMARY)
            .aiRequestId(UPDATED_AI_REQUEST_ID)
            .reviewStartTime(UPDATED_REVIEW_START_TIME)
            .reviewEndTime(UPDATED_REVIEW_END_TIME)
            .reviewDuration(UPDATED_REVIEW_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        contractReview = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedContractReview != null) {
            contractReviewRepository.delete(insertedContractReview);
            insertedContractReview = null;
        }
    }

    @Test
    @Transactional
    void createContractReview() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);
        var returnedContractReviewDTO = om.readValue(
            restContractReviewMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ContractReviewDTO.class
        );

        // Validate the ContractReview in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedContractReview = contractReviewMapper.toEntity(returnedContractReviewDTO);
        assertContractReviewUpdatableFieldsEquals(returnedContractReview, getPersistedContractReview(returnedContractReview));

        insertedContractReview = returnedContractReview;
    }

    @Test
    @Transactional
    void createContractReviewWithExistingId() throws Exception {
        // Create the ContractReview with an existing ID
        contractReview.setId(1L);
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setTenantId(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setEmployeeId(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setStatus(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setVersion(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setCreatedAt(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        contractReview.setIsDeleted(null);

        // Create the ContractReview, which fails.
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        restContractReviewMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllContractReviews() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        // Get all the contractReviewList
        restContractReviewMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(contractReview.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].contractType").value(hasItem(DEFAULT_CONTRACT_TYPE)))
            .andExpect(jsonPath("$.[*].contractTitle").value(hasItem(DEFAULT_CONTRACT_TITLE)))
            .andExpect(jsonPath("$.[*].contractContent").value(hasItem(DEFAULT_CONTRACT_CONTENT)))
            .andExpect(jsonPath("$.[*].reviewResult").value(hasItem(DEFAULT_REVIEW_RESULT)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].overallRiskLevel").value(hasItem(DEFAULT_OVERALL_RISK_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].riskScore").value(hasItem(DEFAULT_RISK_SCORE)))
            .andExpect(jsonPath("$.[*].riskSummary").value(hasItem(DEFAULT_RISK_SUMMARY)))
            .andExpect(jsonPath("$.[*].aiRequestId").value(hasItem(DEFAULT_AI_REQUEST_ID.intValue())))
            .andExpect(jsonPath("$.[*].reviewStartTime").value(hasItem(DEFAULT_REVIEW_START_TIME.toString())))
            .andExpect(jsonPath("$.[*].reviewEndTime").value(hasItem(DEFAULT_REVIEW_END_TIME.toString())))
            .andExpect(jsonPath("$.[*].reviewDuration").value(hasItem(DEFAULT_REVIEW_DURATION.intValue())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getContractReview() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        // Get the contractReview
        restContractReviewMockMvc
            .perform(get(ENTITY_API_URL_ID, contractReview.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(contractReview.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.contractType").value(DEFAULT_CONTRACT_TYPE))
            .andExpect(jsonPath("$.contractTitle").value(DEFAULT_CONTRACT_TITLE))
            .andExpect(jsonPath("$.contractContent").value(DEFAULT_CONTRACT_CONTENT))
            .andExpect(jsonPath("$.reviewResult").value(DEFAULT_REVIEW_RESULT))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.overallRiskLevel").value(DEFAULT_OVERALL_RISK_LEVEL.toString()))
            .andExpect(jsonPath("$.riskScore").value(DEFAULT_RISK_SCORE))
            .andExpect(jsonPath("$.riskSummary").value(DEFAULT_RISK_SUMMARY))
            .andExpect(jsonPath("$.aiRequestId").value(DEFAULT_AI_REQUEST_ID.intValue()))
            .andExpect(jsonPath("$.reviewStartTime").value(DEFAULT_REVIEW_START_TIME.toString()))
            .andExpect(jsonPath("$.reviewEndTime").value(DEFAULT_REVIEW_END_TIME.toString()))
            .andExpect(jsonPath("$.reviewDuration").value(DEFAULT_REVIEW_DURATION.intValue()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingContractReview() throws Exception {
        // Get the contractReview
        restContractReviewMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingContractReview() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractReview
        ContractReview updatedContractReview = contractReviewRepository.findById(contractReview.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedContractReview are not directly saved in db
        em.detach(updatedContractReview);
        updatedContractReview
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .contractType(UPDATED_CONTRACT_TYPE)
            .contractTitle(UPDATED_CONTRACT_TITLE)
            .contractContent(UPDATED_CONTRACT_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .status(UPDATED_STATUS)
            .overallRiskLevel(UPDATED_OVERALL_RISK_LEVEL)
            .riskScore(UPDATED_RISK_SCORE)
            .riskSummary(UPDATED_RISK_SUMMARY)
            .aiRequestId(UPDATED_AI_REQUEST_ID)
            .reviewStartTime(UPDATED_REVIEW_START_TIME)
            .reviewEndTime(UPDATED_REVIEW_END_TIME)
            .reviewDuration(UPDATED_REVIEW_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(updatedContractReview);

        restContractReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractReviewDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractReviewDTO))
            )
            .andExpect(status().isOk());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedContractReviewToMatchAllProperties(updatedContractReview);
    }

    @Test
    @Transactional
    void putNonExistingContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, contractReviewDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(contractReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateContractReviewWithPatch() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractReview using partial update
        ContractReview partialUpdatedContractReview = new ContractReview();
        partialUpdatedContractReview.setId(contractReview.getId());

        partialUpdatedContractReview
            .contractType(UPDATED_CONTRACT_TYPE)
            .contractContent(UPDATED_CONTRACT_CONTENT)
            .riskSummary(UPDATED_RISK_SUMMARY)
            .reviewStartTime(UPDATED_REVIEW_START_TIME)
            .reviewEndTime(UPDATED_REVIEW_END_TIME)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractReview.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractReview))
            )
            .andExpect(status().isOk());

        // Validate the ContractReview in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractReviewUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedContractReview, contractReview),
            getPersistedContractReview(contractReview)
        );
    }

    @Test
    @Transactional
    void fullUpdateContractReviewWithPatch() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the contractReview using partial update
        ContractReview partialUpdatedContractReview = new ContractReview();
        partialUpdatedContractReview.setId(contractReview.getId());

        partialUpdatedContractReview
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .contractType(UPDATED_CONTRACT_TYPE)
            .contractTitle(UPDATED_CONTRACT_TITLE)
            .contractContent(UPDATED_CONTRACT_CONTENT)
            .reviewResult(UPDATED_REVIEW_RESULT)
            .status(UPDATED_STATUS)
            .overallRiskLevel(UPDATED_OVERALL_RISK_LEVEL)
            .riskScore(UPDATED_RISK_SCORE)
            .riskSummary(UPDATED_RISK_SUMMARY)
            .aiRequestId(UPDATED_AI_REQUEST_ID)
            .reviewStartTime(UPDATED_REVIEW_START_TIME)
            .reviewEndTime(UPDATED_REVIEW_END_TIME)
            .reviewDuration(UPDATED_REVIEW_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restContractReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedContractReview.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedContractReview))
            )
            .andExpect(status().isOk());

        // Validate the ContractReview in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertContractReviewUpdatableFieldsEquals(partialUpdatedContractReview, getPersistedContractReview(partialUpdatedContractReview));
    }

    @Test
    @Transactional
    void patchNonExistingContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, contractReviewDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(contractReviewDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamContractReview() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        contractReview.setId(longCount.incrementAndGet());

        // Create the ContractReview
        ContractReviewDTO contractReviewDTO = contractReviewMapper.toDto(contractReview);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restContractReviewMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(contractReviewDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ContractReview in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteContractReview() throws Exception {
        // Initialize the database
        insertedContractReview = contractReviewRepository.saveAndFlush(contractReview);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the contractReview
        restContractReviewMockMvc
            .perform(delete(ENTITY_API_URL_ID, contractReview.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return contractReviewRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ContractReview getPersistedContractReview(ContractReview contractReview) {
        return contractReviewRepository.findById(contractReview.getId()).orElseThrow();
    }

    protected void assertPersistedContractReviewToMatchAllProperties(ContractReview expectedContractReview) {
        assertContractReviewAllPropertiesEquals(expectedContractReview, getPersistedContractReview(expectedContractReview));
    }

    protected void assertPersistedContractReviewToMatchUpdatableProperties(ContractReview expectedContractReview) {
        assertContractReviewAllUpdatablePropertiesEquals(expectedContractReview, getPersistedContractReview(expectedContractReview));
    }
}
