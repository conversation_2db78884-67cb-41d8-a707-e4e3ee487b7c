package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromptTemplateVersionDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplateVersionDTO.class);
        PromptTemplateVersionDTO promptTemplateVersionDTO1 = new PromptTemplateVersionDTO();
        promptTemplateVersionDTO1.setId(1L);
        PromptTemplateVersionDTO promptTemplateVersionDTO2 = new PromptTemplateVersionDTO();
        assertThat(promptTemplateVersionDTO1).isNotEqualTo(promptTemplateVersionDTO2);
        promptTemplateVersionDTO2.setId(promptTemplateVersionDTO1.getId());
        assertThat(promptTemplateVersionDTO1).isEqualTo(promptTemplateVersionDTO2);
        promptTemplateVersionDTO2.setId(2L);
        assertThat(promptTemplateVersionDTO1).isNotEqualTo(promptTemplateVersionDTO2);
        promptTemplateVersionDTO1.setId(null);
        assertThat(promptTemplateVersionDTO1).isNotEqualTo(promptTemplateVersionDTO2);
    }
}
