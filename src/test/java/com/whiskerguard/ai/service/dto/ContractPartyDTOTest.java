package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractPartyDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractPartyDTO.class);
        ContractPartyDTO contractPartyDTO1 = new ContractPartyDTO();
        contractPartyDTO1.setId(1L);
        ContractPartyDTO contractPartyDTO2 = new ContractPartyDTO();
        assertThat(contractPartyDTO1).isNotEqualTo(contractPartyDTO2);
        contractPartyDTO2.setId(contractPartyDTO1.getId());
        assertThat(contractPartyDTO1).isEqualTo(contractPartyDTO2);
        contractPartyDTO2.setId(2L);
        assertThat(contractPartyDTO1).isNotEqualTo(contractPartyDTO2);
        contractPartyDTO1.setId(null);
        assertThat(contractPartyDTO1).isNotEqualTo(contractPartyDTO2);
    }
}
