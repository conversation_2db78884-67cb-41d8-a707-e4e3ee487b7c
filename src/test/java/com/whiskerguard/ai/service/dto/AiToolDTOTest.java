package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiToolDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiToolDTO.class);
        AiToolDTO aiToolDTO1 = new AiToolDTO();
        aiToolDTO1.setId(1L);
        AiToolDTO aiToolDTO2 = new AiToolDTO();
        assertThat(aiToolDTO1).isNotEqualTo(aiToolDTO2);
        aiToolDTO2.setId(aiToolDTO1.getId());
        assertThat(aiToolDTO1).isEqualTo(aiToolDTO2);
        aiToolDTO2.setId(2L);
        assertThat(aiToolDTO1).isNotEqualTo(aiToolDTO2);
        aiToolDTO1.setId(null);
        assertThat(aiToolDTO1).isNotEqualTo(aiToolDTO2);
    }
}
