package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiReviewDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiReviewDTO.class);
        AiReviewDTO aiReviewDTO1 = new AiReviewDTO();
        aiReviewDTO1.setId(1L);
        AiReviewDTO aiReviewDTO2 = new AiReviewDTO();
        assertThat(aiReviewDTO1).isNotEqualTo(aiReviewDTO2);
        aiReviewDTO2.setId(aiReviewDTO1.getId());
        assertThat(aiReviewDTO1).isEqualTo(aiReviewDTO2);
        aiReviewDTO2.setId(2L);
        assertThat(aiReviewDTO1).isNotEqualTo(aiReviewDTO2);
        aiReviewDTO1.setId(null);
        assertThat(aiReviewDTO1).isNotEqualTo(aiReviewDTO2);
    }
}
