package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentContextDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentContextDTO.class);
        AgentContextDTO agentContextDTO1 = new AgentContextDTO();
        agentContextDTO1.setId(1L);
        AgentContextDTO agentContextDTO2 = new AgentContextDTO();
        assertThat(agentContextDTO1).isNotEqualTo(agentContextDTO2);
        agentContextDTO2.setId(agentContextDTO1.getId());
        assertThat(agentContextDTO1).isEqualTo(agentContextDTO2);
        agentContextDTO2.setId(2L);
        assertThat(agentContextDTO1).isNotEqualTo(agentContextDTO2);
        agentContextDTO1.setId(null);
        assertThat(agentContextDTO1).isNotEqualTo(agentContextDTO2);
    }
}
