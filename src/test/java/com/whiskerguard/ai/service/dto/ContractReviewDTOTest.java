package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractReviewDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractReviewDTO.class);
        ContractReviewDTO contractReviewDTO1 = new ContractReviewDTO();
        contractReviewDTO1.setId(1L);
        ContractReviewDTO contractReviewDTO2 = new ContractReviewDTO();
        assertThat(contractReviewDTO1).isNotEqualTo(contractReviewDTO2);
        contractReviewDTO2.setId(contractReviewDTO1.getId());
        assertThat(contractReviewDTO1).isEqualTo(contractReviewDTO2);
        contractReviewDTO2.setId(2L);
        assertThat(contractReviewDTO1).isNotEqualTo(contractReviewDTO2);
        contractReviewDTO1.setId(null);
        assertThat(contractReviewDTO1).isNotEqualTo(contractReviewDTO2);
    }
}
