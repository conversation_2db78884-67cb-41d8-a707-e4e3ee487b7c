package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractClauseIssueDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractClauseIssueDTO.class);
        ContractClauseIssueDTO contractClauseIssueDTO1 = new ContractClauseIssueDTO();
        contractClauseIssueDTO1.setId(1L);
        ContractClauseIssueDTO contractClauseIssueDTO2 = new ContractClauseIssueDTO();
        assertThat(contractClauseIssueDTO1).isNotEqualTo(contractClauseIssueDTO2);
        contractClauseIssueDTO2.setId(contractClauseIssueDTO1.getId());
        assertThat(contractClauseIssueDTO1).isEqualTo(contractClauseIssueDTO2);
        contractClauseIssueDTO2.setId(2L);
        assertThat(contractClauseIssueDTO1).isNotEqualTo(contractClauseIssueDTO2);
        contractClauseIssueDTO1.setId(null);
        assertThat(contractClauseIssueDTO1).isNotEqualTo(contractClauseIssueDTO2);
    }
}
