package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentConfigDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentConfigDTO.class);
        AgentConfigDTO agentConfigDTO1 = new AgentConfigDTO();
        agentConfigDTO1.setId(1L);
        AgentConfigDTO agentConfigDTO2 = new AgentConfigDTO();
        assertThat(agentConfigDTO1).isNotEqualTo(agentConfigDTO2);
        agentConfigDTO2.setId(agentConfigDTO1.getId());
        assertThat(agentConfigDTO1).isEqualTo(agentConfigDTO2);
        agentConfigDTO2.setId(2L);
        assertThat(agentConfigDTO1).isNotEqualTo(agentConfigDTO2);
        agentConfigDTO1.setId(null);
        assertThat(agentConfigDTO1).isNotEqualTo(agentConfigDTO2);
    }
}
