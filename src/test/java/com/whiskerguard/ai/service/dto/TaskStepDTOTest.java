package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TaskStepDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TaskStepDTO.class);
        TaskStepDTO taskStepDTO1 = new TaskStepDTO();
        taskStepDTO1.setId(1L);
        TaskStepDTO taskStepDTO2 = new TaskStepDTO();
        assertThat(taskStepDTO1).isNotEqualTo(taskStepDTO2);
        taskStepDTO2.setId(taskStepDTO1.getId());
        assertThat(taskStepDTO1).isEqualTo(taskStepDTO2);
        taskStepDTO2.setId(2L);
        assertThat(taskStepDTO1).isNotEqualTo(taskStepDTO2);
        taskStepDTO1.setId(null);
        assertThat(taskStepDTO1).isNotEqualTo(taskStepDTO2);
    }
}
