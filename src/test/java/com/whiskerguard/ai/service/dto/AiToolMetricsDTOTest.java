package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiToolMetricsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiToolMetricsDTO.class);
        AiToolMetricsDTO aiToolMetricsDTO1 = new AiToolMetricsDTO();
        aiToolMetricsDTO1.setId(1L);
        AiToolMetricsDTO aiToolMetricsDTO2 = new AiToolMetricsDTO();
        assertThat(aiToolMetricsDTO1).isNotEqualTo(aiToolMetricsDTO2);
        aiToolMetricsDTO2.setId(aiToolMetricsDTO1.getId());
        assertThat(aiToolMetricsDTO1).isEqualTo(aiToolMetricsDTO2);
        aiToolMetricsDTO2.setId(2L);
        assertThat(aiToolMetricsDTO1).isNotEqualTo(aiToolMetricsDTO2);
        aiToolMetricsDTO1.setId(null);
        assertThat(aiToolMetricsDTO1).isNotEqualTo(aiToolMetricsDTO2);
    }
}
