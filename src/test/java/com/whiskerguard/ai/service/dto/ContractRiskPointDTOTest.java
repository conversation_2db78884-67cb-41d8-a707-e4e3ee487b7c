package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ContractRiskPointDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ContractRiskPointDTO.class);
        ContractRiskPointDTO contractRiskPointDTO1 = new ContractRiskPointDTO();
        contractRiskPointDTO1.setId(1L);
        ContractRiskPointDTO contractRiskPointDTO2 = new ContractRiskPointDTO();
        assertThat(contractRiskPointDTO1).isNotEqualTo(contractRiskPointDTO2);
        contractRiskPointDTO2.setId(contractRiskPointDTO1.getId());
        assertThat(contractRiskPointDTO1).isEqualTo(contractRiskPointDTO2);
        contractRiskPointDTO2.setId(2L);
        assertThat(contractRiskPointDTO1).isNotEqualTo(contractRiskPointDTO2);
        contractRiskPointDTO1.setId(null);
        assertThat(contractRiskPointDTO1).isNotEqualTo(contractRiskPointDTO2);
    }
}
