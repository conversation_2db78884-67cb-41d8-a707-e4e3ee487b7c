package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class KnowledgeCacheDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(KnowledgeCacheDTO.class);
        KnowledgeCacheDTO knowledgeCacheDTO1 = new KnowledgeCacheDTO();
        knowledgeCacheDTO1.setId(1L);
        KnowledgeCacheDTO knowledgeCacheDTO2 = new KnowledgeCacheDTO();
        assertThat(knowledgeCacheDTO1).isNotEqualTo(knowledgeCacheDTO2);
        knowledgeCacheDTO2.setId(knowledgeCacheDTO1.getId());
        assertThat(knowledgeCacheDTO1).isEqualTo(knowledgeCacheDTO2);
        knowledgeCacheDTO2.setId(2L);
        assertThat(knowledgeCacheDTO1).isNotEqualTo(knowledgeCacheDTO2);
        knowledgeCacheDTO1.setId(null);
        assertThat(knowledgeCacheDTO1).isNotEqualTo(knowledgeCacheDTO2);
    }
}
