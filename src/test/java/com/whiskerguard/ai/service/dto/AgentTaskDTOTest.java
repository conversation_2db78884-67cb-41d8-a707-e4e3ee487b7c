package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentTaskDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentTaskDTO.class);
        AgentTaskDTO agentTaskDTO1 = new AgentTaskDTO();
        agentTaskDTO1.setId(1L);
        AgentTaskDTO agentTaskDTO2 = new AgentTaskDTO();
        assertThat(agentTaskDTO1).isNotEqualTo(agentTaskDTO2);
        agentTaskDTO2.setId(agentTaskDTO1.getId());
        assertThat(agentTaskDTO1).isEqualTo(agentTaskDTO2);
        agentTaskDTO2.setId(2L);
        assertThat(agentTaskDTO1).isNotEqualTo(agentTaskDTO2);
        agentTaskDTO1.setId(null);
        assertThat(agentTaskDTO1).isNotEqualTo(agentTaskDTO2);
    }
}
