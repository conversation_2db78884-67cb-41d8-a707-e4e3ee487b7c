package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantPromptConfigDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantPromptConfigDTO.class);
        TenantPromptConfigDTO tenantPromptConfigDTO1 = new TenantPromptConfigDTO();
        tenantPromptConfigDTO1.setId(1L);
        TenantPromptConfigDTO tenantPromptConfigDTO2 = new TenantPromptConfigDTO();
        assertThat(tenantPromptConfigDTO1).isNotEqualTo(tenantPromptConfigDTO2);
        tenantPromptConfigDTO2.setId(tenantPromptConfigDTO1.getId());
        assertThat(tenantPromptConfigDTO1).isEqualTo(tenantPromptConfigDTO2);
        tenantPromptConfigDTO2.setId(2L);
        assertThat(tenantPromptConfigDTO1).isNotEqualTo(tenantPromptConfigDTO2);
        tenantPromptConfigDTO1.setId(null);
        assertThat(tenantPromptConfigDTO1).isNotEqualTo(tenantPromptConfigDTO2);
    }
}
