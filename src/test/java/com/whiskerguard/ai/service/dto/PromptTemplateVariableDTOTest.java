package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromptTemplateVariableDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplateVariableDTO.class);
        PromptTemplateVariableDTO promptTemplateVariableDTO1 = new PromptTemplateVariableDTO();
        promptTemplateVariableDTO1.setId(1L);
        PromptTemplateVariableDTO promptTemplateVariableDTO2 = new PromptTemplateVariableDTO();
        assertThat(promptTemplateVariableDTO1).isNotEqualTo(promptTemplateVariableDTO2);
        promptTemplateVariableDTO2.setId(promptTemplateVariableDTO1.getId());
        assertThat(promptTemplateVariableDTO1).isEqualTo(promptTemplateVariableDTO2);
        promptTemplateVariableDTO2.setId(2L);
        assertThat(promptTemplateVariableDTO1).isNotEqualTo(promptTemplateVariableDTO2);
        promptTemplateVariableDTO1.setId(null);
        assertThat(promptTemplateVariableDTO1).isNotEqualTo(promptTemplateVariableDTO2);
    }
}
