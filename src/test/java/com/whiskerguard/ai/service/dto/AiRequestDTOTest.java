package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AiRequestDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AiRequestDTO.class);
        AiRequestDTO aiRequestDTO1 = new AiRequestDTO();
        aiRequestDTO1.setId(1L);
        AiRequestDTO aiRequestDTO2 = new AiRequestDTO();
        assertThat(aiRequestDTO1).isNotEqualTo(aiRequestDTO2);
        aiRequestDTO2.setId(aiRequestDTO1.getId());
        assertThat(aiRequestDTO1).isEqualTo(aiRequestDTO2);
        aiRequestDTO2.setId(2L);
        assertThat(aiRequestDTO1).isNotEqualTo(aiRequestDTO2);
        aiRequestDTO1.setId(null);
        assertThat(aiRequestDTO1).isNotEqualTo(aiRequestDTO2);
    }
}
