package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PromptTemplateDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PromptTemplateDTO.class);
        PromptTemplateDTO promptTemplateDTO1 = new PromptTemplateDTO();
        promptTemplateDTO1.setId(1L);
        PromptTemplateDTO promptTemplateDTO2 = new PromptTemplateDTO();
        assertThat(promptTemplateDTO1).isNotEqualTo(promptTemplateDTO2);
        promptTemplateDTO2.setId(promptTemplateDTO1.getId());
        assertThat(promptTemplateDTO1).isEqualTo(promptTemplateDTO2);
        promptTemplateDTO2.setId(2L);
        assertThat(promptTemplateDTO1).isNotEqualTo(promptTemplateDTO2);
        promptTemplateDTO1.setId(null);
        assertThat(promptTemplateDTO1).isNotEqualTo(promptTemplateDTO2);
    }
}
