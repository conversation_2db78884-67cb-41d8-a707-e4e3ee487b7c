package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AiRequestAsserts.*;
import static com.whiskerguard.ai.domain.AiRequestTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AiRequestMapperTest {

    private AiRequestMapper aiRequestMapper;

    @BeforeEach
    void setUp() {
        aiRequestMapper = new AiRequestMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAiRequestSample1();
        var actual = aiRequestMapper.toEntity(aiRequestMapper.toDto(expected));
        assertAiRequestAllPropertiesEquals(expected, actual);
    }
}
