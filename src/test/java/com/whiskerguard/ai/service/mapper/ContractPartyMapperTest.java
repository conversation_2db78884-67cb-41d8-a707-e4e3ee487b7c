package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.ContractPartyAsserts.*;
import static com.whiskerguard.ai.domain.ContractPartyTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ContractPartyMapperTest {

    private ContractPartyMapper contractPartyMapper;

    @BeforeEach
    void setUp() {
        contractPartyMapper = new ContractPartyMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getContractPartySample1();
        var actual = contractPartyMapper.toEntity(contractPartyMapper.toDto(expected));
        assertContractPartyAllPropertiesEquals(expected, actual);
    }
}
