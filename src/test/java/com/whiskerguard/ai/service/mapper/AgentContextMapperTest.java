package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AgentContextAsserts.*;
import static com.whiskerguard.ai.domain.AgentContextTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AgentContextMapperTest {

    private AgentContextMapper agentContextMapper;

    @BeforeEach
    void setUp() {
        agentContextMapper = new AgentContextMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAgentContextSample1();
        var actual = agentContextMapper.toEntity(agentContextMapper.toDto(expected));
        assertAgentContextAllPropertiesEquals(expected, actual);
    }
}
