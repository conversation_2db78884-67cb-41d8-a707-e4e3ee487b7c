package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.PromptTemplateVersionAsserts.*;
import static com.whiskerguard.ai.domain.PromptTemplateVersionTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PromptTemplateVersionMapperTest {

    private PromptTemplateVersionMapper promptTemplateVersionMapper;

    @BeforeEach
    void setUp() {
        promptTemplateVersionMapper = new PromptTemplateVersionMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPromptTemplateVersionSample1();
        var actual = promptTemplateVersionMapper.toEntity(promptTemplateVersionMapper.toDto(expected));
        assertPromptTemplateVersionAllPropertiesEquals(expected, actual);
    }
}
