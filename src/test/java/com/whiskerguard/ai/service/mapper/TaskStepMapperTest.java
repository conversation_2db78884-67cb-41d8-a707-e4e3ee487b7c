package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.TaskStepAsserts.*;
import static com.whiskerguard.ai.domain.TaskStepTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TaskStepMapperTest {

    private TaskStepMapper taskStepMapper;

    @BeforeEach
    void setUp() {
        taskStepMapper = new TaskStepMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTaskStepSample1();
        var actual = taskStepMapper.toEntity(taskStepMapper.toDto(expected));
        assertTaskStepAllPropertiesEquals(expected, actual);
    }
}
