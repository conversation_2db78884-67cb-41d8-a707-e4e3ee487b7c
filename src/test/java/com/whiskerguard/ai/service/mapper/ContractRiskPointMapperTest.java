package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.ContractRiskPointAsserts.*;
import static com.whiskerguard.ai.domain.ContractRiskPointTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ContractRiskPointMapperTest {

    private ContractRiskPointMapper contractRiskPointMapper;

    @BeforeEach
    void setUp() {
        contractRiskPointMapper = new ContractRiskPointMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getContractRiskPointSample1();
        var actual = contractRiskPointMapper.toEntity(contractRiskPointMapper.toDto(expected));
        assertContractRiskPointAllPropertiesEquals(expected, actual);
    }
}
