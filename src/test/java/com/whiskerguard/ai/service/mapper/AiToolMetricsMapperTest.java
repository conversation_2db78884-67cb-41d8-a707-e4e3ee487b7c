package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AiToolMetricsAsserts.*;
import static com.whiskerguard.ai.domain.AiToolMetricsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AiToolMetricsMapperTest {

    private AiToolMetricsMapper aiToolMetricsMapper;

    @BeforeEach
    void setUp() {
        aiToolMetricsMapper = new AiToolMetricsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAiToolMetricsSample1();
        var actual = aiToolMetricsMapper.toEntity(aiToolMetricsMapper.toDto(expected));
        assertAiToolMetricsAllPropertiesEquals(expected, actual);
    }
}
