package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AiReviewAsserts.*;
import static com.whiskerguard.ai.domain.AiReviewTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AiReviewMapperTest {

    private AiReviewMapper aiReviewMapper;

    @BeforeEach
    void setUp() {
        aiReviewMapper = new AiReviewMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAiReviewSample1();
        var actual = aiReviewMapper.toEntity(aiReviewMapper.toDto(expected));
        assertAiReviewAllPropertiesEquals(expected, actual);
    }
}
