package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.PromptTemplateVariableAsserts.*;
import static com.whiskerguard.ai.domain.PromptTemplateVariableTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PromptTemplateVariableMapperTest {

    private PromptTemplateVariableMapper promptTemplateVariableMapper;

    @BeforeEach
    void setUp() {
        promptTemplateVariableMapper = new PromptTemplateVariableMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPromptTemplateVariableSample1();
        var actual = promptTemplateVariableMapper.toEntity(promptTemplateVariableMapper.toDto(expected));
        assertPromptTemplateVariableAllPropertiesEquals(expected, actual);
    }
}
