package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AgentTaskAsserts.*;
import static com.whiskerguard.ai.domain.AgentTaskTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AgentTaskMapperTest {

    private AgentTaskMapper agentTaskMapper;

    @BeforeEach
    void setUp() {
        agentTaskMapper = new AgentTaskMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAgentTaskSample1();
        var actual = agentTaskMapper.toEntity(agentTaskMapper.toDto(expected));
        assertAgentTaskAllPropertiesEquals(expected, actual);
    }
}
