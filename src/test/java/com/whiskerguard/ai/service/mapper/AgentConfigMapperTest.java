package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AgentConfigAsserts.*;
import static com.whiskerguard.ai.domain.AgentConfigTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AgentConfigMapperTest {

    private AgentConfigMapper agentConfigMapper;

    @BeforeEach
    void setUp() {
        agentConfigMapper = new AgentConfigMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAgentConfigSample1();
        var actual = agentConfigMapper.toEntity(agentConfigMapper.toDto(expected));
        assertAgentConfigAllPropertiesEquals(expected, actual);
    }
}
