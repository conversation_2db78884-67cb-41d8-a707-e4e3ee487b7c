package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.TenantPromptConfigAsserts.*;
import static com.whiskerguard.ai.domain.TenantPromptConfigTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TenantPromptConfigMapperTest {

    private TenantPromptConfigMapper tenantPromptConfigMapper;

    @BeforeEach
    void setUp() {
        tenantPromptConfigMapper = new TenantPromptConfigMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTenantPromptConfigSample1();
        var actual = tenantPromptConfigMapper.toEntity(tenantPromptConfigMapper.toDto(expected));
        assertTenantPromptConfigAllPropertiesEquals(expected, actual);
    }
}
