package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.ContractClauseIssueAsserts.*;
import static com.whiskerguard.ai.domain.ContractClauseIssueTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ContractClauseIssueMapperTest {

    private ContractClauseIssueMapper contractClauseIssueMapper;

    @BeforeEach
    void setUp() {
        contractClauseIssueMapper = new ContractClauseIssueMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getContractClauseIssueSample1();
        var actual = contractClauseIssueMapper.toEntity(contractClauseIssueMapper.toDto(expected));
        assertContractClauseIssueAllPropertiesEquals(expected, actual);
    }
}
