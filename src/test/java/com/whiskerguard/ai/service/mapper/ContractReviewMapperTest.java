package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.ContractReviewAsserts.*;
import static com.whiskerguard.ai.domain.ContractReviewTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ContractReviewMapperTest {

    private ContractReviewMapper contractReviewMapper;

    @BeforeEach
    void setUp() {
        contractReviewMapper = new ContractReviewMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getContractReviewSample1();
        var actual = contractReviewMapper.toEntity(contractReviewMapper.toDto(expected));
        assertContractReviewAllPropertiesEquals(expected, actual);
    }
}
