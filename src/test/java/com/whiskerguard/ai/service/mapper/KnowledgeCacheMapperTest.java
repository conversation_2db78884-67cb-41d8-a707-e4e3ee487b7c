package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.KnowledgeCacheAsserts.*;
import static com.whiskerguard.ai.domain.KnowledgeCacheTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class KnowledgeCacheMapperTest {

    private KnowledgeCacheMapper knowledgeCacheMapper;

    @BeforeEach
    void setUp() {
        knowledgeCacheMapper = new KnowledgeCacheMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getKnowledgeCacheSample1();
        var actual = knowledgeCacheMapper.toEntity(knowledgeCacheMapper.toDto(expected));
        assertKnowledgeCacheAllPropertiesEquals(expected, actual);
    }
}
