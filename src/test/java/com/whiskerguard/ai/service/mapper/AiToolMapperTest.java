package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.AiToolAsserts.*;
import static com.whiskerguard.ai.domain.AiToolTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AiToolMapperTest {

    private AiToolMapper aiToolMapper;

    @BeforeEach
    void setUp() {
        aiToolMapper = new AiToolMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAiToolSample1();
        var actual = aiToolMapper.toEntity(aiToolMapper.toDto(expected));
        assertAiToolAllPropertiesEquals(expected, actual);
    }
}
