package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.PromptTemplateAsserts.*;
import static com.whiskerguard.ai.domain.PromptTemplateTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PromptTemplateMapperTest {

    private PromptTemplateMapper promptTemplateMapper;

    @BeforeEach
    void setUp() {
        promptTemplateMapper = new PromptTemplateMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPromptTemplateSample1();
        var actual = promptTemplateMapper.toEntity(promptTemplateMapper.toDto(expected));
        assertPromptTemplateAllPropertiesEquals(expected, actual);
    }
}
