package com.whiskerguard.ai.service.contract;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.config.EmbeddedSQL;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同审查服务解析方法测试
 */
@SpringBootTest
@ActiveProfiles("test")
@EmbeddedSQL
@Transactional
class ContractReviewServiceParseTest {

    @Autowired
    private ContractReviewService contractReviewService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testJsonParsing() throws Exception {
        // 测试 JSON 解析功能
        String jsonResponse =
            """
            {
              "整体风险等级评估": "中等",
              "具体风险点列表": [
                "服务内容描述不够具体",
                "违约金比例可能过高"
              ]
            }
            """;

        // 直接测试 JSON 解析
        Map<String, Object> resultMap = objectMapper.readValue(jsonResponse, Map.class);

        assertThat(resultMap).isNotNull();
        assertThat(resultMap.get("整体风险等级评估")).isEqualTo("中等");
        assertThat(resultMap.get("具体风险点列表")).isInstanceOf(java.util.List.class);

        @SuppressWarnings("unchecked")
        java.util.List<String> riskPoints = (java.util.List<String>) resultMap.get("具体风险点列表");
        assertThat(riskPoints).hasSize(2);
        assertThat(riskPoints.get(0)).isEqualTo("服务内容描述不够具体");
    }
}
