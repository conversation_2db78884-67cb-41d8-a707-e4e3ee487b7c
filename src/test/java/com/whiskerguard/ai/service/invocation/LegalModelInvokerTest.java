package com.whiskerguard.ai.service.invocation;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.client.ChatLawApiClient;
import com.whiskerguard.ai.client.LaWGPTClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 法律模型 Invoker 集成测试
 */
@ExtendWith(MockitoExtension.class)
class LegalModelInvokerTest {

    @Mock
    private ChatLawApiClient chatLawApiClient;

    @Mock
    private LaWGPTClient laWGPTClient;

    @Mock
    private RagHelper ragHelper;

    @InjectMocks
    private ChatLawInvoker chatLawInvoker;

    @InjectMocks
    private LaWGPTInvoker laWGPTInvoker;

    @BeforeEach
    void setUp() {
        // 简单的初始化
    }

    @Test
    void testChatLawInvokerToolKey() {
        // 测试 ChatLaw Invoker 工具键
        assertThat(chatLawInvoker.getToolKey()).isEqualTo("chatlaw");
    }

    @Test
    void testLaWGPTInvokerToolKey() {
        // 测试 LaWGPT Invoker 工具键
        assertThat(laWGPTInvoker.getToolKey()).isEqualTo("lawgpt");
    }

    @Test
    void testChatLawInvokerStreamingSupport() {
        // 测试 ChatLaw 流式支持
        assertThat(chatLawInvoker.supportsStreaming()).isFalse();
    }

    @Test
    void testLaWGPTInvokerStreamingSupport() {
        // 测试 LaWGPT 流式支持
        assertThat(laWGPTInvoker.supportsStreaming()).isFalse();
    }

    @Test
    void testChatLawInvokerInitialization() {
        // 测试 ChatLaw Invoker 正确初始化
        assertThat(chatLawInvoker).isNotNull();
        assertThat(chatLawInvoker.getToolKey()).isEqualTo("chatlaw");
    }

    @Test
    void testLaWGPTInvokerInitialization() {
        // 测试 LaWGPT Invoker 正确初始化
        assertThat(laWGPTInvoker).isNotNull();
        assertThat(laWGPTInvoker.getToolKey()).isEqualTo("lawgpt");
    }

    @Test
    void testChatLawPromptEnhancement() {
        // 测试组件的初始化和配置
        assertThat(chatLawInvoker.getToolKey()).isEqualTo("chatlaw");
    }
}
