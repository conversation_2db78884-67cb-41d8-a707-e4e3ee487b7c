/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentServiceTest.java
 * 包    名：com.whiskerguard.ai.service.agent
 * 描    述：合规智能体服务测试类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.agent.business.ContractReviewAgentService;
import com.whiskerguard.ai.service.agent.business.PolicyReviewAgentService;
import com.whiskerguard.ai.service.agent.business.RegulationInternalizationAgentService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorService;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskResponseDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskStatusDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 合规智能体服务测试类
 * <p>
 * 测试合规智能体服务的核心功能。
 * 包括任务创建、状态查询、结果获取等功能的测试。
 * 
 * <AUTHOR>
 * @since 1.0
 */
@ExtendWith(MockitoExtension.class)
class ComplianceAgentServiceTest {

    @Mock
    private AgentTaskRepository agentTaskRepository;

    @Mock
    private AgentTaskMapper agentTaskMapper;

    @Mock
    private TaskOrchestratorService taskOrchestratorService;

    @Mock
    private RegulationInternalizationAgentService regulationInternalizationService;

    @Mock
    private PolicyReviewAgentService policyReviewService;

    @Mock
    private ContractReviewAgentService contractReviewService;

    @InjectMocks
    private ComplianceAgentService complianceAgentService;

    private AgentTaskRequestDTO testRequest;
    private AgentTask testTask;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRequest = AgentTaskRequestDTO.builder()
                .tenantId(1L)
                .taskType(AgentTaskType.REGULATION_INTERNALIZATION)
                .title("测试外规内化任务")
                .description("测试描述")
                .priority(TaskPriority.NORMAL)
                .requestData("{\"regulationId\":\"REG_001\"}")
                .build();

        testTask = new AgentTask();
        testTask.setId(1L);
        testTask.setTenantId(1L);
        testTask.setTaskType(AgentTaskType.REGULATION_INTERNALIZATION);
        testTask.setTitle("测试外规内化任务");
        testTask.setStatus(AgentTaskStatus.PENDING);
        testTask.setPriority(TaskPriority.NORMAL);
        testTask.setProgress(0);
        testTask.setCreatedAt(Instant.now());
    }

    @Test
    void createTask_ShouldReturnTaskResponse_WhenValidRequest() {
        // Given
        when(agentTaskRepository.save(any(AgentTask.class))).thenReturn(testTask);

        // When
        AgentTaskResponseDTO response = complianceAgentService.createTask(testRequest);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getTaskId()).isEqualTo(1L);
        assertThat(response.getTaskType()).isEqualTo(AgentTaskType.REGULATION_INTERNALIZATION);
        assertThat(response.getTitle()).isEqualTo("测试外规内化任务");
        assertThat(response.getStatus()).isEqualTo(AgentTaskStatus.PENDING);

        verify(agentTaskRepository).save(any(AgentTask.class));
    }

    @Test
    void createTask_ShouldThrowException_WhenRepositoryFails() {
        // Given
        when(agentTaskRepository.save(any(AgentTask.class))).thenThrow(new RuntimeException("数据库错误"));

        // When & Then
        assertThatThrownBy(() -> complianceAgentService.createTask(testRequest))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("创建Agent任务失败");

        verify(agentTaskRepository).save(any(AgentTask.class));
    }

    @Test
    void getTaskStatus_ShouldReturnStatus_WhenTaskExists() {
        // Given
        testTask.setStatus(AgentTaskStatus.RUNNING);
        testTask.setProgress(50);
        testTask.setStartTime(Instant.now());
        
        when(agentTaskRepository.findById(1L)).thenReturn(Optional.of(testTask));

        // When
        AgentTaskStatusDTO status = complianceAgentService.getTaskStatus(1L);

        // Then
        assertThat(status).isNotNull();
        assertThat(status.getTaskId()).isEqualTo(1L);
        assertThat(status.getStatus()).isEqualTo(AgentTaskStatus.RUNNING);
        assertThat(status.getProgress()).isEqualTo(50);
        assertThat(status.getStartTime()).isNotNull();

        verify(agentTaskRepository).findById(1L);
    }

    @Test
    void getTaskStatus_ShouldThrowException_WhenTaskNotExists() {
        // Given
        when(agentTaskRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> complianceAgentService.getTaskStatus(999L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("任务不存在");

        verify(agentTaskRepository).findById(999L);
    }

    @Test
    void getTaskResult_ShouldReturnResult_WhenTaskCompleted() {
        // Given
        testTask.setStatus(AgentTaskStatus.COMPLETED);
        testTask.setProgress(100);
        testTask.setResponseData("{\"result\":\"success\"}");
        testTask.setEndTime(Instant.now());
        
        when(agentTaskRepository.findById(1L)).thenReturn(Optional.of(testTask));

        // When
        AgentTaskResponseDTO result = complianceAgentService.getTaskResult(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTaskId()).isEqualTo(1L);
        assertThat(result.getStatus()).isEqualTo(AgentTaskStatus.COMPLETED);
        assertThat(result.getProgress()).isEqualTo(100);
        assertThat(result.getResult()).isEqualTo("{\"result\":\"success\"}");

        verify(agentTaskRepository).findById(1L);
    }

    @Test
    void getTaskResult_ShouldThrowException_WhenTaskNotExists() {
        // Given
        when(agentTaskRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> complianceAgentService.getTaskResult(999L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("任务不存在");

        verify(agentTaskRepository).findById(999L);
    }

    @Test
    void cancelTask_ShouldUpdateStatus_WhenTaskIsRunning() {
        // Given
        testTask.setStatus(AgentTaskStatus.RUNNING);
        when(agentTaskRepository.findById(1L)).thenReturn(Optional.of(testTask));
        when(agentTaskRepository.save(any(AgentTask.class))).thenReturn(testTask);

        // When
        complianceAgentService.cancelTask(1L);

        // Then
        verify(agentTaskRepository).findById(1L);
        verify(agentTaskRepository).save(argThat(task -> 
            task.getStatus() == AgentTaskStatus.CANCELLED && 
            task.getEndTime() != null
        ));
    }

    @Test
    void cancelTask_ShouldNotUpdateStatus_WhenTaskNotRunning() {
        // Given
        testTask.setStatus(AgentTaskStatus.COMPLETED);
        when(agentTaskRepository.findById(1L)).thenReturn(Optional.of(testTask));

        // When
        complianceAgentService.cancelTask(1L);

        // Then
        verify(agentTaskRepository).findById(1L);
        verify(agentTaskRepository, never()).save(any(AgentTask.class));
    }

    @Test
    void cancelTask_ShouldThrowException_WhenTaskNotExists() {
        // Given
        when(agentTaskRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> complianceAgentService.cancelTask(999L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("任务不存在");

        verify(agentTaskRepository).findById(999L);
        verify(agentTaskRepository, never()).save(any(AgentTask.class));
    }
}

