# 使用微软官方镜像
FROM mcr.microsoft.com/openjdk/jdk:17-ubuntu

# 设置工作目录
WORKDIR /app

# 添加维护者信息
LABEL maintainer="WhiskerGuard Team"

# 安装必要的工具并设置时区
RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制已构建好的jar文件
COPY target/*.jar app.jar

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=prod \
    JAVA_OPTS="-Xmx512m -Xms256m"

# 配置SkyWalking监控代理
COPY skywalking/skywalking-agent.tgz /tmp/
RUN mkdir -p /skywalking/agent && \
    tar -zxvf /tmp/skywalking-agent.tgz -C /skywalking/agent --strip-components=1 && \
    rm -f /tmp/skywalking-agent.tgz

# 暴露端口
EXPOSE 8086

# 健康检查（注意：端口应该是8087，不是8181）
HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost:8086/management/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", \
    "java ${JAVA_OPTS} \
    -javaagent:/skywalking/agent/skywalking-agent.jar \
    -Dskywalking.agent.service_name=${SPRING_PROFILES_ACTIVE}_retrievalservice \
    -Dskywalking.collector.backend_service=************:11800 \
    -jar app.jar"]
