{"annotations": {"changelogDate": "20250619033814"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "Agent上下文实体\\n存储Agent执行过程中的上下文信息", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "上下文键", "fieldName": "<PERSON><PERSON>ey", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "上下文值", "fieldName": "contextValue", "fieldType": "TextBlob"}, {"documentation": "上下文类型", "fieldName": "contextType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "过期时间", "fieldName": "expireTime", "fieldType": "Instant"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AgentContext", "pagination": "pagination", "relationships": [{"otherEntityField": "title", "otherEntityName": "agentTask", "otherEntityRelationshipName": "contexts", "relationshipName": "agentTask", "relationshipSide": "right", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "serviceImpl"}