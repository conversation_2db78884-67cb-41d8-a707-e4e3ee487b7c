{"annotations": {"changelogDate": "20250506115800"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "AI工具指标（AiToolMetrics）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "统计周期", "fieldName": "period", "fieldType": "MetricsPeriod", "fieldTypeDocumentation": "统计周期枚举", "fieldValidateRules": ["required"], "fieldValues": "HOURLY,DAILY,WEEKLY"}, {"documentation": "响应时间", "fieldName": "responseTime", "fieldType": "Integer"}, {"documentation": "成功次数", "fieldName": "successCount", "fieldType": "<PERSON>"}, {"documentation": "失败次数", "fieldName": "failureCount", "fieldType": "<PERSON>"}, {"documentation": "总请求数", "fieldName": "totalRequests", "fieldType": "<PERSON>"}, {"documentation": "错误率", "fieldName": "errorRate", "fieldType": "Float"}, {"documentation": "采集日期", "fieldName": "collectDate", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AiToolMetrics", "pagination": "pagination", "relationships": [{"otherEntityName": "aiTool", "relationshipName": "tool", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}