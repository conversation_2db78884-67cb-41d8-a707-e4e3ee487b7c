{"annotations": {"changelogDate": "20250616094110"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "提示词模板（PromptTemplate）实体\\n存储提示词模板的基本信息和内容", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>"}, {"documentation": "模板唯一标识键", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "模板名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "模板描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "模板类型", "fieldName": "templateType", "fieldType": "PromptTemplateType", "fieldTypeDocumentation": "提示词模板类型枚举", "fieldValidateRules": ["required"], "fieldValues": "CONTRACT_REVIE<PERSON>,CONTRACT_COMPREHENSIVE_REVI<PERSON><PERSON>,CONTRACT_LEGAL_COMPLIANCE,CONTRACT_RISK_ASSESSMENT,CONTRACT_FINANCIAL_TERMS,POLICY_REVIEW,<PERSON><PERSON><PERSON><PERSON>_REGULATORY_CONVERSION,P<PERSON><PERSON><PERSON>_INTERNAL_REVIEW,POLICY_COMPLIANCE_CHECK,GENERAL_ANALYSIS,GENERAL_GENERATION,GENERAL_EXTRACTION"}, {"documentation": "模板内容", "fieldName": "content", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "模板状态", "fieldName": "status", "fieldType": "PromptTemplateStatus", "fieldTypeDocumentation": "提示词模板状态枚举", "fieldValidateRules": ["required"], "fieldValues": "DRAFT,TESTING,PUBL<PERSON>HED,DISABLED,ARCHIVED"}, {"documentation": "版本号", "fieldName": "templateVersion", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "是否为系统默认模板", "fieldName": "isSystemDefault", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "创建者ID", "fieldName": "createdById", "fieldType": "<PERSON>"}, {"documentation": "最后修改者ID", "fieldName": "lastModifiedById", "fieldType": "<PERSON>"}, {"documentation": "使用次数统计", "fieldName": "usageCount", "fieldType": "<PERSON>"}, {"documentation": "最后使用时间", "fieldName": "lastUsedAt", "fieldType": "Instant"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "PromptTemplate", "pagination": "pagination", "relationships": [{"otherEntityName": "promptTemplateVariable", "otherEntityRelationshipName": "promptTemplate", "relationshipName": "promptTemplateVariable", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "promptTemplateVersion", "otherEntityRelationshipName": "promptTemplate", "relationshipName": "promptTemplateVersion", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}