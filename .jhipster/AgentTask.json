{"annotations": {"changelogDate": "20250619033812"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "Agent任务实体\\n记录Agent执行的任务信息", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "任务类型", "fieldName": "taskType", "fieldType": "AgentTaskType", "fieldTypeDocumentation": "Agent任务类型枚举", "fieldValidateRules": ["required"], "fieldValues": "REGULATION_INTERNALIZATION,POLICY_REVIEW,CONTRACT_REVIEW"}, {"documentation": "任务标题", "fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "任务描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "任务状态", "fieldName": "status", "fieldType": "AgentTaskStatus", "fieldTypeDocumentation": "Agent任务状态枚举", "fieldValidateRules": ["required"], "fieldValues": "PENDING,RUNNING,COMPLETED,FAILED,CANCELLED"}, {"documentation": "任务优先级", "fieldName": "priority", "fieldType": "TaskPriority", "fieldTypeDocumentation": "任务优先级枚举", "fieldValidateRules": ["required"], "fieldValues": "LOW,NORMAL,HIGH,URGENT"}, {"documentation": "请求数据", "fieldName": "requestData", "fieldType": "TextBlob"}, {"documentation": "响应数据", "fieldName": "responseData", "fieldType": "TextBlob"}, {"documentation": "错误信息", "fieldName": "errorMessage", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "2000"}, {"documentation": "开始时间", "fieldName": "startTime", "fieldType": "Instant"}, {"documentation": "结束时间", "fieldName": "endTime", "fieldType": "Instant"}, {"documentation": "执行时长(毫秒)", "fieldName": "executionTime", "fieldType": "<PERSON>"}, {"documentation": "进度百分比", "fieldName": "progress", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "100", "fieldValidateRulesMin": "0"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AgentTask", "pagination": "pagination", "relationships": [{"documentation": "一个Agent任务可以有多个执行步骤", "otherEntityName": "taskStep", "otherEntityRelationshipName": "agentTask", "relationshipName": "taskSteps", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"documentation": "一个Agent任务可以有多个上下文", "otherEntityName": "agentContext", "otherEntityRelationshipName": "agentTask", "relationshipName": "contexts", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}