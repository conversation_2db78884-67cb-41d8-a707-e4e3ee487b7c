{"annotations": {"changelogDate": "20250616094113"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "租户提示词配置（TenantPromptConfig）实体\\n存储租户级别的个性化配置", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "配置键", "fieldName": "config<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "配置值", "fieldName": "config<PERSON><PERSON><PERSON>", "fieldType": "TextBlob"}, {"documentation": "配置类型", "fieldName": "configType", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "配置描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "是否启用", "fieldName": "isEnabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "优先级", "fieldName": "priority", "fieldType": "Integer"}, {"documentation": "生效时间", "fieldName": "effectiveFrom", "fieldType": "Instant"}, {"documentation": "失效时间", "fieldName": "effectiveTo", "fieldType": "Instant"}, {"documentation": "创建者ID", "fieldName": "createdById", "fieldType": "<PERSON>"}, {"documentation": "最后修改者ID", "fieldName": "lastModifiedById", "fieldType": "<PERSON>"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "TenantPromptConfig", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}