{"annotations": {"changelogDate": "20250616094111"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "提示词模板变量（PromptTemplateVariable）实体\\n定义模板中使用的变量信息", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "变量名称", "fieldName": "variableName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "变量显示名称", "fieldName": "displayName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "变量描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "变量类型", "fieldName": "variableType", "fieldType": "VariableType", "fieldTypeDocumentation": "变量类型枚举", "fieldValidateRules": ["required"], "fieldValues": "SYSTEM,BUSINESS,RAG,CUSTOM,COMPUTED,EXTERNAL"}, {"documentation": "默认值", "fieldName": "defaultValue", "fieldType": "TextBlob"}, {"documentation": "是否必填", "fieldName": "isRequired", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "变量验证规则", "fieldName": "validationRule", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "变量示例值", "fieldName": "exampleValue", "fieldType": "TextBlob"}, {"documentation": "排序顺序", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "是否启用", "fieldName": "isEnabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "PromptTemplateVariable", "pagination": "pagination", "relationships": [{"otherEntityName": "promptTemplate", "relationshipName": "promptTemplate", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}