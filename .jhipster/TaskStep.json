{"annotations": {"changelogDate": "20250619033813"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "任务步骤实体\\n记录Agent任务的执行步骤", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "步骤名称", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "步骤描述", "fieldName": "stepDescription", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "步骤状态", "fieldName": "status", "fieldType": "TaskStepStatus", "fieldTypeDocumentation": "任务步骤状态枚举", "fieldValidateRules": ["required"], "fieldValues": "PENDING,RUNNING,COMPLETED,FAILED,SKIPPED"}, {"documentation": "步骤顺序", "fieldName": "step<PERSON>rder", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "输入数据", "fieldName": "inputData", "fieldType": "TextBlob"}, {"documentation": "输出数据", "fieldName": "outputData", "fieldType": "TextBlob"}, {"documentation": "错误信息", "fieldName": "errorMessage", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "开始时间", "fieldName": "startTime", "fieldType": "Instant"}, {"documentation": "结束时间", "fieldName": "endTime", "fieldType": "Instant"}, {"documentation": "执行时长(毫秒)", "fieldName": "executionTime", "fieldType": "<PERSON>"}, {"documentation": "重试次数", "fieldName": "retryCount", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "TaskStep", "pagination": "pagination", "relationships": [{"otherEntityField": "title", "otherEntityName": "agentTask", "otherEntityRelationshipName": "taskSteps", "relationshipName": "agentTask", "relationshipSide": "right", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "serviceImpl"}