{"annotations": {"changelogDate": "20250506115759"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "AI审核（AiReview）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "职工（用户）ID", "fieldName": "employeeId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "审核内容", "fieldName": "reviewContent", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "审核结果", "fieldName": "reviewResult", "fieldType": "ReviewResult", "fieldTypeDocumentation": "审核结果枚举", "fieldValidateRules": ["required"], "fieldValues": "APPROVED,REJECTED,MODIFIED"}, {"documentation": "审核日期", "fieldName": "reviewDate", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "审核人", "fieldName": "reviewer", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "审核意见", "fieldName": "comments", "fieldType": "String"}, {"documentation": "反馈数据", "fieldName": "feedback", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AiReview", "pagination": "pagination", "relationships": [{"otherEntityName": "aiRequest", "relationshipName": "request", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}