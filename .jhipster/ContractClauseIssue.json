{"annotations": {"changelogDate": "20250613081138"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "条款问题实体\\n存储合同条款中发现的具体问题", "dto": "mapstruct", "fields": [{"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "关联的审查记录ID", "fieldName": "reviewId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "条款内容", "fieldName": "clauseText", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "条款编号/位置", "fieldName": "clauseNumber", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "问题类型", "fieldName": "issueType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "问题描述", "fieldName": "issueDescription", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "严重程度", "fieldName": "severity", "fieldType": "RiskLevel", "fieldValidateRules": ["required"], "fieldValues": "HIGH,MEDIUM,LOW"}, {"documentation": "法律风险说明", "fieldName": "legalRisk", "fieldType": "TextBlob"}, {"documentation": "修改建议", "fieldName": "suggestions", "fieldType": "TextBlob"}, {"documentation": "参考法规", "fieldName": "referenceLaws", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "ContractClauseIssue", "pagination": "pagination", "relationships": [{"otherEntityName": "contractReview", "relationshipName": "review", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}