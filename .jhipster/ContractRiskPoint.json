{"annotations": {"changelogDate": "20250613081137"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "风险点实体\\n存储合同审查中识别的具体风险点", "dto": "mapstruct", "fields": [{"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "关联的审查记录ID", "fieldName": "reviewId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "风险类别", "fieldName": "riskCategory", "fieldType": "RiskCategory", "fieldValidateRules": ["required"], "fieldValues": "LEGAL_COMPLIANCE,BUSINESS_RISK,FINANCIAL_RISK,OPERATIONAL_RISK,REPUTATION_RISK"}, {"documentation": "风险描述", "fieldName": "riskDescription", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "风险等级", "fieldName": "severity", "fieldType": "RiskLevel", "fieldValidateRules": ["required"], "fieldValues": "HIGH,MEDIUM,LOW"}, {"documentation": "涉及的条款", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "TextBlob"}, {"documentation": "法律依据", "fieldName": "legalBasis", "fieldType": "TextBlob"}, {"documentation": "修改建议", "fieldName": "suggestions", "fieldType": "TextBlob"}, {"documentation": "风险分数 (0-100)", "fieldName": "riskScore", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "100", "fieldValidateRulesMin": "0"}, {"documentation": "是否为关键风险", "fieldName": "isCritical", "fieldType": "Boolean"}, {"documentation": "风险来源", "fieldName": "riskSource", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "ContractRiskPoint", "pagination": "pagination", "relationships": [{"otherEntityName": "contractReview", "relationshipName": "review", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}