{"annotations": {"changelogDate": "20250506115758"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "AI请求（AiRequest）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "职工（用户）ID", "fieldName": "employeeId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "工具类型", "fieldName": "toolType", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "提示词", "fieldName": "prompt", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "响应内容", "fieldName": "response", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "请求时间", "fieldName": "requestTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "响应时间", "fieldName": "responseTime", "fieldType": "Instant"}, {"documentation": "请求状态", "fieldName": "status", "fieldType": "RequestStatus", "fieldTypeDocumentation": "请求状态枚举", "fieldValidateRules": ["required"], "fieldValues": "SUCCESS,FAILED,PROCESSING,TIMEOUT"}, {"documentation": "错误信息", "fieldName": "errorMessage", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AiRequest", "pagination": "pagination", "relationships": [{"otherEntityName": "aiTool", "relationshipName": "tool", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}