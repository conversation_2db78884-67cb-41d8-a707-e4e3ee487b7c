{"annotations": {"changelogDate": "20250613081135"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "合同审查记录实体\\n存储合同审查的基本信息和结果", "dto": "mapstruct", "fields": [{"documentation": "租户ID - 多租户数据隔离", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "员工ID", "fieldName": "employeeId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "合同类型", "fieldName": "contractType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "合同标题", "fieldName": "contractTitle", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "合同内容", "fieldName": "contractContent", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "审查结果（JSON格式）", "fieldName": "reviewResult", "fieldType": "TextBlob"}, {"documentation": "审查状态", "fieldName": "status", "fieldType": "ReviewStatus", "fieldTypeDocumentation": "合同智能审查相关实体定义\\n用于支持合同智能审查功能的数据模型", "fieldValidateRules": ["required"], "fieldValues": "PENDING,PROCESSING,COMPLETED,FAILED,CANCELLED"}, {"documentation": "整体风险等级", "fieldName": "overallRiskLevel", "fieldType": "RiskLevel", "fieldValues": "HIGH,MEDIUM,LOW"}, {"documentation": "风险分数 (0-100)", "fieldName": "riskScore", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "100", "fieldValidateRulesMin": "0"}, {"documentation": "风险总结", "fieldName": "riskSummary", "fieldType": "TextBlob"}, {"documentation": "AI调用ID - 关联到ai_request表", "fieldName": "aiRequestId", "fieldType": "<PERSON>"}, {"documentation": "审查开始时间", "fieldName": "reviewStartTime", "fieldType": "Instant"}, {"documentation": "审查完成时间", "fieldName": "reviewEndTime", "fieldType": "Instant"}, {"documentation": "审查耗时（毫秒）", "fieldName": "reviewDuration", "fieldType": "<PERSON>"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "ContractReview", "pagination": "pagination", "relationships": [{"otherEntityName": "contractParty", "otherEntityRelationshipName": "review", "relationshipName": "contractParty", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "contractRiskPoint", "otherEntityRelationshipName": "review", "relationshipName": "contractRiskPoint", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "contractClauseIssue", "otherEntityRelationshipName": "review", "relationshipName": "contractClauseIssue", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}