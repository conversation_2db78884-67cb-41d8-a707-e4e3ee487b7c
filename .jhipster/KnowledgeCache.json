{"annotations": {"changelogDate": "20250619033815"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "知识项缓存实体\\n缓存从RAG服务检索到的知识项", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "知识类型", "fieldName": "knowledgeType", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "查询关键词", "fieldName": "query<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "知识内容", "fieldName": "content", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "相似度分数", "fieldName": "similarityScore", "fieldType": "Double"}, {"documentation": "来源服务", "fieldName": "sourceService", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "缓存过期时间", "fieldName": "expireTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "访问次数", "fieldName": "accessCount", "fieldType": "<PERSON>", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "最后访问时间", "fieldName": "lastAccessTime", "fieldType": "Instant"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "KnowledgeCache", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}