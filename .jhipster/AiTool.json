{"annotations": {"changelogDate": "20250506115757"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "AI工具（AiTool）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "工具名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "工具关键词", "fieldName": "tool<PERSON>ey", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "API 地址", "fieldName": "apiUrl", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "API 密钥", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "鉴权类型", "fieldName": "authType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "接口路径", "fieldName": "path", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "工具状态", "fieldName": "status", "fieldType": "ToolStatus", "fieldTypeDocumentation": "AI工具状态枚举", "fieldValidateRules": ["required"], "fieldValues": "AVAILABLE,UNAVAILABLE,MAINTENANCE"}, {"documentation": "路由权重", "fieldName": "weight", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "并发许可数", "fieldName": "maxConcurrentCalls", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "是否为模型类型", "fieldName": "isModel", "fieldType": "Boolean"}, {"documentation": "模型分类", "fieldName": "modelCategory", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "模型提供商", "fieldName": "modelProvider", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "备注信息", "fieldName": "remark", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "AiTool", "relationships": [], "searchEngine": "no", "service": "serviceClass"}