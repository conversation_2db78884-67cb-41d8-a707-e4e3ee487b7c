{"annotations": {"changelogDate": "20250613081136"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "合同关联方实体\\n存储从合同中提取的关联方信息", "dto": "mapstruct", "fields": [{"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "关联的审查记录ID", "fieldName": "reviewId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "关联方名称", "fieldName": "partyName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "关联方类型", "fieldName": "partyType", "fieldType": "PartyType", "fieldValidateRules": ["required"], "fieldValues": "COMPANY,INDIVIDUAL,GOVERNMENT,OTHER"}, {"documentation": "在合同中的角色", "fieldName": "partyRole", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "统一社会信用代码（企业）", "fieldName": "creditCode", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "注册地址", "fieldName": "registeredAddress", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "512"}, {"documentation": "法定代表人（企业）", "fieldName": "legalRepresentative", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "联系方式", "fieldName": "contactInfo", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "风险等级", "fieldName": "riskLevel", "fieldType": "RiskLevel", "fieldValues": "HIGH,MEDIUM,LOW"}, {"documentation": "风险因素（JSON数组）", "fieldName": "riskFactors", "fieldType": "TextBlob"}, {"documentation": "合规问题（JSON数组）", "fieldName": "complianceIssues", "fieldType": "TextBlob"}, {"documentation": "天眼查信息（JSON格式）", "fieldName": "tianyanchaInfo", "fieldType": "TextBlob"}, {"documentation": "扩展信息", "fieldName": "additionalInfo", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "ContractParty", "pagination": "pagination", "relationships": [{"otherEntityName": "contractReview", "relationshipName": "review", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}