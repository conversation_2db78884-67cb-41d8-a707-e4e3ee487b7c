{"annotations": {"changelogDate": "20250616094112"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "提示词模板版本（PromptTemplateVersion）实体\\n管理模板的版本历史", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "版本号", "fieldName": "versionNumber", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "版本名称", "fieldName": "versionName", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "版本描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "模板内容", "fieldName": "content", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"documentation": "变量定义JSON", "fieldName": "variablesDefinition", "fieldType": "TextBlob"}, {"documentation": "版本状态", "fieldName": "status", "fieldType": "PromptTemplateStatus", "fieldTypeDocumentation": "提示词模板状态枚举", "fieldValidateRules": ["required"], "fieldValues": "DRAFT,TESTING,PUBL<PERSON>HED,DISABLED,ARCHIVED"}, {"documentation": "是否为当前活跃版本", "fieldName": "isActive", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "创建者ID", "fieldName": "createdById", "fieldType": "<PERSON>"}, {"documentation": "版本变更说明", "fieldName": "changeLog", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "使用次数统计", "fieldName": "usageCount", "fieldType": "<PERSON>"}, {"documentation": "最后使用时间", "fieldName": "lastUsedAt", "fieldType": "Instant"}, {"documentation": "性能评分", "fieldName": "performanceScore", "fieldType": "Double"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant"}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "PromptTemplateVersion", "pagination": "pagination", "relationships": [{"otherEntityName": "promptTemplate", "relationshipName": "promptTemplate", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}