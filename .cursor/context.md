# WhiskerGuard AI Service 项目上下文

## 项目架构

- 技术栈：Spring Cloud + JHipster + Docker
- 架构模式：分布式微服务，支持多租户
- 主要模块：
  - AI 服务调用与编排（统一API、负载均衡、结果聚合）
  - 工作流编排与调度（支持异步、批量、错误重试）
  - 合同/合规业务场景定制
  - 监控与指标采集（XXL-JOB、Prometheus）

## 主要设计原则

- 统一接口、解耦业务与AI工具
- 支持多AI工具动态扩展
- 任务异步、批量、可追踪
- 企业级安全、审计、监控

## 典型调用流程

1. 业务系统通过统一API发起AI请求
2. 服务编排层根据类型路由到合适AI工具
3. 支持多工具并发调用与结果聚合
4. 工作流引擎支持自定义业务流程
5. 任务调度与监控由XXL-JOB等组件保障

---

如需补充具体模块、接口或业务场景说明，请在此文件继续追加。
