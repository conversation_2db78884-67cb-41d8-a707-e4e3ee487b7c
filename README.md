# WhiskerGuard AI Service

## 项目简介

WhiskerGuard AI Service 是一个基于 Spring Cloud + JHipster 构建的分布式合规管理 SaaS 系统的核心 AI 服务模块。该服务提供智能合规分析、风险评估和决策支持功能，采用微服务架构，支持高可用性和可扩展性。

## 技术架构

- **基础框架**: Spring Boot 3.4.4 + JHipster 8.10.0
- **微服务架构**: Spring Cloud 2024.0.1
- **数据存储**:
  - MySQL (主数据库)
  - Redis (缓存)
- **消息队列**: Kafka
- **服务注册与发现**: Eureka/Consul
- **身份验证**: OAuth2 Resource Server
- **API 文档**: SpringDoc OpenAPI 2.8.6
- **容器化**: Docker + Kubernetes
- **监控**: Micrometer + Prometheus
- **分布式任务编排**: Temporal
- **RAG 技术**: 检索增强生成系统

## 核心功能模块

1. **智能合规分析引擎**

   - 自动文本分析与合规检测
   - 多语言文档支持
   - 规则引擎与策略管理
   - 实时合规评分

2. **AI 工具管理**

   - AI 工具注册与配置
   - 工具使用权限控制
   - 工具性能监控
   - 版本管理与回滚机制

3. **AI 请求处理**

   - 智能合规分析
   - 风险评估算法
   - 决策建议生成
   - 批量处理能力

4. **检索增强生成系统 (RAG)**

   - 知识库管理与更新
   - 智能文档检索
   - 上下文感知回答生成
   - 多租户隔离

5. **AI 审核系统**

   - 多级审核流程
   - 审核历史追踪
   - 审核结果反馈
   - 异常案例标记与学习

6. **指标监控与分析**
   - 性能指标收集
   - 使用情况统计
   - 异常监控告警
   - 趋势分析报告

## 项目结构

```
src/main/java/com/whiskerguard/ai/
├── config/          # 配置类
├── domain/          # 领域模型
├── repository/      # 数据访问层
├── service/         # 业务服务
│   ├── dto/         # 数据传输对象
│   ├── mapper/      # 对象映射
│   └── impl/        # 服务实现
├── temporal/        # Temporal工作流
│   ├── workflow/    # 工作流定义
│   └── activity/    # 活动实现
├── rag/             # 检索增强生成系统
│   ├── embeddings/  # 向量嵌入服务
│   ├── retriever/   # 文档检索器
│   └── generator/   # 回答生成器
└── web/             # Web 接口
    ├── rest/        # REST API
    └── errors/      # 错误处理
```

## 技术特性

- **高性能**: 使用Redis缓存和Undertow服务器提升请求处理速度
- **高可用性**: 支持服务集群和负载均衡
- **可扩展性**: 微服务架构便于水平扩展
- **安全性**: OAuth2身份验证与细粒度权限控制
- **可观测性**: 完整的日志、指标和健康监控
- **多租户**: 支持数据隔离的SaaS模式
- **国际化**: 支持多语言界面和处理能力

## 快速开始

### 环境要求

- JDK 17+
- Docker & Docker Compose
- Maven 3.8+
- MySQL 8+
- Redis 6+

### 本地开发

```bash
# 克隆项目
git clone [项目地址]

# 启动依赖服务
docker-compose -f src/main/docker/mysql.yml up -d
docker-compose -f src/main/docker/redis.yml up -d
docker-compose -f src/main/docker/consul.yml up -d  # 服务发现

# 构建项目
./mvnw clean install

# 运行应用
./mvnw spring-boot:run
```

### Docker 部署

```bash
# 构建镜像
./mvnw package -Pprod jib:dockerBuild

# 运行容器
docker run -d \
  --name whiskerguard-ai-service \
  --network whiskerguard_whiskerguard \
  -p 8085:8085 \
  -e SPRING_PROFILES_ACTIVE=prod \
  harbor.mbbhg.com/whiskerguard/whiskerguard-ai-service:latest
```

### Kubernetes 部署

项目支持使用Kubernetes进行容器编排管理，相关配置位于 `kubernetes/` 目录。

## 配置说明

- **开发环境**: `application-dev.yml`
- **生产环境**: `application-prod.yml`
- **测试环境**: `application-test.yml`
- **TLS配置**: `application-tls.yml`

### 关键配置参数

```yaml
# RAG Configuration
whiskerguard:
  ai:
    rag:
      enabled: true
      default-tenant-id: 1
      top-k: 3
      distance-metric: cosine
      min-score: 0.6

# Temporal Configuration
temporal:
  server:
    host: 127.0.0.1
    port: 7233
  task-queue: AiTaskQueue
```

## 接口文档

- **Swagger UI**: `http://localhost:8085/swagger-ui.html`
- **OpenAPI 规范**: `http://localhost:8085/v3/api-docs`

## 监控与管理

- **应用健康检查**: `http://localhost:8085/management/health`
- **性能指标**: `http://localhost:8085/management/metrics`
- **线程转储**: `http://localhost:8085/management/threaddump`
- **环境信息**: `http://localhost:8085/management/env`
- **日志级别**: `http://localhost:8085/management/loggers`

## 开发规范

1. 遵循 JHipster 开发规范
2. 使用统一的代码格式化工具和静态检查
3. 提交前进行代码审查及单元测试
4. 保持测试覆盖率在 80% 以上
5. 遵守API设计最佳实践

## 部署架构

- **容器化**: 采用 Docker 容器化部署
- **编排管理**: 使用 Kubernetes 进行容器编排
- **负载均衡**: 配置 Nginx 作为反向代理
- **镜像仓库**: 使用 Harbor 作为私有镜像仓库
- **CI/CD**: 支持自动化构建与部署管道
- **灾备策略**: 支持多区域数据备份与恢复

## 安全说明

- **身份认证**: 使用 OAuth2/JWT 进行身份认证
- **权限控制**: 实现基于RBAC的细粒度权限控制
- **数据加密**: 敏感数据加密存储
- **安全审计**: 定期安全审计和漏洞扫描
- **API安全**: 限流、参数验证、HTTPS加密传输
- **数据隔离**: 多租户架构下的数据安全隔离

## 维护团队

- **系统架构师**: [Haishui Yan]
- **开发团队**: [WhiskerGuard Technical Team]
- **运维团队**: [WhiskerGuard Technical Team]
- **技术支持**: <EMAIL>

## 版本历史

- **v1.2.0** (计划中):
  - RAG系统进阶功能
  - 多语言支持扩展
  - 高级报告生成
- **v1.1.0** (2025-04-15):

  - 添加Temporal工作流支持
  - 优化RAG检索精度
  - 增强API安全性
  - 性能优化

- **v1.0.0** (2025-02-01):
  - 基础 AI 服务功能
  - 核心合规分析能力
  - 基础监控指标
  - 多租户支持

## 许可证

[WhiskerGuard Commercial LicenseV1.0]

## 贡献指南

如果您想为项目做出贡献，请参阅[贡献指南](CONTRIBUTING.md)文档。

## 联系方式

- **技术支持**: <EMAIL>
- **项目负责人**: <EMAIL>
