
/**
 * WhiskerGuard AI Service JDL 定义
 */

/**
 * AI工具状态枚举
 */
enum ToolStatus {
    AVAILABLE,    // 可用
    UNAVAILABLE,  // 不可用
    MAINTENANCE   // 维护中
}

/**
 * 请求状态枚举
 */
enum RequestStatus {
    SUCCESS,     // 成功
    FAILED,      // 失败
    PROCESSING,  // 处理中
    TIMEOUT      // 超时
}

/**
 * 审核结果枚举
 */
enum ReviewResult {
    APPROVED,    // 通过
    REJECTED,    // 拒绝
    MODIFIED     // 需要修改
}

/**
 * 统计周期枚举
 */
enum MetricsPeriod {
    HOURLY,    // 小时统计
    DAILY,     // 天统计
    WEEKLY     // 周统计
}

/**
 * AI工具（AiTool）实体
 */
entity AiTool {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 工具名称 */
    name String required maxlength(32),
    /** 工具关键词 */
    toolKey String required maxlength(32),
    /** 工具版本 */
    version String maxlength(32),
    /** API 地址 */
    apiUrl String required maxlength(256),
    /** API 密钥 */
    apiKey String required maxlength(256),
    /** 鉴权类型 */
    authType String maxlength(32),
    /** 接口路径 */
    path String maxlength(128),
    /** 工具状态 */
    status ToolStatus required,
    /** 路由权重 */
    weight Integer required,
    /** 并发许可数 */
    maxConcurrentCalls Integer required,
    /** 备注信息 */
    remark String,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * AI请求（AiRequest）实体
 */
entity AiRequest {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 工具类型 */
    toolType String required maxlength(64),
    /** 提示词 */
    prompt String required,
    /** 响应内容 */
    response String required,
    /** 请求时间 */
    requestTime Instant required,
    /** 响应时间 */
    responseTime Instant,
    /** 请求状态 */
    status RequestStatus required,
    /** 错误信息 */
    errorMessage String,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * AI审核（AiReview）实体
 */
entity AiReview {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 审核内容 */
    reviewContent String required,
    /** 审核结果 */
    reviewResult ReviewResult required,
    /** 审核日期 */
    reviewDate Instant required,
    /** 审核人 */
    reviewer String required maxlength(64),
    /** 审核意见 */
    comments String,
    /** 反馈数据 */
    feedback String,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * AI工具指标（AiToolMetrics）实体
 */
entity AiToolMetrics {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 统计周期 */
    period MetricsPeriod required,
    /** 响应时间 */
    responseTime Integer,
    /** 成功次数 */
    successCount Long,
    /** 失败次数 */
    failureCount Long,
    /** 总请求数 */
    totalRequests Long,
    /** 错误率 */
    errorRate Float,
    /** 采集日期 */
    collectDate Instant required,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 实体关系定义
 */
relationship ManyToOne {
    AiRequest{tool} to AiTool,
    AiReview{request} to AiRequest,
    AiToolMetrics{tool} to AiTool
}

/**
 * DTO 和服务定义
 */
dto * with mapstruct
service all with serviceImpl
paginate * with pagination