/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：agent_entities.jdl
 * 包    名：jdl
 * 描    述：Agent智能体相关实体JDL定义
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

/**
 * Agent任务状态枚举
 */
enum AgentTaskStatus {
    PENDING,      // 待处理
    RUNNING,      // 运行中
    COMPLETED,    // 已完成
    FAILED,       // 失败
    CANCELLED     // 已取消
}

/**
 * Agent任务类型枚举
 */
enum AgentTaskType {
    REGULATION_INTERNALIZATION,  // 外规内化
    POLICY_REVIEW,              // 制度审查
    CONTRACT_REVIEW             // 合同审查
}

/**
 * 任务步骤状态枚举
 */
enum TaskStepStatus {
    PENDING,      // 待执行
    RUNNING,      // 执行中
    COMPLETED,    // 已完成
    FAILED,       // 失败
    SKIPPED       // 已跳过
}

/**
 * 任务优先级枚举
 */
enum TaskPriority {
    LOW,          // 低优先级
    NORMAL,       // 普通优先级
    HIGH,         // 高优先级
    URGENT        // 紧急
}

/**
 * Agent任务实体
 * 记录Agent执行的任务信息
 */
entity AgentTask {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 任务类型 */
    taskType AgentTaskType required,
    /** 任务标题 */
    title String required maxlength(200),
    /** 任务描述 */
    description String maxlength(1000),
    /** 任务状态 */
    status AgentTaskStatus required,
    /** 任务优先级 */
    priority TaskPriority required,
    /** 请求数据 */
    requestData TextBlob,
    /** 响应数据 */
    responseData TextBlob,
    /** 错误信息 */
    errorMessage String maxlength(2000),
    /** 开始时间 */
    startTime Instant,
    /** 结束时间 */
    endTime Instant,
    /** 执行时长(毫秒) */
    executionTime Long,
    /** 进度百分比 */
    progress Integer min(0) max(100),
    /** 扩展元数据 */
    metadata TextBlob,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 任务步骤实体
 * 记录Agent任务的执行步骤
 */
entity TaskStep {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 步骤名称 */
    stepName String required maxlength(100),
    /** 步骤描述 */
    stepDescription String maxlength(500),
    /** 步骤状态 */
    status TaskStepStatus required,
    /** 步骤顺序 */
    stepOrder Integer required,
    /** 输入数据 */
    inputData TextBlob,
    /** 输出数据 */
    outputData TextBlob,
    /** 错误信息 */
    errorMessage String maxlength(1000),
    /** 开始时间 */
    startTime Instant,
    /** 结束时间 */
    endTime Instant,
    /** 执行时长(毫秒) */
    executionTime Long,
    /** 重试次数 */
    retryCount Integer min(0),
    /** 扩展元数据 */
    metadata TextBlob,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * Agent上下文实体
 * 存储Agent执行过程中的上下文信息
 */
entity AgentContext {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 上下文键 */
    contextKey String required maxlength(100),
    /** 上下文值 */
    contextValue TextBlob,
    /** 上下文类型 */
    contextType String maxlength(50),
    /** 过期时间 */
    expireTime Instant,
    /** 扩展元数据 */
    metadata TextBlob,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 知识项缓存实体
 * 缓存从RAG服务检索到的知识项
 */
entity KnowledgeCache {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 知识类型 */
    knowledgeType String required maxlength(50),
    /** 查询关键词 */
    queryKey String required maxlength(200),
    /** 知识内容 */
    content TextBlob required,
    /** 相似度分数 */
    similarityScore Double,
    /** 来源服务 */
    sourceService String maxlength(50),
    /** 缓存过期时间 */
    expireTime Instant required,
    /** 访问次数 */
    accessCount Long min(0),
    /** 最后访问时间 */
    lastAccessTime Instant,
    /** 扩展元数据 */
    metadata TextBlob,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * Agent配置实体
 * 存储Agent的配置参数
 */
entity AgentConfig {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 配置键 */
    configKey String required maxlength(100),
    /** 配置值 */
    configValue String required maxlength(2000),
    /** 配置描述 */
    description String maxlength(500),
    /** 配置分组 */
    configGroup String maxlength(50),
    /** 是否启用 */
    enabled Boolean required,
    /** 扩展元数据 */
    metadata TextBlob,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 实体关系定义
 */
relationship OneToMany {
    /** 一个Agent任务可以有多个执行步骤 */
    AgentTask{taskSteps} to TaskStep{agentTask(title) required},
    
    /** 一个Agent任务可以有多个上下文 */
    AgentTask{contexts} to AgentContext{agentTask(title) required}
}

/**
 * 分页配置
 */
paginate AgentTask, TaskStep, AgentContext, KnowledgeCache, AgentConfig with pagination

/**
 * DTO配置
 */
dto AgentTask, TaskStep, AgentContext, KnowledgeCache, AgentConfig with mapstruct

/**
 * 服务配置
 */
service AgentTask, TaskStep, AgentContext, KnowledgeCache, AgentConfig with serviceImpl

/**
 * 搜索配置
 */
search AgentTask, TaskStep, AgentContext, KnowledgeCache, AgentConfig with no

