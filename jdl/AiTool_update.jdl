/**
 * AI工具状态枚举
 */
enum ToolStatus {
    AVAILABLE,    // 可用
    UNAVAILABLE,  // 不可用
    MAINTENANCE   // 维护中
}


/**
 * AI工具（AiTool）实体
 */
entity AiTool {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 工具名称 */
    name String required maxlength(32),
    /** 工具关键词 */
    toolKey String required maxlength(32),
    /** 工具版本 */
    version String maxlength(32),
    /** API 地址 */
    apiUrl String required maxlength(256),
    /** API 密钥 */
    apiKey String required maxlength(256),
    /** 鉴权类型 */
    authType String maxlength(32),
    /** 接口路径 */
    path String maxlength(128),
    /** 工具状态 */
    status ToolStatus required,
    /** 路由权重 */
    weight Integer required,
    /** 并发许可数 */
    maxConcurrentCalls Integer required,
    /** 是否为模型类型 */
    isModel Boolean,
    /** 模型分类 */
    modelCategory String maxlength(32),
    /** 模型提供商 */
    modelProvider String maxlength(64),
    /** 备注信息 */
    remark String,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * DTO 和服务定义
 */
dto * with mapstruct