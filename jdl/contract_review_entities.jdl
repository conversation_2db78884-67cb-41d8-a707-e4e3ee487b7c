/**
 * 合同智能审查相关实体定义
 * 用于支持合同智能审查功能的数据模型
 */

// 审查状态枚举
enum ReviewStatus {
    PENDING,      // 待审查
    PROCESSING,   // 审查中
    COMPLETED,    // 已完成
    FAILED,       // 审查失败
    CANCELLED     // 已取消
}

// 关联方类型枚举
enum PartyType {
    COMPANY,      // 企业
    INDIVIDUAL,   // 个人
    GOVERNMENT,   // 政府机构
    OTHER         // 其他
}

// 风险等级枚举
enum RiskLevel {
    HIGH,         // 高风险
    MEDIUM,       // 中风险
    LOW           // 低风险
}

// 风险类别枚举
enum RiskCategory {
    LEGAL_COMPLIANCE,    // 法律合规
    BUSINESS_RISK,       // 商业风险
    FINANCIAL_RISK,      // 财务风险
    OPERATIONAL_RISK,    // 操作风险
    REPUTATION_RISK      // 声誉风险
}

/**
 * 合同审查记录实体
 * 存储合同审查的基本信息和结果
 */
entity ContractReview {
    /** 租户ID - 多租户数据隔离 */
    tenantId Long required,

    /** 员工ID */
    employeeId Long required,

    /** 合同类型 */
    contractType String maxlength(64),

    /** 合同标题 */
    contractTitle String maxlength(256),

    /** 合同内容 */
    contractContent TextBlob required,

    /** 审查结果（JSON格式） */
    reviewResult TextBlob,

    /** 审查状态 */
    status ReviewStatus required,

    /** 整体风险等级 */
    overallRiskLevel RiskLevel,

    /** 风险分数 (0-100) */
    riskScore Integer min(0) max(100),

    /** 风险总结 */
    riskSummary TextBlob,

    /** AI调用ID - 关联到ai_request表 */
    aiRequestId Long,

    /** 审查开始时间 */
    reviewStartTime Instant,

    /** 审查完成时间 */
    reviewEndTime Instant,

    /** 审查耗时（毫秒） */
    reviewDuration Long,

    /** 扩展元数据 */
    metadata TextBlob,

    /** 乐观锁版本 */
    version Integer required,

    /** 创建者 */
    createdBy String maxlength(50),

    /** 创建时间 */
    createdAt Instant required,

    /** 更新者 */
    updatedBy String maxlength(50),

    /** 更新时间 */
    updatedAt Instant,

    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 合同关联方实体
 * 存储从合同中提取的关联方信息
 */
entity ContractParty {
    /** 租户ID */
    tenantId Long required,

    /** 关联的审查记录ID */
    reviewId Long required,

    /** 关联方名称 */
    partyName String required maxlength(256),

    /** 关联方类型 */
    partyType PartyType required,

    /** 在合同中的角色 */
    partyRole String maxlength(64),

    /** 统一社会信用代码（企业） */
    creditCode String maxlength(32),

    /** 注册地址 */
    registeredAddress String maxlength(512),

    /** 法定代表人（企业） */
    legalRepresentative String maxlength(64),

    /** 联系方式 */
    contactInfo String maxlength(256),

    /** 风险等级 */
    riskLevel RiskLevel,

    /** 风险因素（JSON数组） */
    riskFactors TextBlob,

    /** 合规问题（JSON数组） */
    complianceIssues TextBlob,

    /** 天眼查信息（JSON格式） */
    tianyanchaInfo TextBlob,

    /** 扩展信息 */
    additionalInfo TextBlob,

    /** 乐观锁版本 */
    version Integer required,

    /** 创建时间 */
    createdAt Instant required,

    /** 更新时间 */
    updatedAt Instant,

    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 风险点实体
 * 存储合同审查中识别的具体风险点
 */
entity ContractRiskPoint {
    /** 租户ID */
    tenantId Long required,

    /** 关联的审查记录ID */
    reviewId Long required,

    /** 风险类别 */
    riskCategory RiskCategory required,

    /** 风险描述 */
    riskDescription TextBlob required,

    /** 风险等级 */
    severity RiskLevel required,

    /** 涉及的条款 */
    affectedClauses TextBlob,

    /** 法律依据 */
    legalBasis TextBlob,

    /** 修改建议 */
    suggestions TextBlob,

    /** 风险分数 (0-100) */
    riskScore Integer min(0) max(100),

    /** 是否为关键风险 */
    isCritical Boolean,

    /** 风险来源 */
    riskSource String maxlength(128),

    /** 乐观锁版本 */
    version Integer required,

    /** 创建时间 */
    createdAt Instant required,

    /** 更新时间 */
    updatedAt Instant,

    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 条款问题实体
 * 存储合同条款中发现的具体问题
 */
entity ContractClauseIssue {
    /** 租户ID */
    tenantId Long required,

    /** 关联的审查记录ID */
    reviewId Long required,

    /** 条款内容 */
    clauseText TextBlob required,

    /** 条款编号/位置 */
    clauseNumber String maxlength(32),

    /** 问题类型 */
    issueType String maxlength(64),

    /** 问题描述 */
    issueDescription TextBlob required,

    /** 严重程度 */
    severity RiskLevel required,

    /** 法律风险说明 */
    legalRisk TextBlob,

    /** 修改建议 */
    suggestions TextBlob,

    /** 参考法规 */
    referenceLaws TextBlob,

    /** 乐观锁版本 */
    version Integer required,

    /** 创建时间 */
    createdAt Instant required,

    /** 更新时间 */
    updatedAt Instant,

    /** 软删除标志 */
    isDeleted Boolean required
}

// 实体关系定义
relationship OneToMany {
    ContractReview to ContractParty{review},
    ContractReview to ContractRiskPoint{review},
    ContractReview to ContractClauseIssue{review}
}

/**
 * DTO 和服务定义
 */
dto * with mapstruct
service all with serviceImpl
paginate * with pagination
