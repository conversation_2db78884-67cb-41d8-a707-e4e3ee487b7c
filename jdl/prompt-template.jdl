/**
 * WhiskerGuard AI Service - 提示词模板管理 JDL 定义
 *
 * 用于定义 Prompt 提示词模板管理相关的实体和关系
 * 支持多租户、版本控制、变量管理等功能
 */

/**
 * 提示词模板类型枚举
 */
enum PromptTemplateType {
    CONTRACT_REVIEW,                // 合同审查
    CONTRACT_COMPREHENSIVE_REVIEW,  // 合同综合审查
    CONTRACT_LEGAL_COMPLIANCE,      // 合同法律合规
    CONTRACT_RISK_ASSESSMENT,       // 合同风险评估
    CONTRACT_FINANCIAL_TERMS,       // 合同财务条款
    POLICY_REVIEW,                  // 制度审查
    POLICY_REGULATORY_CONVERSION,   // 法规转制度
    POLICY_INTERNAL_REVIEW,         // 内部制度审查
    POLICY_COMPLIANCE_CHECK,        // 合规检查
    GENERAL_ANALYSIS,               // 通用分析
    GENERAL_GENERATION,             // 通用生成
    GENERAL_EXTRACTION              // 通用提取
}

/**
 * 提示词模板状态枚举
 */
enum PromptTemplateStatus {
    DRAFT,      // 草稿
    TESTING,    // 测试中
    PUBLISHED,  // 已发布
    DISABLED,   // 已停用
    ARCHIVED    // 已归档
}

/**
 * 变量类型枚举
 */
enum VariableType {
    SYSTEM,     // 系统变量
    BUSINESS,   // 业务变量
    RAG,        // RAG变量
    CUSTOM,     // 自定义变量
    COMPUTED,   // 计算变量
    EXTERNAL    // 外部变量
}

/**
 * 提示词模板（PromptTemplate）实体
 * 存储提示词模板的基本信息和内容
 */
entity PromptTemplate {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long,
    /** 模板唯一标识键 */
    templateKey String required maxlength(100),
    /** 模板名称 */
    name String required maxlength(200),
    /** 模板描述 */
    description String maxlength(1000),
    /** 模板类型 */
    templateType PromptTemplateType required,
    /** 模板内容 */
    content TextBlob required,
    /** 模板状态 */
    status PromptTemplateStatus required,
    /** 版本号 */
    templateVersion Integer required,
    /** 是否为系统默认模板 */
    isSystemDefault Boolean required,
    /** 创建者ID */
    createdById Long,
    /** 最后修改者ID */
    lastModifiedById Long,
    /** 使用次数统计 */
    usageCount Long,
    /** 最后使用时间 */
    lastUsedAt Instant,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 提示词模板变量（PromptTemplateVariable）实体
 * 定义模板中使用的变量信息
 */
entity PromptTemplateVariable {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 变量名称 */
    variableName String required maxlength(100),
    /** 变量显示名称 */
    displayName String required maxlength(200),
    /** 变量描述 */
    description String maxlength(500),
    /** 变量类型 */
    variableType VariableType required,
    /** 默认值 */
    defaultValue TextBlob,
    /** 是否必填 */
    isRequired Boolean required,
    /** 变量验证规则 */
    validationRule String maxlength(500),
    /** 变量示例值 */
    exampleValue TextBlob,
    /** 排序顺序 */
    sortOrder Integer,
    /** 是否启用 */
    isEnabled Boolean required,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 提示词模板版本（PromptTemplateVersion）实体
 * 管理模板的版本历史
 */
entity PromptTemplateVersion {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 版本号 */
    versionNumber Integer required,
    /** 版本名称 */
    versionName String maxlength(200),
    /** 版本描述 */
    description String maxlength(1000),
    /** 模板内容 */
    content TextBlob required,
    /** 变量定义JSON */
    variablesDefinition TextBlob,
    /** 版本状态 */
    status PromptTemplateStatus required,
    /** 是否为当前活跃版本 */
    isActive Boolean required,
    /** 创建者ID */
    createdById Long,
    /** 版本变更说明 */
    changeLog String maxlength(1000),
    /** 使用次数统计 */
    usageCount Long,
    /** 最后使用时间 */
    lastUsedAt Instant,
    /** 性能评分 */
    performanceScore Double,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 租户提示词配置（TenantPromptConfig）实体
 * 存储租户级别的个性化配置
 */
entity TenantPromptConfig {
    /** 主键 ID */
    id Long,
    /** 租户ID */
    tenantId Long required,
    /** 配置键 */
    configKey String required maxlength(100),
    /** 配置值 */
    configValue TextBlob,
    /** 配置类型 */
    configType String required maxlength(50),
    /** 配置描述 */
    description String maxlength(500),
    /** 是否启用 */
    isEnabled Boolean required,
    /** 优先级 */
    priority Integer,
    /** 生效时间 */
    effectiveFrom Instant,
    /** 失效时间 */
    effectiveTo Instant,
    /** 创建者ID */
    createdById Long,
    /** 最后修改者ID */
    lastModifiedById Long,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 实体关系定义
 */
relationship OneToMany {
    PromptTemplate to PromptTemplateVariable{promptTemplate},
    PromptTemplate to PromptTemplateVersion{promptTemplate}
}

/**
 * DTO 和服务定义
 */
dto * with mapstruct
service all with serviceImpl
paginate * with pagination
